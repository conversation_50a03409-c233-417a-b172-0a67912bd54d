# U盘升级系统使用说明

## 概述

本系统实现了基于U盘的车载系统升级功能，支持自动检测U盘插入、分析升级包、拷贝文件到指定目录，并通过S2S服务将升级任务分发到车内各域控。

## 系统架构

### 通信架构

```
升级服务 → S2S服务 → 其他域控
   ↑         ↑         ↑
  AIDL     DDS      DDS订阅
```

1. **升级服务与S2S通信**：使用AIDL接口，数据格式为JSON字符串
2. **S2S与其他域控通信**：S2S负责将升级任务通过DDS转发到各域控
3. **其他域控接收任务**：通过DDS订阅接收升级任务和状态更新

### 核心组件

1. **UsbDetectionService** - U盘检测服务
   - 监听U盘插入/拔出事件
   - 自动启动升级包分析

2. **UpgradePackageAnalyzer** - 升级包分析器
   - 扫描U盘中的升级文件
   - 解析配置文件
   - 生成升级任务信息

3. **UpgradeTaskManager** - 升级任务管理器
   - 处理升级任务
   - 拷贝文件到目标目录
   - 管理任务状态

4. **UpgradeTaskPublisher** - 任务发布者
   - 通过AIDL与S2S服务通信
   - 发送升级任务和状态更新

5. **UpgradeTaskHandler** - S2S任务处理器
   - 在S2S服务中处理升级任务
   - 通过DDS转发任务到各域控

6. **UpgradeTaskSubscriber** - DDS订阅者
   - 订阅DDS升级任务
   - 接收S2S转发的任务

7. **UpgradeTaskClient** - 升级任务客户端
   - 其他域控使用的客户端
   - 通过AIDL与S2S通信接收任务

## 使用方法

### 1. 准备升级包

在U盘根目录或子目录中放置以下文件：

#### 升级文件
支持的文件格式：
- `.bin` - 固件文件
- `.hex` - 固件文件
- `.img` - 镜像文件
- `.tar` - 压缩包
- `.zip` - 压缩包
- `.apk` - Android应用
- `.so` - 动态库
- `.conf` - 配置文件
- `.json` - JSON配置
- `.xml` - XML配置

#### 配置文件（可选）
在U盘根目录创建 `upgrade_config.json` 文件，格式如下：

```json
{
  "taskName": "车载系统升级包 v2.1.0",
  "version": "2.1.0",
  "description": "包含HPCM、ZCUF等域控的固件升级包",
  "packages": [
    {
      "fileName": "hpcm_firmware_v2.1.0.bin",
      "name": "HPCM固件升级包",
      "version": "2.1.0",
      "targetEcu": "HPCM",
      "type": "firmware",
      "required": true
    }
  ]
}
```

### 2. 插入U盘

1. 将准备好的U盘插入车机USB接口
2. 系统会自动检测到U盘插入事件
3. 开始分析升级包并生成任务

### 3. 查看升级任务

在主界面点击相关按钮：
- "启动升级任务订阅者" - 开始监听DDS升级任务（用于测试）
- "启动升级任务客户端" - 启动域控客户端（模拟其他域控）
- "显示活跃升级任务" - 查看当前活跃的升级任务
- "停止升级任务订阅者" - 停止DDS监听
- "停止升级任务客户端" - 停止客户端

### 4. 升级流程

1. **检测阶段**：检测到U盘插入
2. **分析阶段**：扫描升级文件，解析配置
3. **拷贝阶段**：将文件拷贝到 `/data/upgrade/TASK_ID/` 目录
4. **发布阶段**：通过AIDL将任务发送到S2S服务
5. **转发阶段**：S2S服务通过DDS转发任务到各域控
6. **接收阶段**：各域控通过DDS订阅或AIDL客户端接收任务
7. **执行阶段**：各域控执行升级并报告结果

## 任务状态

- `PENDING` - 待处理
- `COPYING` - 拷贝中
- `READY` - 准备就绪
- `DISTRIBUTING` - 分发中
- `COMPLETED` - 已完成
- `FAILED` - 失败
- `CANCELLED` - 已取消

## 目标ECU类型

系统支持以下ECU类型：
- `HPCM` - 座椅控制模块
- `ZCUF` - 前排ZCU
- `ZCUFR` - 前右ZCU
- `ZCUR` - 后排ZCU
- `BCM` - 车身控制模块
- `TBOX` - 远程信息处理盒
- `ANDROID` - Android系统

## 文件命名规范

为了更好的自动识别，建议按以下规范命名：

- 固件文件：`{ecu_name}_firmware_v{version}.{ext}`
- 配置文件：`{ecu_name}_config_v{version}.{ext}`
- 应用文件：`{app_name}_v{version}.apk`

例如：
- `hpcm_firmware_v2.1.0.bin`
- `zcuf_config_v2.1.0.conf`
- `system_app_v2.1.0.apk`

## 权限要求

系统需要以下权限：
- `READ_EXTERNAL_STORAGE` - 读取U盘文件
- `WRITE_EXTERNAL_STORAGE` - 写入目标目录
- `MANAGE_EXTERNAL_STORAGE` - 管理外部存储
- `MOUNT_UNMOUNT_FILESYSTEMS` - 挂载文件系统

## 日志查看

使用以下标签查看相关日志：
- `UsbDetectionService` - U盘检测
- `UpgradePackageAnalyzer` - 包分析
- `UpgradeTaskManager` - 任务管理
- `UpgradeTaskPublisher` - 任务发布（AIDL通信）
- `UpgradeTaskHandler` - S2S任务处理
- `UpgradeTaskSubscriber` - DDS任务订阅
- `UpgradeTaskClient` - 域控客户端

## 故障排除

### 常见问题

1. **U盘检测不到**
   - 检查U盘格式（建议FAT32或exFAT）
   - 确认USB接口正常
   - 查看权限是否正确

2. **升级包识别失败**
   - 检查文件扩展名是否支持
   - 确认配置文件格式正确
   - 查看文件权限

3. **任务分发失败**
   - 检查DDS服务是否正常
   - 确认网络连接
   - 查看订阅者是否在线

### 调试模式

在MainActivity中可以：
- 手动启动/停止订阅者
- 查看活跃任务列表
- 监控任务状态变化

## 扩展开发

### 添加新的文件类型支持

在 `UpgradePackageAnalyzer.kt` 中修改 `supportedExtensions` 集合。

### 自定义目标路径

在 `UpgradePackageAnalyzer.kt` 中修改 `getTargetPath()` 方法。

### 添加新的ECU类型

在 `guessTargetEcu()` 方法中添加新的识别规则。

## 安全注意事项

1. 升级包应进行数字签名验证
2. 文件传输过程中进行完整性校验
3. 升级过程中应有回滚机制
4. 关键系统升级需要用户确认

## 性能优化

1. 大文件拷贝使用异步处理
2. 支持断点续传
3. 压缩传输减少网络负载
4. 批量处理提高效率

package seres.zcuf
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class ZCUF_Control_Call: TypeUnion(){
    private var _d : ZCUF_Control_descriminator = ZCUF_Control_descriminator.NONE
    inner class union{
        private var BCM_CommonLight_Hood_CtrlIn: ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In = ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In()
        fun initialize(){
            when(_d){
                ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d -> _u_prop.change_prop_param("BCM_CommonLight_Hood_CtrlIn", PropType.STRUCT, 4u)
                else -> println("Data initialize error")
            }
        }
        fun serialize(buffer: Buffer){
            when(_d){
                ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d -> BCM_CommonLight_Hood_CtrlIn.serialize(buffer)
                else -> println("Data serialize error")
            }
        }

        fun deserialize(buffer: Buffer){
            when(_d){
                ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d -> BCM_CommonLight_Hood_CtrlIn.deserialize(buffer)
                else -> println("Data deserialize error")
            }
        }

        fun get_value():Any{
            return when(_d){
                ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d -> BCM_CommonLight_Hood_CtrlIn
                else -> println("Data get_value error")
            }
        }

        fun set_value(value: Any){
            return when(_d){
                ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d -> BCM_CommonLight_Hood_CtrlIn = value as ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In
                else -> println("Data get_value error")
            }
        }

    }
    private var _u: union = union()

    init{
        typename = "kotlinidl:ZCUF_Control_Call"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUF_Control_Call", 0u)
        prop!!.create_member_prop(Prop("_d",  1u, PropType.ENUM, 4u, "Int"))
        prop!!.create_member_prop(_u_prop)
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, _d.value)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUF_Control_Call{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._d = ZCUF_Control_descriminator.fromValue(res as Int)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.deserialize(buffer)
            }
        }
        return this
    }

    fun copy(value: ZCUF_Control_Call){
        this._d = value.get__d()
        this._u.set_value(value.get__u())
    }

    fun get__d():ZCUF_Control_descriminator{
        return this._d
    }

    fun set__d(value: ZCUF_Control_descriminator){
        this._d = value
        this._u.initialize()
    }

    fun get__u():Any{
        return this._u.get_value()
    }

    fun set__u(value: Any){
        this._u.set_value(value)
    }

}
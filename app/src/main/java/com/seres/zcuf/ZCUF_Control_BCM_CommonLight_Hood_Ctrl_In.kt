package seres.zcuf

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In : TypeStruct(){
    private var onOffCmd : seres.zcuf.OnOffCmd = seres.zcuf.OnOffCmd.NO_REQUEST

    init{
        typename = "seres::zcuf::ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("onOffCmd", 1u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, onOffCmd.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.onOffCmd = seres.zcuf.OnOffCmd.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In){
        onOffCmd = value.onOffCmd
    }

    fun get_onOffCmd(): seres.zcuf.OnOffCmd{
        return onOffCmd
    }

    fun set_onOffCmd(value: seres.zcuf.OnOffCmd){
        onOffCmd = value
    }
}
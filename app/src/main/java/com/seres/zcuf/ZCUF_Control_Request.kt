package seres.zcuf

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.RequestHeader


class ZCUF_Control_Request : TypeStruct(){
    private var header : RequestHeader = RequestHeader()
    private var data : ZCUF_Control_Call = ZCUF_Control_Call()

    init{
        typename = "seres::zcuf::ZCUF_Control_Request"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUF_Control_Request", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("header", 1u, PropType.STRUCT, 4u))
        prop!!.create_member_prop(Prop("data", 2u, PropType.UNION, 4u))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                header.serialize(buffer)
            }
               if(subprop.m_id == 2.toUInt()){
                data.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUF_Control_Request{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                this.header = header.deserialize(buffer)
              }
            if(subprop.m_id == 2.toUInt()){
                this.data = data.deserialize(buffer)
              }
        }
        return this
    }

    fun copy(value: ZCUF_Control_Request){
        header.copy(value.header)
        data.copy(value.data)
    }

    fun get_header(): RequestHeader{
        return header
    }

    fun set_header(value: RequestHeader){
        header.copy(value)
    }

    fun get_data(): ZCUF_Control_Call{
        return data
    }

    fun set_data(value: ZCUF_Control_Call){
        data.copy(value)
    }
}
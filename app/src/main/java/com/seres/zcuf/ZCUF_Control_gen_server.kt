package seres.zcuf



import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.*


abstract class ZCUF_Control_base {
    abstract fun BCM_CommonLight_Hood_Ctrl(onOffCmd: seres.zcuf.OnOffCmd): ReturnCode
}

class ZCUF_Control_dispatcher(requestType: TypeBase, replyType: TypeBase): Dispatcher<ZCUF_Control_base>(requestType, replyType) {
    lateinit var serviceImpl: ZCUF_Control_base
    lateinit var replier: Replier
  
    override fun process(request: TypeBase?) {
        var data: Any? = null
        var desc: ZCUF_Control_descriminator = ZCUF_Control_descriminator.NONE
        var sampleIdentity : SampleIdentity = SampleIdentity()
        desc = (request as ZCUF_Control_Request).get_data().get__d()
        sampleIdentity = (request as ZCUF_Control_Request).get_header().get_requestId()
        var result = ZCUF_Control_Reply()
        var header = ReplyHeader()
        header.set_relatedRequestId(sampleIdentity)
        result.set_header(header)
        when (desc.value) {
            ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d.value -> {
                val inData = (request as ZCUF_Control_Request).get_data().get__u() as ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In
                val onOffCmd = inData.get_onOffCmd()

                val retcode = serviceImpl.BCM_CommonLight_Hood_Ctrl(onOffCmd)

                var outData = ZCUF_Control_BCM_CommonLight_Hood_Ctrl_Result()
                outData.set__return(retcode)

                result.get_data().set__d(ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }
        }
    }

    override fun add_service_impl(serviceImpl: ZCUF_Control_base) {
        this.serviceImpl = serviceImpl
    }  

    override fun set_replier(replier: Replier) {
        this.replier = replier
    }  
}

class ZCUF_ControlService(
    serviceImpl: ZCUF_Control_base,
    server: Server,
    param: ServiceParam,
    dispatcher: Dispatcher<ZCUF_Control_base> = ZCUF_Control_dispatcher(ZCUF_Control_Request(), ZCUF_Control_Reply())
    ) : ServiceEndpoint<ZCUF_Control_base>(serviceImpl, param, server, dispatcher) {
}

package seres.zcuf

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class ZCUF_Status : TypeStruct(){
    private var commonlight_hood : Int = 0

    init{
        typename = "seres::zcuf::ZCUF_Status"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUF_Status", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("commonlight_hood", 1u, PropType.PRIMITIVE, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, commonlight_hood)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUF_Status{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.commonlight_hood = res as Int
            }
        }
        return this
    }

    fun copy(value: ZCUF_Status){
        commonlight_hood = value.commonlight_hood
    }

    fun get_commonlight_hood(): Int{
        return commonlight_hood
    }

    fun set_commonlight_hood(value: Int){
        commonlight_hood = value
    }
}
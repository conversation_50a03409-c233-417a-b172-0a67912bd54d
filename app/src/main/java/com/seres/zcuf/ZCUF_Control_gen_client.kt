package seres.zcuf



import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.*


class ZCUF_ControlClient : ClientEndpoint {
    constructor(
        param: ClientParam,
        rpcRequest: TypeBase = ZCUF_Control_Request(),
        rpcReply: TypeBase = ZCUF_Control_Reply()
    ) : super(param, rpcRequest, rpcReply)
    fun BCM_CommonLight_Hood_Ctrl(onOffCmd: seres.zcuf.OnOffCmd): ReturnCode{
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = ZCUF_Control_Request()
        request.set_header(header)
        val call = ZCUF_Control_Call()
        val request_data = ZCUF_Control_BCM_CommonLight_Hood_Ctrl_In()
        request_data.set_onOffCmd(onOffCmd as seres.zcuf.OnOffCmd)

        call.set__d(ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as ZCUF_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == ZCUF_Control_descriminator.ZCUF_Control_BCM_CommonLight_Hood_Ctrl_d) {
                var return_val = reply.get_data().get__u() as ZCUF_Control_BCM_CommonLight_Hood_Ctrl_Result
                return return_val.get__return()
            } else {
                throw Exception("[ZCUF_ControlClient]--> call BCM_CommonLight_Hood_Ctrl unkown _d() value")
            }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }
}

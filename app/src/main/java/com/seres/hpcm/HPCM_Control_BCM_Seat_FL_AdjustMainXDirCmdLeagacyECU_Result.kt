package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result : TypeStruct(){
    private var BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut : HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out = HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out()
    private var _return : ReturnCode = ReturnCode.NONE

    init{
        typename = "seres::hpcm::HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut", 1u, PropType.STRUCT, 4u,))
        prop!!.create_member_prop(Prop("_return", 2u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut.serialize(buffer)
            }
               if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, _return.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                this.BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut = BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut.deserialize(buffer)
              }
            if(subprop.m_id == 2.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._return = ReturnCode.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result){
        BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut.copy(value.BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut)
        _return = value._return
    }

    fun get_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut(): HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out {
        return BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut
    }

    fun set_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut(value: HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out){
        BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUOut.copy(value)
    }

    fun get__return(): ReturnCode {
        return _return
    }

    fun set__return(value: ReturnCode){
        _return = value
    }
}
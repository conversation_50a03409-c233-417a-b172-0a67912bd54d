package seres.hpcm

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCM_Control_Call: TypeUnion(){
    private var _d : HPCM_Control_descriminator = HPCM_Control_descriminator.NONE
    inner class union{
        private var BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUIn: HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_In = HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_In()
        private var BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn: HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In = HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In()
        private var BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn: HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In = HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In()
        private var BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUIn: HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In = HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In()
        private var BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUIn: HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_In = HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_In()
        private var BCM_CushionTemp_FL_HeatCtrlLeagacyECUIn: HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_In = HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_In()
        fun initialize(){
            when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> _u_prop.change_prop_param("BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUIn", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> _u_prop.change_prop_param("BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUIn", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUIn", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FL_HeatCtrlLeagacyECUIn", PropType.STRUCT, 4u)
                else -> println("Data initialize error")
            }
        }
        fun serialize(buffer: Buffer){
            when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUIn.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUIn.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUIn.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUIn.serialize(buffer)
                else -> println("Data serialize error")
            }
        }

        fun deserialize(buffer: Buffer){
            when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUIn.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUIn.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUIn.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUIn.deserialize(buffer)
                else -> println("Data deserialize error")
            }
        }

        fun get_value():Any{
            return when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUIn
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUIn
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUIn
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUIn
                else -> println("Data get_value error")
            }
        }

        fun set_value(value: Any){
            return when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUIn = value as HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_In
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn = value as HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn = value as HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUIn = value as HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUIn = value as HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_In
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUIn = value as HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_In
                else -> println("Data get_value error")
            }
        }

    }
    private var _u: union = union()

    init{
        typename = "kotlinidl:HPCM_Control_Call"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_Call", 0u)
        prop!!.create_member_prop(Prop("_d",  1u, PropType.ENUM, 4u, "Int"))
        prop!!.create_member_prop(_u_prop)
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, _d.value)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_Call {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._d = HPCM_Control_descriminator.fromValue(res as Int)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.deserialize(buffer)
            }
        }
        return this
    }


    fun copy(value: HPCM_Control_Call){
        this._d = value.get__d()
        this._u.set_value(value.get__u())
    }

    fun get__d(): HPCM_Control_descriminator {
        return this._d
    }

    fun set__d(value: HPCM_Control_descriminator){
        this._d = value
        this._u.initialize()
    }

    fun get__u():Any{
        return this._u.get_value()
    }

    fun set__u(value: Any){
        this._u.set_value(value)
    }

}
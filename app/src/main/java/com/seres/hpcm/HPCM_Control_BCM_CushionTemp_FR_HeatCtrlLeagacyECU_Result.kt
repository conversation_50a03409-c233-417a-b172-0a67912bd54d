package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result : TypeStruct(){
    private var BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut : HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Out = HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Out()
    private var _return : ReturnCode = ReturnCode.NONE

    init{
        typename = "seres::hpcm::HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut", 1u, PropType.STRUCT, 4u))
        prop!!.create_member_prop(Prop("_return", 2u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut.serialize(buffer)
            }
               if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, _return.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                this.BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut = BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut.deserialize(buffer)
              }
            if(subprop.m_id == 2.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._return = ReturnCode.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result){
        BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut.copy(value.BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut)
        _return = value._return
    }

    fun get_BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut(): HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Out {
        return BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut
    }

    fun set_BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut(value: HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Out){
        BCM_CushionTemp_FR_HeatCtrlLeagacyECUOut.copy(value)
    }

    fun get__return(): ReturnCode {
        return _return
    }

    fun set__return(value: ReturnCode){
        _return = value
    }
}
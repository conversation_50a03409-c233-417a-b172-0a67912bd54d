package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In : TypeStruct(){
    private var bcm_Legacy_SeatMainXDir : Int = 0

    init{
        typename = "seres::hpcm::HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("bcm_Legacy_SeatMainXDir", 1u, PropType.PRIMITIVE, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, bcm_Legacy_SeatMainXDir)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.bcm_Legacy_SeatMainXDir = res as Int
            }
        }
        return this
    }

    fun copy(value: HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In){
        bcm_Legacy_SeatMainXDir = value.bcm_Legacy_SeatMainXDir
    }

    fun get_bcm_Legacy_SeatMainXDir(): Int{
        return bcm_Legacy_SeatMainXDir
    }

    fun set_bcm_Legacy_SeatMainXDir(value: Int){
        bcm_Legacy_SeatMainXDir = value
    }
}
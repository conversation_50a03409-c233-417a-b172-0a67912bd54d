package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.ReplyHeader

class HPCM_Control_Reply : TypeStruct(){
    private var header : ReplyHeader = ReplyHeader()
    private var data : HPCM_Control_Return = HPCM_Control_Return()

    init{
        typename = "seres::hpcm::HPCM_Control_Reply"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_Reply", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("header", 1u, PropType.STRUCT, 4u))
        prop!!.create_member_prop(Prop("data", 2u, PropType.UNION, 4u))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                header.serialize(buffer)
            }
               if(subprop.m_id == 2.toUInt()){
                data.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_Reply {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                this.header = header.deserialize(buffer)
              }
            if(subprop.m_id == 2.toUInt()){
                this.data = data.deserialize(buffer)
              }
        }
        return this
    }

    fun copy(value: HPCM_Control_Reply){
        header.copy(value.header)
        data.copy(value.data)
    }

    fun get_header(): ReplyHeader{
        return header
    }

    fun set_header(value: ReplyHeader){
        header.copy(value)
    }

    fun get_data(): HPCM_Control_Return {
        return data
    }

    fun set_data(value: HPCM_Control_Return){
        data.copy(value)
    }
}
package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out : TypeStruct(){
    private var _default : Byte = 0

    init{
        typename = "seres::hpcm::HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("_default", 1u, PropType.PRIMITIVE, 4u, "Byte"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, _default)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._default = res as Byte
            }
        }
        return this
    }

    fun copy(value: HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Out){
        _default = value._default
    }

    fun get__default(): Byte{
        return _default
    }

    fun set__default(value: Byte){
        _default = value
    }
}
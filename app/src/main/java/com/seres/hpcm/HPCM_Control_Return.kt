package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

enum class HPCM_Control_descriminator(val value: Int){
    NONE(0),
    HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d(
        HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_HASH
    ),
    HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d(
        HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_HASH
    ),
    HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d(
        HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_HASH
    ),
    HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d(
        HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_HASH
    ),
    HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d(
        HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_HASH
    ),
    HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d(
        HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_HASH
    );
    companion object {
        private val valueMap = HPCM_Control_descriminator.entries.associateBy { it.value }
        fun fromValue(value: Int): HPCM_Control_descriminator {
            return  valueMap[value]?: NONE
        }
    }

}

class HPCM_Control_Return: TypeUnion(){
    private var _d : HPCM_Control_descriminator = HPCM_Control_descriminator.NONE
    inner class union{
        private var BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUResult: HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_Result = HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_Result()
        private var BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUResult: HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result = HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result()
        private var BCM_CushionTemp_FR_HeatCtrlLeagacyECUResult: HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result = HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result()
        private var BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUResult: HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result = HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result()
        private var BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUResult: HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_Result = HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_Result()
        private var BCM_CushionTemp_FL_HeatCtrlLeagacyECUResult: HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_Result = HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_Result()
        fun initialize(){
            when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> _u_prop.change_prop_param("BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUResult", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUResult", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FR_HeatCtrlLeagacyECUResult", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> _u_prop.change_prop_param("BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUResult", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUResult", PropType.STRUCT, 4u)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> _u_prop.change_prop_param("BCM_CushionTemp_FL_HeatCtrlLeagacyECUResult", PropType.STRUCT, 4u)
                else -> println("Data initialize error")
            }
        }
        fun serialize(buffer: Buffer){
            when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUResult.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUResult.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUResult.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUResult.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUResult.serialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUResult.serialize(buffer)
                else -> println("Data serialize error")
            }
        }

        fun deserialize(buffer: Buffer){
            when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUResult.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUResult.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUResult.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUResult.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUResult.deserialize(buffer)
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUResult.deserialize(buffer)
                else -> println("Data deserialize error")
            }
        }

        fun get_value():Any{
            return when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUResult
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUResult
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUResult
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUResult
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUResult
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUResult
                else -> println("Data get_value error")
            }
        }

        fun set_value(value: Any){
            return when(_d){
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FR_AdjustMainXDirCmdLeagacyECUResult = value as HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_Result
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUResult = value as HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUResult = value as HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result
                HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d -> BCM_Seat_FL_AdjustMainXDirCmdLeagacyECUResult = value as HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECUResult = value as HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_Result
                HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FL_HeatCtrlLeagacyECUResult = value as HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_Result
                else -> println("Data get_value error")
            }
        }

    }
    private var _u: union = union()

    init{
        typename = "kotlinidl:HPCM_Control_Return"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_Return", 0u)
        prop!!.create_member_prop(Prop("_d",  1u, PropType.ENUM, 4u, "Int"))
        prop!!.create_member_prop(_u_prop)
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, _d.value)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_Return {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._d = HPCM_Control_descriminator.fromValue(res as Int)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.deserialize(buffer)
            }
        }
        return this
    }

    fun copy(value: HPCM_Control_Return){
        this._d = value.get__d()
        this._u.set_value(value.get__u())
    }

    fun get__d(): HPCM_Control_descriminator {
        return this._d
    }

    fun set__d(value: HPCM_Control_descriminator){
        this._d = value
        this._u.initialize()
    }

    fun get__u():Any{
        return this._u.get_value()
    }

    fun set__u(value: Any){
        this._u.set_value(value)
    }

}
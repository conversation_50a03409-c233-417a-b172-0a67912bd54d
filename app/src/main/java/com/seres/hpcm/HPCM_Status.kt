package seres.hpcm

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCM_Status : TypeStruct(){
    private var heatingStatusFL : Boolean = false
    private var heatingLevelFL : Int = 0
    private var ventilatingStatusFL : Boolean = false
    private var heatingStatusFR : Boolean = false
    private var ventilatingStatusFR : Boolean = false
    private var heatingLevelFR : Int = 0
    private var mainXDirFL : Int = 0
    private var mainXDirFR : Int = 0
    private var ventilationFL : Int = 0
    private var ventilationFR : Int = 0
    private var seat_FL_XActuateStatus : Int = 0
    private var seat_FR_XActuateStatus : Int = 0

    init{
        typename = "seres::hpcm::HPCM_Status"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Status", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("heatingStatusFL", 1u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("heatingLevelFL", 2u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("ventilatingStatusFL", 3u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("heatingStatusFR", 4u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("ventilatingStatusFR", 5u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("heatingLevelFR", 6u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("mainXDirFL", 7u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("mainXDirFR", 8u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("ventilationFL", 9u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("ventilationFR", 10u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("seat_FL_XActuateStatus", 11u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("seat_FR_XActuateStatus", 12u, PropType.PRIMITIVE, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, heatingStatusFL)
            }
            if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, heatingLevelFL)
            }
            if(subprop.m_id == 3.toUInt()){
                subprop.machine!!.serialize(buffer, ventilatingStatusFL)
            }
            if(subprop.m_id == 4.toUInt()){
                subprop.machine!!.serialize(buffer, heatingStatusFR)
            }
            if(subprop.m_id == 5.toUInt()){
                subprop.machine!!.serialize(buffer, ventilatingStatusFR)
            }
            if(subprop.m_id == 6.toUInt()){
                subprop.machine!!.serialize(buffer, heatingLevelFR)
            }
            if(subprop.m_id == 7.toUInt()){
                subprop.machine!!.serialize(buffer, mainXDirFL)
            }
            if(subprop.m_id == 8.toUInt()){
                subprop.machine!!.serialize(buffer, mainXDirFR)
            }
            if(subprop.m_id == 9.toUInt()){
                subprop.machine!!.serialize(buffer, ventilationFL)
            }
            if(subprop.m_id == 10.toUInt()){
                subprop.machine!!.serialize(buffer, ventilationFR)
            }
            if(subprop.m_id == 11.toUInt()){
                subprop.machine!!.serialize(buffer, seat_FL_XActuateStatus)
            }
            if(subprop.m_id == 12.toUInt()){
                subprop.machine!!.serialize(buffer, seat_FR_XActuateStatus)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Status{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingStatusFL = res as Boolean
            }
            if(subprop.m_id == 2.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingLevelFL = res as Int
            }
            if(subprop.m_id == 3.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.ventilatingStatusFL = res as Boolean
            }
            if(subprop.m_id == 4.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingStatusFR = res as Boolean
            }
            if(subprop.m_id == 5.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.ventilatingStatusFR = res as Boolean
            }
            if(subprop.m_id == 6.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingLevelFR = res as Int
            }
            if(subprop.m_id == 7.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.mainXDirFL = res as Int
            }
            if(subprop.m_id == 8.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.mainXDirFR = res as Int
            }
            if(subprop.m_id == 9.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.ventilationFL = res as Int
            }
            if(subprop.m_id == 10.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.ventilationFR = res as Int
            }
            if(subprop.m_id == 11.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.seat_FL_XActuateStatus = res as Int
            }
            if(subprop.m_id == 12.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.seat_FR_XActuateStatus = res as Int
            }
        }
        return this
    }

    fun copy(value: HPCM_Status){
        heatingStatusFL = value.heatingStatusFL
        heatingLevelFL = value.heatingLevelFL
        ventilatingStatusFL = value.ventilatingStatusFL
        heatingStatusFR = value.heatingStatusFR
        ventilatingStatusFR = value.ventilatingStatusFR
        heatingLevelFR = value.heatingLevelFR
        mainXDirFL = value.mainXDirFL
        mainXDirFR = value.mainXDirFR
        ventilationFL = value.ventilationFL
        ventilationFR = value.ventilationFR
        seat_FL_XActuateStatus = value.seat_FL_XActuateStatus
        seat_FR_XActuateStatus = value.seat_FR_XActuateStatus
    }

    fun get_heatingStatusFL(): Boolean{
        return heatingStatusFL
    }

    fun set_heatingStatusFL(value: Boolean){
        heatingStatusFL = value
    }

    fun get_heatingLevelFL(): Int{
        return heatingLevelFL
    }

    fun set_heatingLevelFL(value: Int){
        heatingLevelFL = value
    }

    fun get_ventilatingStatusFL(): Boolean{
        return ventilatingStatusFL
    }

    fun set_ventilatingStatusFL(value: Boolean){
        ventilatingStatusFL = value
    }

    fun get_heatingStatusFR(): Boolean{
        return heatingStatusFR
    }

    fun set_heatingStatusFR(value: Boolean){
        heatingStatusFR = value
    }

    fun get_ventilatingStatusFR(): Boolean{
        return ventilatingStatusFR
    }

    fun set_ventilatingStatusFR(value: Boolean){
        ventilatingStatusFR = value
    }

    fun get_heatingLevelFR(): Int{
        return heatingLevelFR
    }

    fun set_heatingLevelFR(value: Int){
        heatingLevelFR = value
    }

    fun get_mainXDirFL(): Int{
        return mainXDirFL
    }

    fun set_mainXDirFL(value: Int){
        mainXDirFL = value
    }

    fun get_mainXDirFR(): Int{
        return mainXDirFR
    }

    fun set_mainXDirFR(value: Int){
        mainXDirFR = value
    }

    fun get_ventilationFL(): Int{
        return ventilationFL
    }

    fun set_ventilationFL(value: Int){
        ventilationFL = value
    }

    fun get_ventilationFR(): Int{
        return ventilationFR
    }

    fun set_ventilationFR(value: Int){
        ventilationFR = value
    }

    fun get_seat_FL_XActuateStatus(): Int{
        return seat_FL_XActuateStatus
    }

    fun set_seat_FL_XActuateStatus(value: Int){
        seat_FL_XActuateStatus = value
    }

    fun get_seat_FR_XActuateStatus(): Int{
        return seat_FR_XActuateStatus
    }

    fun set_seat_FR_XActuateStatus(value: Int){
        seat_FR_XActuateStatus = value
    }
}
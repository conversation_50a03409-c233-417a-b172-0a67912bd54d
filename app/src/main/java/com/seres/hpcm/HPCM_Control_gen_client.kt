package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.utils.LogUtils


import dds.rpc.*


class HPCM_ControlClient : ClientEndpoint {
    constructor(
        param: ClientParam,
        rpcRequest: TypeBase = HPCM_Control_Request(),
        rpcReply: TypeBase = HPCM_Control_Reply()
    ) : super(param, rpcRequest, rpcReply)
    fun BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir: Int): ReturnCode {
        LogUtils.d("MethodMap","BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU has been invoked. bcm_Legacy_SeatMainXDir = "+bcm_Legacy_SeatMainXDir)
        return ReturnCode.OK
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = HPCM_Control_Request()
        request.set_header(header)
        val call = HPCM_Control_Call()
        val request_data = HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatMainXDir(bcm_Legacy_SeatMainXDir as Int)

        call.set__d(HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as HPCM_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d) {
                var return_val = reply.get_data().get__u() as HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_Result
                return return_val.get__return()
            } else {
                return ret;
//                throw Exception("[HPCM_ControlClient]--> call BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU unkown _d() value")
            }
        }else{
//            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
            return ret;
        }
        return ret
    }

    fun BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode {
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFu).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000u).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = HPCM_Control_Request()
        request.set_header(header)
        val call = HPCM_Control_Call()
        val request_data = HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatVentilationLevel(bcm_Legacy_SeatVentilationLevel as Int)

        call.set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as HPCM_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d) {
                var return_val = reply.get_data().get__u() as HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result
                return return_val.get__return()
            } else {
//                throw Exception("[HPCM_ControlClient]--> call BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU unkown _d() value")
                return ret
            }
        }else{
//            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
            return ret
        }
        return ret
    }

    fun BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode {
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = HPCM_Control_Request()
        request.set_header(header)
        val call = HPCM_Control_Call()
        val request_data = HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatHeatingLevel(bcm_Legacy_SeatHeatingLevel as Int)

        call.set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as HPCM_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d) {
                var return_val = reply.get_data().get__u() as HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result
                return return_val.get__return()
            } else {
                return ret
                throw Exception("[HPCM_ControlClient]--> call BCM_CushionTemp_FR_HeatCtrlLeagacyECU unkown _d() value")
            }
        }else{
            return ret
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir: Int): ReturnCode {
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = HPCM_Control_Request()
        request.set_header(header)
        val call = HPCM_Control_Call()
        val request_data = HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatMainXDir(bcm_Legacy_SeatMainXDir as Int)

        call.set__d(HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as HPCM_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d) {
                var return_val = reply.get_data().get__u() as HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result
                return return_val.get__return()
            } else {
                return ret
                throw Exception("[HPCM_ControlClient]--> call BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU unkown _d() value")
            }
        }else{
            return ret
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode {
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = HPCM_Control_Request()
        request.set_header(header)
        val call = HPCM_Control_Call()
        val request_data = HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatVentilationLevel(bcm_Legacy_SeatVentilationLevel as Int)

        call.set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as HPCM_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d) {
                var return_val = reply.get_data().get__u() as HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_Result
                return return_val.get__return()
            } else {
                return ret
                throw Exception("[HPCM_ControlClient]--> call BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU unkown _d() value")
            }
        }else{
            return ret
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_CushionTemp_FL_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode {
        LogUtils.d("MethodMap","BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU has been invoked.")
        var ret : ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = HPCM_Control_Request()
        request.set_header(header)
        val call = HPCM_Control_Call()
        val request_data = HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatHeatingLevel(bcm_Legacy_SeatHeatingLevel as Int)

        call.set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as HPCM_Control_Reply
        if(request.get_header().get_requestId().get_sequence_number().get_low() == reply.get_header().get_relatedRequestId().get_sequence_number().get_low()
            &&request.get_header().get_requestId().get_sequence_number().get_high() == reply.get_header().get_relatedRequestId().get_sequence_number().get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKey())
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId().get_entityKind()
            ){

            if(reply.get_data().get__d() == HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d) {
                var return_val = reply.get_data().get__u() as HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_Result
                return return_val.get__return()
            } else {
                return ret
                throw Exception("[HPCM_ControlClient]--> call BCM_CushionTemp_FL_HeatCtrlLeagacyECU unkown _d() value")
            }
        }else{
            return ret
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }
}

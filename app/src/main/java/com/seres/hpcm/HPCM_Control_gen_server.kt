package seres.hpcm


import com.seres.dds.sdk.*
import dds.rpc.*


abstract class HPCM_Control_base {
    abstract fun BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir: Int): ReturnCode
    abstract fun BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode
    abstract fun BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode
    abstract fun BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir: Int): ReturnCode
    abstract fun BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode
    abstract fun BCM_CushionTemp_FL_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode
}

class HPCM_Control_dispatcher(requestType: TypeBase, replyType: TypeBase): Dispatcher<HPCM_Control_base>(requestType, replyType) {
    lateinit var serviceImpl: HPCM_Control_base
    lateinit var replier: Replier

    override fun process(request: TypeBase?) {
        var data: Any? = null
        var desc: HPCM_Control_descriminator = HPCM_Control_descriminator.NONE
        var sampleIdentity : SampleIdentity = SampleIdentity()
        desc = (request as HPCM_Control_Request).get_data().get__d()
        sampleIdentity = (request as HPCM_Control_Request).get_header().get_requestId()
        var result = HPCM_Control_Reply()
        var header = ReplyHeader()
        header.set_relatedRequestId(sampleIdentity)
        result.set_header(header)
        when (desc.value) {
            HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d.value -> {
                val inData = (request as HPCM_Control_Request).get_data().get__u() as HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_In
                val bcm_Legacy_SeatMainXDir = inData.get_bcm_Legacy_SeatMainXDir()

                val retcode = serviceImpl.BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir)

                var outData = HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d.value -> {
                val inData = (request as HPCM_Control_Request).get_data().get__u() as HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In
                val bcm_Legacy_SeatVentilationLevel = inData.get_bcm_Legacy_SeatVentilationLevel()

                val retcode = serviceImpl.BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel)

                var outData = HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d.value -> {
                val inData = (request as HPCM_Control_Request).get_data().get__u() as HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In
                val bcm_Legacy_SeatHeatingLevel = inData.get_bcm_Legacy_SeatHeatingLevel()

                val retcode = serviceImpl.BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel)

                var outData = HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d.value -> {
                val inData = (request as HPCM_Control_Request).get_data().get__u() as HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_In
                val bcm_Legacy_SeatMainXDir = inData.get_bcm_Legacy_SeatMainXDir()

                val retcode = serviceImpl.BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir)

                var outData = HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(HPCM_Control_descriminator.HPCM_Control_BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d.value -> {
                val inData = (request as HPCM_Control_Request).get_data().get__u() as HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_In
                val bcm_Legacy_SeatVentilationLevel = inData.get_bcm_Legacy_SeatVentilationLevel()

                val retcode = serviceImpl.BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel)

                var outData = HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d.value -> {
                val inData = (request as HPCM_Control_Request).get_data().get__u() as HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_In
                val bcm_Legacy_SeatHeatingLevel = inData.get_bcm_Legacy_SeatHeatingLevel()

                val retcode = serviceImpl.BCM_CushionTemp_FL_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel)

                var outData = HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(HPCM_Control_descriminator.HPCM_Control_BCM_CushionTemp_FL_HeatCtrlLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }
        }
    }

    override fun add_service_impl(serviceImpl: HPCM_Control_base) {
        this.serviceImpl = serviceImpl
    }

    override fun set_replier(replier: Replier) {
        this.replier = replier
    }
}

class HPCM_ControlService(
    serviceImpl: HPCM_Control_base,
    server: Server,
    param: ServiceParam,
    dispatcher: Dispatcher<HPCM_Control_base> = HPCM_Control_dispatcher(HPCM_Control_Request(), HPCM_Control_Reply())
) : ServiceEndpoint<HPCM_Control_base>(serviceImpl, param, server, dispatcher) {
}

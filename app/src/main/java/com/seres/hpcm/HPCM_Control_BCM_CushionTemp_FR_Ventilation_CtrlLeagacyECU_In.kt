package seres.hpcm
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In : TypeStruct(){
    private var bcm_Legacy_SeatVentilationLevel : Int = 0

    init{
        typename = "seres::hpcm::HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("bcm_Legacy_SeatVentilationLevel", 1u, PropType.PRIMITIVE, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, bcm_Legacy_SeatVentilationLevel)
            }
        }
    }

    override fun deserialize(buffer: Buffer): HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In {
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.bcm_Legacy_SeatVentilationLevel = res as Int
            }
        }
        return this
    }

    fun copy(value: HPCM_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In){
        bcm_Legacy_SeatVentilationLevel = value.bcm_Legacy_SeatVentilationLevel
    }

    fun get_bcm_Legacy_SeatVentilationLevel(): Int{
        return bcm_Legacy_SeatVentilationLevel
    }

    fun set_bcm_Legacy_SeatVentilationLevel(value: Int){
        bcm_Legacy_SeatVentilationLevel = value
    }
}
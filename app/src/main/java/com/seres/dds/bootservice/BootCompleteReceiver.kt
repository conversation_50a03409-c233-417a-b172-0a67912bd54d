package com.seres.dds.bootservice

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.seres.dds.utils.LogUtils

/**
 *@BelongsProject: S2SService
 *@BelongsPackage: com.seres.dds.receiver
 *@Author: ke.dong(416718)
 *@Email: <EMAIL>
 *@CreateTime: 2024-12-23 14:19
 *@Description: 接收安卓系统启动成功的广播
 */
class BootCompleteReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        LogUtils.i(TAG, "Receive BootComplete broadcast.")
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent?.action)) {
            LogUtils.i(TAG, "Start boot complete service")
            val intent = Intent(context, BootCompleteService::class.java)
            context?.startService(intent)
        }
    }
    
    companion object {
        private const val TAG = "BootCompleteReceiver"
    }
}
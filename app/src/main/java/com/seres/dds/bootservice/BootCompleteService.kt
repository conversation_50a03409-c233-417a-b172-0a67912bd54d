package com.seres.dds.bootservice

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.os.Message
import androidx.core.app.NotificationCompat
import com.google.gson.GsonBuilder
import com.seres.dds.R
import com.seres.dds.s2sservicesdk.S2sServiceSDK
import com.seres.dds.utils.BcmStatusUtils
import com.seres.dds.utils.BcmStatusUtils.getCurrentBcmStatus
import com.seres.dds.utils.BcmStatusUtils.recycleBcmStatus
import com.seres.dds.utils.LogUtils
import java.util.concurrent.TimeUnit

/**
 *@BelongsProject: S2SService
 *@BelongsPackage: com.seres.dds.service
 *@Author: ke.dong(416718)
 *@Email: <EMAIL>
 *@CreateTime: 2024-12-23 14:21
 *@Description: 车机系统启动后启动各种任务的服务
 */
class BootCompleteService : Service() {
    private val BOOT_SERVICE_CHANNEL_ID = "s2s_service_channel_id"
    private var notificationId = 1
    private val gson = GsonBuilder().setPrettyPrinting().create()
    
    private val DATA_TYPE: String = "dataType"
    private val DATA_CONTENT: String = "dataContent"
    private val DATA_ID_BCM_STATUS = 1
    
    private var tempWindowPositionFL = 0
    private var tempWindowPositionFR = 0
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "Start BootCompleteService")
        startForegroundNotification()
        initBootTask()
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    private fun startForegroundNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(BOOT_SERVICE_CHANNEL_ID, "S2sServiceChannel", NotificationManager.IMPORTANCE_DEFAULT)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            // 创建前台通知
            val notification = NotificationCompat.Builder(this, BOOT_SERVICE_CHANNEL_ID)
                .setContentTitle("S2s服务正在运行")
                .setContentText("S2s服务正在后台运行,提供数据中转")
                .setSmallIcon(R.mipmap.logo)
                .build()
            // 启动前台通知
            startForeground(notificationId, notification)
        }
    }
    
    private fun initBootTask() {
        // 打印bcm状态信息
        printBcmStatus()
        // updateIncreaseWindowPosition()
    }
    
    /**
     * 定时打印当前BCM_Status车辆状态信息
     *
     */
    private fun printBcmStatus() {
        BcmStatusUtils.scheduleTask(1000, TimeUnit.MILLISECONDS) {
            val bcmStatus = getCurrentBcmStatus()
            val bcmStatusJson = gson.toJson(bcmStatus)
            LogUtils.i(TAG, "Current bcm status:${bcmStatusJson}")
            recycleBcmStatus(bcmStatus)
        }
    }
    
    /**
     * 测试向android持续发送修改车窗打开的消息
     *
     */
    private fun updateIncreaseWindowPosition() {
        LogUtils.i(TAG, "Start test update window position")
        BcmStatusUtils.scheduleTask(1500, TimeUnit.MILLISECONDS) {
            val bcmStatus = getCurrentBcmStatus()
            bcmStatus.set_positionFL(tempWindowPositionFL++)
            bcmStatus.set_positionFR(tempWindowPositionFR++)
            val bcmStatusJson = gson.toJson(bcmStatus)
            val bundleBcmStatus = Bundle()
            bundleBcmStatus.putInt(DATA_TYPE, DATA_ID_BCM_STATUS)
            bundleBcmStatus.putString(DATA_CONTENT, bcmStatusJson)
            val bcmMsg = Message.obtain()
            bcmMsg.data = bundleBcmStatus
            // 上传bcm_Status
            LogUtils.i(TAG, "Send bcmMsg to android =\n$bcmStatusJson")
            // S2sServiceSDK.getIpcClient()?.sendMsg(bcmMsg)
        }
    }
    
    companion object {
        private const val TAG = "BootCompleteService"
    }
}
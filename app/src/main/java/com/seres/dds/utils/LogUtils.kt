package com.seres.dds.utils

import android.util.Log

/**
 *@BelongsProject: S2SService
 *@BelongsPackage: com.seres.dds.utils
 *@Author: ke.dong(416718)
 *@Email: <EMAIL>
 *@CreateTime: 2024-12-23 15:31
 *@Description: 打印log
 */
object LogUtils {
    private const val TAG = "S2sService"
    fun d(tag: String, msg: String) {
        Log.d(TAG, "$tag - $msg")
    }
    
    fun i(tag: String, msg: String) {
        Log.i(TAG, "$tag - $msg")
    }
    
    fun w(tag: String, msg: String) {
        Log.w(TAG, "$tag - $msg")
    }
    
    fun e(tag: String, msg: String) {
        Log.e(TAG, "$tag - $msg")
    }
}
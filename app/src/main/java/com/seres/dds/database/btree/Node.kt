package com.seres.dds.database.btree

class Node<T>(private val type : Class<T>, data : T) {
    private var hash : Int = 0
    private var isChange : Boolean = false
    private val appList : MutableList<Int> = mutableListOf()
    private var value = data

    fun readNodeIsChange(): Boolean{
        return isChange
    }
    fun writeNodeIsChange(data : Boolean){
        isChange = data
    }

    fun subscribeApp(id:Int){
        appList.add(id)
    }

    fun unsubscribeApp(id:Int){
        appList.remove(id)
    }

    fun getAppList():MutableList<Int>{
        return appList
    }

    fun readNodeValue(): T{
        return value
    }
    fun writeNodeValue(data: T){
        value = data
    }

    fun getNodeType(): Class<*> {
        return type
    }
}
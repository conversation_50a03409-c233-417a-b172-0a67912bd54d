package com.seres.dds.database.btree

object S2sBTree {
    private var bTree = BTree<Int,Node<*>>()

    private fun nodeInsert(key:Int, node: Node<*>){
        bTree.put(key,node)
    }

    fun nodeFind(key:Int):Node<*>{
        return bTree.get(key)
    }

    /**
     * @name: nodeCreate
     * @Description: Create a node and insert it into the B-tree.
     * @param key: node index
     * @param type: node type
     * @param value: signal value
     * @return:
     */
    fun <T>nodeCreate(key: Int, type:Class<T>, value: T){
        val node = Node<T>(type, value)
        nodeInsert(key,node)
    }
}

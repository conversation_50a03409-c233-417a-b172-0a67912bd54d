package com.seres.dds.testdemo.hpcm_demo.pubsub

import com.seres.dds.commsvc.DomainContext
import seres.hpcm.HPCM_Status
import com.seres.dds.sdk.DataWriter
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.KScomNativeLib.kScomGetStatueMask
import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
import com.seres.dds.sdk.Publisher
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.core.StatusMask.Companion.publication_matched
import com.seres.dds.utils.LogUtils
import seres.zcur.ZCUR_Status

//class MyListener : Listener(){
//
//    override fun on_liveliness_changed(entityid:Int, dds_liveliness_changed_status:dds_liveliness_changed_status_t)
//    {
//        println("on_liveliness_changed ==> entityId = ${entityid}");
//    }
//
//    override fun on_publication_matched(entityid:Int, dds_publication_matched_status:dds_publication_matched_status_t)
//    {
//        println("on_publication_matched ==> entityId = ${entityid}");
//    }
//
//    override fun on_subscription_matched(entityid:Int, dds_subscription_matched_status:dds_subscription_matched_status_t)
//    {
//        println("on_subscription_matched ==> entityId = ${entityid}");
//    }
//}

var reportData = 0

fun pubMain(){
    val tag = "PubTest"
    val par = DomainParticipant(1)
    val publisher = Publisher(par)

//    val topic_hpcm = Topic(par, "HPCM_MCU_STATUS", HPCM_Status())
//    val writer_hpcm = DataWriter(par, topic_hpcm)
//    kScomSetStatusMask(writer_hpcm.ref, publication_matched().toLong());
//    var status_hpcm:Long = 0L
//
//
//    val fixedThreadPool = Executors.newFixedThreadPool(4)
//    val task_hpcm_pub = Runnable{
//
//        while(((status_hpcm) and publication_matched().toLong()).toInt() == 0){
//    //        println()
//            status_hpcm = kScomGetStatueMask(writer_hpcm.ref)
//            var data = HPCM_Status()
//            data.set_mainXDirFL(10)
//            data.set_mainXDirFR(20)
//            data.set_heatingLevelFL(30)
//            data.set_heatingStatusFR(false)
//            data.set_heatingLevelFR(50)
//            data.set_heatingLevelFL(60)
//            data.set_heatingStatusFL(true)
//            data.set_ventilatingStatusFL(true)
//            data.set_ventilatingStatusFR(false)
//            writer_hpcm.write(data)
//            Thread.sleep(200)
//        }
//    }
//    val future_hpcm = fixedThreadPool.submit(task_hpcm_pub)
//
//    val topic_zcufr = Topic(par, "ZCU_FR_STATUS", ZCUFR_Status())
//    val writer_zcufr = DataWriter(par, topic_zcufr)
//    kScomSetStatusMask(writer_zcufr.ref, publication_matched().toLong());
//    var status_zcufr:Long = 0L
//
//
//    val task_zcufr_pub = Runnable{
//        while(((status_zcufr) and publication_matched().toLong()).toInt() == 0){
//            //        println()
//            status_zcufr = kScomGetStatueMask(writer_zcufr.ref)
//            var data = ZCUFR_Status()
//            data.set_heatingLevelFL(15)
//            data.set_heatingStatusFR(true)
//            data.set_heatingLevelFR(5)
//            data.set_heatingLevelFL(6)
//            data.set_heatingStatusFL(false)
//            data.set_ventilatingStatusFL(false)
//            data.set_ventilatingStatusFR(true)
//            writer_zcufr.write(data)
//            Thread.sleep(200)
//        }
//    }
//    val future_zcufr = fixedThreadPool.submit(task_zcufr_pub)
//
//
//
//    val topic_zcuf = Topic(par, "ZCU_F_STATUS", ZCUF_Status())
//    val writer_zcuf = DataWriter(par, topic_zcuf)
//    kScomSetStatusMask(writer_zcuf.ref, publication_matched().toLong());
//    var status_zcuf:Long = 0L
//
//
//    val task_zcuf_pub = Runnable{
//        while(((status_zcuf) and publication_matched().toLong()).toInt() == 0){
//            //        println()
//            status_zcuf = kScomGetStatueMask(writer_zcuf.ref)
//            var data = ZCUF_Status()
//            data.set_commonlight_hood(50)
//            writer_zcuf.write(data)
//            Thread.sleep(200)
//        }
//    }
//
//    val future_zcuf = fixedThreadPool.submit(task_zcuf_pub)


    val topic_zcur = Topic(DomainContext.dp(), "ZCU_R_STATUS", ZCUR_Status())
    val writer_zcur = DataWriter(DomainContext.dp(), topic_zcur)
    kScomSetStatusMask(writer_zcur.ref, publication_matched().toLong());
    var status_zcur:Long = 0L
//
    while(((status_zcur) and publication_matched().toLong()).toInt() == 0){
        //        println()
        status_zcur = kScomGetStatueMask(writer_zcur.ref)
        var data = ZCUR_Status()
        reportData += 10
        data.set_commonlight_trunk_lightstatus(reportData)
        writer_zcur.write(data)
        LogUtils.d(tag,"pub msg to Sub")
        break
    }
}
package com.seres.dds.testdemo.hpcm_demo.pubsub

import seres.hpcm.HPCM_Status
import com.seres.dds.sdk.DataReader
import com.seres.dds.sdk.DataWriter
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.KScomNativeLib.kScomGetStatueMask
import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
import com.seres.dds.sdk.Publisher
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.core.StatusMask.Companion.publication_matched
import seres.zcuf.ZCUF_Status
import seres.zcufr.ZCUFR_Status
import seres.zcur.ZCUR_Status
import java.util.concurrent.Executors


//fun subMain(){
//    val par = DomainParticipant(1)
////
////    val topic = Topic(par, "kotlinidl_Subdata", Subdata())
//
//    val topic = Topic(par, "ZCU_R_STATUS", ZCUR_Status())
//    val reader = DataReader(par, topic)
//
//    while(true){
//        val samples = reader.take()
//
//
//        samples.sample_list!!.forEach{ sample ->
//            var data = sample.type as ZCUR_Status
//
////            println("temp._d is :" + temp._d)
//
////            println("temp.msg.get_num() is :" + temp.get_num())
////            println("temp.msg.get_num() is :" + temp.get_des())
////            println("temp.mag.g " + )
////            println("data.get_mainXDirFL: "+ data.get_mainXDirFL())
////            println("data.get_mainXDirFR: "+ data.get_mainXDirFR())
////            println("data.get_heatingLevelFL: " + data.get_heatingLevelFL())
////            println("data.get_heatingStatusFR: " + data.get_heatingStatusFR())
////            println("data.get_heatingLevelFR: " + data.get_heatingLevelFR())
////            println("data.get_heatingLevelFL: " + data.get_heatingLevelFL())
////            println("data.get_heatingStatusFL: " + data.get_heatingStatusFL())
////            println("data.get_ventilatingStatusFL: "+ data.get_ventilatingStatusFL())
////            println("data.get_ventilatingStatusFR: " + data.get_ventilatingStatusFR())
//            println("data.get_commonlight_trunk_lightstatus"+data.get_commonlight_trunk_lightstatus())
//        }
//
//        Thread.sleep(1000)
//    }
//}
//
fun subMain(){
    val par = DomainParticipant(1)
    val publisher = Publisher(par)
//
    val topic_hpcm = Topic(par, "HPCM_MCU_STATUS", HPCM_Status())
    val writer_hpcm = DataWriter(par, topic_hpcm)
    kScomSetStatusMask(writer_hpcm.ref, publication_matched().toLong());
    var status_hpcm:Long = 0L


    val fixedThreadPool = Executors.newFixedThreadPool(4)
    val task_hpcm_pub = Runnable{

        while(((status_hpcm) and publication_matched().toLong()).toInt() == 0){
    //        println()
            status_hpcm = kScomGetStatueMask(writer_hpcm.ref)
            var data = HPCM_Status()
            data.set_mainXDirFL(10)
            data.set_mainXDirFR(20)
            data.set_heatingLevelFL(30)
            data.set_heatingStatusFR(false)
            data.set_heatingLevelFR(50)
            data.set_heatingLevelFL(60)
            data.set_heatingStatusFL(true)
            data.set_ventilatingStatusFL(true)
            data.set_ventilatingStatusFR(false)
            writer_hpcm.write(data)
            Thread.sleep(200)
        }
    }
    val future_hpcm = fixedThreadPool.submit(task_hpcm_pub)

    val topic_zcufr = Topic(par, "ZCU_FR_STATUS", ZCUFR_Status())
    val writer_zcufr = DataWriter(par, topic_zcufr)
    kScomSetStatusMask(writer_zcufr.ref, publication_matched().toLong());
    var status_zcufr:Long = 0L


    val task_zcufr_pub = Runnable{
        while(((status_zcufr) and publication_matched().toLong()).toInt() == 0){
            //        println()
            status_zcufr = kScomGetStatueMask(writer_zcufr.ref)
            var data = ZCUFR_Status()
            data.set_heatingLevelFL(15)
            data.set_heatingStatusFR(true)
            data.set_heatingLevelFR(5)
            data.set_heatingLevelFL(6)
            data.set_heatingStatusFL(false)
            data.set_ventilatingStatusFL(false)
            data.set_ventilatingStatusFR(true)
            writer_zcufr.write(data)
            Thread.sleep(200)
        }
    }
    val future_zcufr = fixedThreadPool.submit(task_zcufr_pub)



    val topic_zcuf = Topic(par, "ZCU_F_STATUS", ZCUF_Status())
    val writer_zcuf = DataWriter(par, topic_zcuf)
    kScomSetStatusMask(writer_zcuf.ref, publication_matched().toLong());
    var status_zcuf:Long = 0L


    val task_zcuf_pub = Runnable{
        while(((status_zcuf) and publication_matched().toLong()).toInt() == 0){
            //        println()
            status_zcuf = kScomGetStatueMask(writer_zcuf.ref)
            var data = ZCUF_Status()
            data.set_commonlight_hood(50)
            writer_zcuf.write(data)
            Thread.sleep(200)
        }
    }

    val future_zcuf = fixedThreadPool.submit(task_zcuf_pub)


    val topic_zcur = Topic(par, "ZCU_R_STATUS", ZCUR_Status())
    val writer_zcur = DataWriter(par, topic_zcur)
    kScomSetStatusMask(writer_zcur.ref, publication_matched().toLong());
    var status_zcur:Long = 0L
//
    while(((status_zcur) and publication_matched().toLong()).toInt() == 0){
        //        println()
        status_zcur = kScomGetStatueMask(writer_zcur.ref)
        var data = ZCUR_Status()
        data.set_commonlight_trunk_lightstatus(555)
        writer_zcur.write(data)
        Thread.sleep(20)
    }
//
//
}
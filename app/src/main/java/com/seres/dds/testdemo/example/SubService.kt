package com.seres.dds.testdemo.example

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
//import subMain
import java.util.concurrent.Executors

import com.seres.dds.testdemo.hpcm_demo.pubsub.subMain

class SubService : Service() {
    private val threadPool = Executors.newCachedThreadPool()

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "SubService onCreate, process is = ${android.os.Process.myPid()}")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "SubService onStartCommand, process is = ${android.os.Process.myPid()}")
        startSubClient()
        return START_STICKY
    }


    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    private fun startSubClient() {
        Log.i(TAG, "startSubClient")
        threadPool.submit(object : Runnable {
            override fun run() {
                Log.i(TAG, "runSubClient")
//                test.subMain()
//                subMain()
                subMain()
            }
        })
    }

    companion object {
        const val TAG = "SubService"
    }
}
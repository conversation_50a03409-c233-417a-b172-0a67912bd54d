package com.seres.dds.testdemo.hpcm_demo.rpc

// import com.seres.dds.sdk.core.Listener
import android.util.Log
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.Server
import com.seres.dds.sdk.ServerParam
import com.seres.dds.sdk.ServiceParam
import seres.hpcm.*


class ServiceImpl : HPCM_Control_base() {

    companion object {
        private const val TAG = "ServiceImpl"
    }

    override fun BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir: Int): ReturnCode {
        println("server demo get msg : bcm_Legacy_SeatMainXDir: " + bcm_Legacy_SeatMainXDir)
        return ReturnCode.OK
    }

    override fun BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode {
        println("server demo get msg : bcm_Legacy_SeatVentilationLevel: " + bcm_Legacy_SeatVentilationLevel)
        return ReturnCode.OK
    }

    override fun BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode {
        println("server demo get msg : bcm_Legacy_SeatHeatingLevel: " + bcm_Legacy_SeatHeatingLevel)
        return ReturnCode.OK
    }

    override fun BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir: Int): ReturnCode {
        println("server demo get msg : bcm_Legacy_SeatMainXDir: " + bcm_Legacy_SeatMainXDir)
        return ReturnCode.OK
    }

    override fun BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode {
        println("server demo get msg : bcm_Legacy_SeatVentilationLevel: " + bcm_Legacy_SeatVentilationLevel)
        return ReturnCode.OK
    }

    override fun BCM_CushionTemp_FL_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode {
        println("server demo get msg : bcm_Legacy_SeatHeatingLevel: " + bcm_Legacy_SeatHeatingLevel)
        return ReturnCode.OK
    }

}

class ServerDemo {
    private var server: Server? = null
    fun start() {
        val dp = DomainParticipant(1)
        server = Server(ServerParam(dp))
        val serviceparam = ServiceParam(dp, null, null)
        serviceparam.set_service_name("MCU_CTRL")
        val service = HPCM_ControlService(ServiceImpl(), server!!, serviceparam)
        server?.start()
        Log.i(TAG, "end serverdemo")
    }

    fun stop() {
        server?.stop()
    }

    companion object {
        private const val TAG = "ServerDemo"
    }

}
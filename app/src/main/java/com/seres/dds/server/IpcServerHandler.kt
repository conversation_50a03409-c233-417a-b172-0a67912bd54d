package com.seres.dds.server

import android.os.Bundle
import com.seres.dds.server.api.BCM_SeatEnhanceFuncStatus
import com.seres.dds.server.api.BCM_SeatLearnCMD
import com.seres.dds.server.api.BCM_SeatPosition
import com.seres.dds.server.api.BCM_SeatPositonByStep
import com.seres.dds.server.consts.InvokeConsts
import com.seres.dds.server.consts.ReturnCode
import com.seres.dds.server.consts.ServiceHash
import com.seres.dds.upgrade.UpgradeTaskHandler
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IAsyncResultCallback

/**
 * 处理客户端调用s2s服务的实现类
 *
 * @constructor Create empty Server impl
 */
object IpcServerHandler {
    private const val TAG = "ServerImpl"
    
    /**
     * 同步处理客户端对s2s服务的请求
     *
     * @param appId
     * @param serviceHashId 要调用的服务hashId值
     * @param params
     */
    fun handleRequestSync(appId: Int?, serviceHashId: Int, params: Bundle?): Bundle {
        // LogUtils.d(TAG, "处理[$appId]调用同步方法$serviceHashId")
        return handleRequest(serviceHashId, params)
    }
    
    /**
     * 异步处理客户端对s2s服务的请求
     *
     * @param appId
     * @param serviceHashId 要调用的服务hashId值
     * @param params
     * @param callback
     */
    fun handleRequestAsync(appId: Int, serviceHashId: Int, params: Bundle?, callback: IAsyncResultCallback?) {
        // LogUtils.d(TAG, "处理[$appId]调用异步方法$serviceHashId")
        val resultBundle = handleRequest(serviceHashId, params)
        callback?.onResult(resultBundle)
    }
    
    /**
     * 处理client端服务调用
     *
     * @param serviceHashId 要调用的服务hashId值
     * @param params 入参
     * @return 返回bundle，返回值在bundle中
     */
    private fun handleRequest(serviceHashId: Int, params: Bundle?): Bundle {
        val bundle = Bundle()
        // TODO 具体实现处理客户端方法调用
        when (serviceHashId) {
            ServiceHash.HASH_BCM_SEAT -> {
                // 设置Parcelable参数解析classLoader
                params?.classLoader = BCM_SeatPosition::class.java.classLoader
                // 获取参数
                val bcm_CallerID = params?.getInt(InvokeConsts.MethodSeatAdjustPosCtrl.PARAM_CALL_ID)
                val bcm_CallerID1 = params?.getBoolean(InvokeConsts.MethodSeatAdjustPosCtrl.PARAM_CALL_ID)
                val bcm_SeatPositon = params?.getParcelable<BCM_SeatPosition>(InvokeConsts.MethodSeatAdjustPosCtrl.PARAM_SEAT_POSITION)
                LogUtils.i(TAG, "$serviceHashId bcm_CallerID=$bcm_CallerID,bcm_SeatPositon=${bcm_SeatPositon?.array}")
                
                
                // 设置返回值
                bundle.putInt(InvokeConsts.KEY_RESULT, 20)
            }
            
            ServiceHash.HASH_BCM_SEAT_GET_STATUS -> {
                // 设置Parcelable参数解析classLoader
                params?.classLoader = BCM_SeatEnhanceFuncStatus::class.java.classLoader
                // 获取参数
                val bcm_CallerID = params?.getInt(InvokeConsts.MethodSeatEnhanceFuncGetSts.PARAM_CALL_ID)
                val bcm_SeatEnhanceFuncStatus = params?.getParcelable<BCM_SeatEnhanceFuncStatus>(InvokeConsts.MethodSeatEnhanceFuncGetSts.PARAM_ENHANCE_FUNC_STS)
                LogUtils.i(TAG, "$serviceHashId bcm_call_id=$bcm_CallerID, bcm_enhanceFuncStatus size=${bcm_SeatEnhanceFuncStatus?.array?.size}")
                // 设置返回值
                bundle.putParcelable(InvokeConsts.KEY_RESULT, null)
            }
            
            ServiceHash.HASH_BCM_SEAT_ADJUST_POSITION_BY_STEP_CTRL -> {
                // 设置Parcelable参数解析classLoader
                params?.classLoader = BCM_SeatPositonByStep::class.java.classLoader
                // 获取参数
                val bcmCallId = params?.getInt(InvokeConsts.MethodSeatAdjustPositionByStepCtrl.PARAM_CALL_ID)
                val positions = params?.getParcelable<BCM_SeatPositonByStep>(InvokeConsts.MethodSeatAdjustPositionByStepCtrl.PARAM_SEAT_BY_STEP)
                LogUtils.i(TAG, "$serviceHashId bcm_CallerID=$bcmCallId,bcm_SeatAdjustPositionByStep size=${positions?.array?.size}")
                // 设置返回值
                bundle.putInt(InvokeConsts.KEY_RESULT, 10)
            }
            
            ServiceHash.HASH_BCM_SEAT_LEARN_CTRL -> {
                // 设置Parcelable参数解析classLoader
                params?.classLoader = BCM_SeatLearnCMD::class.java.classLoader
                // 获取参数
                val bcmCallId = params?.getInt(InvokeConsts.MethodSeatLearnCtrl.PARAM_CALL_ID)
                val learns = params?.getParcelable<BCM_SeatLearnCMD>(InvokeConsts.MethodSeatLearnCtrl.PARAM_LEARN_CMD)
                LogUtils.i(TAG, "$serviceHashId bcm_CallerID=$bcmCallId,bcm_seatLearnCMD size=${learns?.array?.size}")
                // 设置返回值
                bundle.putInt(InvokeConsts.KEY_RESULT, 22)
            }

            // 升级任务相关服务
            1000, 1001 -> { // UPGRADE_TASK_SERVICE_HASH, UPGRADE_STATUS_SERVICE_HASH
                LogUtils.i(TAG, "Handling upgrade task service: $serviceHashId")
                return UpgradeTaskHandler.handleUpgradeTaskPublish(params)
            }
        }
        return bundle
    }
}
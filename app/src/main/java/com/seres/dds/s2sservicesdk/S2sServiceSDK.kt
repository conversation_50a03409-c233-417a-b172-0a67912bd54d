package com.seres.dds.s2sservicesdk

import android.content.Context
import android.content.Intent
import com.seres.dds.threadpool.ThreadPool
import com.seres.dds.commsvc.rpc.ClientManager
import com.seres.dds.commsvc.ipc.IpcClientManager
import com.seres.dds.commsvc.sub.SubscriberManager
import com.seres.dds.database.DataBase
import com.seres.dds.upgrade.UsbDetectionService
import com.seres.dds.utils.LogUtils

object S2sServiceSDK {
    private const val TAG = "S2sServiceSDK"
    
    /**
     * 初始化S2s服务，在应用application启动时初始化
     *
     * @param context
     */
    fun init(context: Context) {
        LogUtils.i(TAG, "Init s2s service")

        DataBase.init()
        ClientManager.clientInit()
        SubscriberManager.subscriberInit()
        SubscriberManager.subReceive()

        // 4. Ipc Client
        ThreadPool.submitTask(object : Runnable {
            override fun run() {
                IpcClientManager.report()
            }
        })

        // 5. 启动USB检测服务
        ThreadPool.submitTask(object : Runnable {
            override fun run() {
                try {
                    val usbServiceIntent = Intent(context, UsbDetectionService::class.java)
                    context.startService(usbServiceIntent)
                    LogUtils.i(TAG, "USB detection service started")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "Failed to start USB detection service: ${e.message}")
                }
            }
        })
    }
}
package com.seres.dds.upgrade

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.seres.dds.commsvc.DomainContext
import com.seres.dds.sdk.DataReader
import com.seres.dds.sdk.Topic
import com.seres.dds.server.api.UpgradePackageInfo
import com.seres.dds.server.api.UpgradeTaskInfo
import com.seres.dds.server.api.UpgradeTaskStatus
import com.seres.dds.threadpool.ThreadPool
import com.seres.dds.utils.LogUtils
import com.seres.upgrade.UpgradeTask_Status

/**
 * DDS升级任务订阅者
 * 用于其他域控订阅升级任务（通过DDS接收S2S转发的任务）
 */
class UpgradeTaskSubscriber {
    
    private val TAG = "UpgradeTaskSubscriber"
    private val gson = Gson()
    
    // DDS相关组件
    private val topicName = "UPGRADE_TASK_STATUS"
    private val dataType = UpgradeTask_Status()
    private val topic: Topic
    private val reader: DataReader
    
    // 任务监听器
    private var taskListener: UpgradeTaskListener? = null
    
    init {
        // 初始化DDS订阅者
        topic = Topic(DomainContext.dp(), topicName, dataType)
        reader = DataReader(DomainContext.dp(), topic)
        
        LogUtils.i(TAG, "UpgradeTaskSubscriber initialized with topic: $topicName")
        
        // 启动订阅接收
        startReceiving()
    }
    
    /**
     * 设置任务监听器
     */
    fun setTaskListener(listener: UpgradeTaskListener) {
        this.taskListener = listener
        LogUtils.i(TAG, "Task listener set")
    }
    
    /**
     * 启动订阅接收
     */
    private fun startReceiving() {
        val task = Runnable {
            LogUtils.i(TAG, "Starting upgrade task subscription")
            
            while (true) {
                try {
                    val samples = reader.take()
                    
                    samples.sample_list?.forEach { sample ->
                        val data = sample.type as UpgradeTask_Status
                        
                        LogUtils.d(TAG, "Received upgrade task data: ${data.get_taskId()}")
                        
                        // 转换为UpgradeTaskInfo
                        val taskInfo = convertFromUpgradeTaskStatus(data)
                        
                        // 通知监听器
                        taskListener?.onTaskReceived(taskInfo)
                        
                        // 根据任务状态处理
                        when (taskInfo.taskStatus) {
                            UpgradeTaskStatus.READY -> {
                                LogUtils.i(TAG, "Received ready upgrade task: ${taskInfo.taskId}")
                                taskListener?.onTaskReady(taskInfo)
                            }
                            UpgradeTaskStatus.DISTRIBUTING -> {
                                LogUtils.i(TAG, "Received distributing task: ${taskInfo.taskId}")
                                taskListener?.onTaskDistributing(taskInfo)
                            }
                            UpgradeTaskStatus.COMPLETED -> {
                                LogUtils.i(TAG, "Task completed: ${taskInfo.taskId}")
                                taskListener?.onTaskCompleted(taskInfo)
                            }
                            UpgradeTaskStatus.FAILED -> {
                                LogUtils.w(TAG, "Task failed: ${taskInfo.taskId}")
                                taskListener?.onTaskFailed(taskInfo)
                            }
                            UpgradeTaskStatus.CANCELLED -> {
                                LogUtils.i(TAG, "Task cancelled: ${taskInfo.taskId}")
                                taskListener?.onTaskCancelled(taskInfo)
                            }
                            else -> {
                                LogUtils.d(TAG, "Task status update: ${taskInfo.taskId} -> ${taskInfo.taskStatus}")
                                taskListener?.onTaskStatusChanged(taskInfo)
                            }
                        }
                    }
                    
                    Thread.sleep(20) // 短暂休眠避免CPU占用过高
                    
                } catch (e: Exception) {
                    LogUtils.e(TAG, "Error receiving upgrade task data: ${e.message}")
                    Thread.sleep(1000) // 出错时休眠更长时间
                }
            }
        }
        
        ThreadPool.submitTask(task)
    }
    
    /**
     * 将UpgradeTask_Status转换为UpgradeTaskInfo
     */
    private fun convertFromUpgradeTaskStatus(ddsData: UpgradeTask_Status): UpgradeTaskInfo {
        val taskInfo = UpgradeTaskInfo()
        
        taskInfo.taskId = ddsData.get_taskId()
        taskInfo.taskName = ddsData.get_taskName()
        taskInfo.taskVersion = ddsData.get_taskVersion()
        taskInfo.createTime = ddsData.get_createTime()
        taskInfo.usbPath = ddsData.get_usbPath()
        taskInfo.targetPath = ddsData.get_targetPath()
        taskInfo.totalSize = ddsData.get_totalSize()
        taskInfo.taskStatus = UpgradeTaskStatus.values()[ddsData.get_taskStatus()]
        taskInfo.description = ddsData.get_description()
        
        // 解析包列表JSON
        val packageListJson = ddsData.get_packageListJson()
        if (packageListJson.isNotEmpty()) {
            try {
                val packageListType = object : TypeToken<List<UpgradePackageInfo>>() {}.type
                val packageList: List<UpgradePackageInfo> = gson.fromJson(packageListJson, packageListType)
                taskInfo.packageList = packageList.toMutableList()
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error parsing package list JSON: ${e.message}")
                taskInfo.packageList = mutableListOf()
            }
        }
        
        return taskInfo
    }
}

/**
 * 升级任务监听器接口
 */
interface UpgradeTaskListener {
    /**
     * 收到升级任务
     */
    fun onTaskReceived(task: UpgradeTaskInfo)
    
    /**
     * 任务准备就绪
     */
    fun onTaskReady(task: UpgradeTaskInfo)
    
    /**
     * 任务分发中
     */
    fun onTaskDistributing(task: UpgradeTaskInfo)
    
    /**
     * 任务完成
     */
    fun onTaskCompleted(task: UpgradeTaskInfo)
    
    /**
     * 任务失败
     */
    fun onTaskFailed(task: UpgradeTaskInfo)
    
    /**
     * 任务取消
     */
    fun onTaskCancelled(task: UpgradeTaskInfo)
    
    /**
     * 任务状态变化
     */
    fun onTaskStatusChanged(task: UpgradeTaskInfo)
}

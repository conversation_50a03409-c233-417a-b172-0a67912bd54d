package com.seres.dds.upgrade

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.google.gson.Gson
import com.seres.dds.server.api.UpgradeTaskInfo
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IAsyncResultCallback
import seres.s2s.internal.IS2SReportListener
import seres.s2s.internal.IS2SService
import java.util.concurrent.Executors

/**
 * 升级任务客户端
 * 用于其他域控与S2S服务通信，接收升级任务
 */
class UpgradeTaskClient(private val context: Context, private val appId: Int) {
    
    private val TAG = "UpgradeTaskClient"
    private val gson = Gson()
    private val threadPool = Executors.newCachedThreadPool()
    
    // S2S服务相关
    private var s2sService: IS2SService? = null
    private var isServiceConnected = false
    
    // 升级任务监听器
    private var taskListener: UpgradeTaskListener? = null
    
    // 升级任务相关常量
    companion object {
        const val UPGRADE_TASK_SIGNAL_HASH = 2000 // 升级任务信号Hash ID
        const val UPGRADE_STATUS_SIGNAL_HASH = 2001 // 升级状态信号Hash ID
    }
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            s2sService = IS2SService.Stub.asInterface(service)
            isServiceConnected = true
            LogUtils.i(TAG, "Connected to S2S service")
            
            // 注册升级任务监听器
            registerUpgradeTaskListener()
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            s2sService = null
            isServiceConnected = false
            LogUtils.w(TAG, "Disconnected from S2S service")
        }
    }
    
    private val s2sReportListener = object : IS2SReportListener.Stub() {
        override fun notify(data: Bundle?) {
            try {
                data?.let { handleS2SNotification(it) }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error handling S2S notification: ${e.message}")
            }
        }
    }
    
    init {
        connectToS2SService()
        LogUtils.i(TAG, "UpgradeTaskClient initialized for appId: $appId")
    }
    
    /**
     * 设置任务监听器
     */
    fun setTaskListener(listener: UpgradeTaskListener) {
        this.taskListener = listener
        LogUtils.i(TAG, "Task listener set")
    }
    
    /**
     * 连接到S2S服务
     */
    private fun connectToS2SService() {
        try {
            val intent = Intent("com.example.remote.action").apply {
                setPackage("com.seres.dds")
            }
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
            LogUtils.i(TAG, "Binding to S2S service")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Failed to bind S2S service: ${e.message}")
        }
    }
    
    /**
     * 注册升级任务监听器
     */
    private fun registerUpgradeTaskListener() {
        threadPool.submit {
            try {
                if (isServiceConnected) {
                    val signalHashList = intArrayOf(UPGRADE_TASK_SIGNAL_HASH, UPGRADE_STATUS_SIGNAL_HASH)
                    s2sService?.registerS2SSignalListener(appId, s2sReportListener, signalHashList)
                    LogUtils.i(TAG, "Registered upgrade task listener for appId: $appId")
                } else {
                    LogUtils.w(TAG, "Cannot register listener: S2S service not connected")
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error registering upgrade task listener: ${e.message}")
            }
        }
    }
    
    /**
     * 处理S2S通知
     */
    private fun handleS2SNotification(data: Bundle) {
        try {
            val signalHash = data.getInt("signal_hash", -1)
            val signalData = data.getString("signal_data", "")
            
            LogUtils.d(TAG, "Received S2S notification: signalHash=$signalHash")
            
            when (signalHash) {
                UPGRADE_TASK_SIGNAL_HASH -> {
                    handleUpgradeTaskNotification(signalData)
                }
                UPGRADE_STATUS_SIGNAL_HASH -> {
                    handleUpgradeStatusNotification(signalData)
                }
                else -> {
                    LogUtils.w(TAG, "Unknown signal hash: $signalHash")
                }
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error handling S2S notification: ${e.message}")
        }
    }
    
    /**
     * 处理升级任务通知
     */
    private fun handleUpgradeTaskNotification(taskDataJson: String) {
        try {
            if (taskDataJson.isNotEmpty()) {
                val taskInfo = gson.fromJson(taskDataJson, UpgradeTaskInfo::class.java)
                LogUtils.i(TAG, "Received upgrade task: ${taskInfo.taskId}")
                
                taskListener?.onTaskReceived(taskInfo)
                
                // 根据任务状态处理
                when (taskInfo.taskStatus) {
                    com.seres.dds.server.api.UpgradeTaskStatus.READY -> {
                        LogUtils.i(TAG, "Received ready upgrade task: ${taskInfo.taskId}")
                        taskListener?.onTaskReady(taskInfo)
                    }
                    com.seres.dds.server.api.UpgradeTaskStatus.DISTRIBUTING -> {
                        LogUtils.i(TAG, "Received distributing task: ${taskInfo.taskId}")
                        taskListener?.onTaskDistributing(taskInfo)
                    }
                    else -> {
                        LogUtils.d(TAG, "Task status: ${taskInfo.taskId} -> ${taskInfo.taskStatus}")
                        taskListener?.onTaskStatusChanged(taskInfo)
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error handling upgrade task notification: ${e.message}")
        }
    }
    
    /**
     * 处理升级状态通知
     */
    private fun handleUpgradeStatusNotification(statusDataJson: String) {
        try {
            if (statusDataJson.isNotEmpty()) {
                val statusData = gson.fromJson(statusDataJson, Map::class.java) as Map<String, Any>
                val taskId = statusData["taskId"] as? String ?: ""
                val status = statusData["status"] as? String ?: ""
                val message = statusData["message"] as? String ?: ""
                
                LogUtils.d(TAG, "Received status update: $taskId -> $status")
                
                when (status) {
                    "COMPLETED" -> {
                        LogUtils.i(TAG, "Task completed: $taskId")
                        // 创建一个简单的任务信息用于回调
                        val taskInfo = UpgradeTaskInfo().apply {
                            this.taskId = taskId
                            this.taskStatus = com.seres.dds.server.api.UpgradeTaskStatus.COMPLETED
                            this.description = message
                        }
                        taskListener?.onTaskCompleted(taskInfo)
                    }
                    "FAILED" -> {
                        LogUtils.w(TAG, "Task failed: $taskId - $message")
                        val taskInfo = UpgradeTaskInfo().apply {
                            this.taskId = taskId
                            this.taskStatus = com.seres.dds.server.api.UpgradeTaskStatus.FAILED
                            this.description = message
                        }
                        taskListener?.onTaskFailed(taskInfo)
                    }
                    else -> {
                        LogUtils.d(TAG, "Status update: $taskId -> $status")
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error handling upgrade status notification: ${e.message}")
        }
    }
    
    /**
     * 发送升级任务执行结果
     */
    fun reportTaskResult(taskId: String, success: Boolean, message: String) {
        threadPool.submit {
            try {
                if (!isServiceConnected) {
                    LogUtils.w(TAG, "Cannot report task result: S2S service not connected")
                    return@submit
                }
                
                val resultData = mapOf(
                    "taskId" to taskId,
                    "success" to success,
                    "message" to message,
                    "timestamp" to System.currentTimeMillis(),
                    "reporterId" to appId
                )
                val resultJson = gson.toJson(resultData)
                
                val params = Bundle().apply {
                    putString("action", "report_task_result")
                    putString("result_data", resultJson)
                }
                
                s2sService?.invokeAsync(appId, 1002, params, // 1002 = UPGRADE_RESULT_SERVICE_HASH
                    object : IAsyncResultCallback.Stub() {
                        override fun onResult(bundle: Bundle?) {
                            LogUtils.i(TAG, "Task result reported: $taskId -> $success")
                        }
                    })
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error reporting task result: ${e.message}")
            }
        }
    }
    
    /**
     * 取消订阅升级任务
     */
    fun unsubscribe() {
        try {
            if (isServiceConnected) {
                s2sService?.unregisterS2SSignalListener(appId)
                LogUtils.i(TAG, "Unregistered upgrade task listener for appId: $appId")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error unregistering listener: ${e.message}")
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            unsubscribe()
            if (isServiceConnected) {
                context.unbindService(serviceConnection)
                isServiceConnected = false
            }
            LogUtils.i(TAG, "UpgradeTaskClient cleanup completed")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error during cleanup: ${e.message}")
        }
    }
}

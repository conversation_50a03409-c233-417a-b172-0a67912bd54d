package com.seres.dds.upgrade

import android.content.Context
import com.google.gson.Gson
import com.seres.dds.server.api.UpgradeTaskInfo
import com.seres.dds.server.api.UpgradeTaskStatus
import com.seres.dds.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors

/**
 * 升级任务管理器
 * 负责处理升级任务的拷贝和分发
 */
class UpgradeTaskManager private constructor(private val context: Context) {
    
    private val TAG = "UpgradeTaskManager"
    private val threadPool = Executors.newCachedThreadPool()
    private val gson = Gson()
    
    // 活跃任务列表
    private val activeTasks = ConcurrentHashMap<String, UpgradeTaskInfo>()
    
    // DDS发布者
    private var upgradeTaskPublisher: UpgradeTaskPublisher? = null
    
    companion object {
        @Volatile
        private var INSTANCE: UpgradeTaskManager? = null
        
        fun getInstance(context: Context): UpgradeTaskManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UpgradeTaskManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    init {
        upgradeTaskPublisher = UpgradeTaskPublisher(context)
        LogUtils.i(TAG, "UpgradeTaskManager initialized")
    }
    
    /**
     * 处理升级任务
     */
    fun processUpgradeTask(task: UpgradeTaskInfo) {
        LogUtils.i(TAG, "Processing upgrade task: ${task.taskId}")
        
        activeTasks[task.taskId] = task
        
        threadPool.submit {
            try {
                // 1. 更新任务状态为拷贝中
                updateTaskStatus(task, UpgradeTaskStatus.COPYING)
                
                // 2. 拷贝文件到目标目录
                val success = copyUpgradeFiles(task)
                
                if (success) {
                    // 3. 生成JSON任务文件
                    generateTaskJson(task)
                    
                    // 4. 更新任务状态为准备就绪
                    updateTaskStatus(task, UpgradeTaskStatus.READY)
                    
                    // 5. 通过DDS分发任务
                    distributeTask(task)
                    
                    // 6. 更新任务状态为分发中
                    updateTaskStatus(task, UpgradeTaskStatus.DISTRIBUTING)
                    
                    LogUtils.i(TAG, "Upgrade task processed successfully: ${task.taskId}")
                } else {
                    updateTaskStatus(task, UpgradeTaskStatus.FAILED)
                    LogUtils.e(TAG, "Failed to copy upgrade files for task: ${task.taskId}")
                }
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error processing upgrade task ${task.taskId}: ${e.message}")
                updateTaskStatus(task, UpgradeTaskStatus.FAILED)
            }
        }
    }
    
    /**
     * 拷贝升级文件到目标目录
     */
    private fun copyUpgradeFiles(task: UpgradeTaskInfo): Boolean {
        return try {
            val targetDir = File(task.targetPath, task.taskId)
            if (!targetDir.exists()) {
                targetDir.mkdirs()
            }
            
            var copiedSize = 0L
            val totalSize = task.totalSize
            
            for (packageInfo in task.packageList) {
                val sourceFile = File(packageInfo.packagePath)
                val targetFile = File(targetDir, sourceFile.name)
                
                LogUtils.d(TAG, "Copying ${sourceFile.name} to ${targetFile.absolutePath}")
                
                // 拷贝文件
                FileInputStream(sourceFile).use { input ->
                    FileOutputStream(targetFile).use { output ->
                        val buffer = ByteArray(8192)
                        var bytesRead: Int
                        while (input.read(buffer).also { bytesRead = it } != -1) {
                            output.write(buffer, 0, bytesRead)
                            copiedSize += bytesRead
                            
                            // 可以在这里更新拷贝进度
                            val progress = (copiedSize * 100 / totalSize).toInt()
                            LogUtils.d(TAG, "Copy progress: $progress%")
                        }
                    }
                }
                
                // 更新包路径为目标路径
                packageInfo.packagePath = targetFile.absolutePath
                
                // 验证文件完整性
                if (!verifyFileIntegrity(targetFile, packageInfo.checksum)) {
                    LogUtils.e(TAG, "File integrity check failed: ${targetFile.name}")
                    return false
                }
            }
            
            // 更新任务目标路径
            task.targetPath = targetDir.absolutePath
            
            LogUtils.i(TAG, "All files copied successfully for task: ${task.taskId}")
            true
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error copying upgrade files: ${e.message}")
            false
        }
    }
    
    /**
     * 验证文件完整性
     */
    private fun verifyFileIntegrity(file: File, expectedChecksum: String): Boolean {
        if (expectedChecksum.isEmpty()) {
            return true // 如果没有校验和，跳过验证
        }
        
        return try {
            val analyzer = UpgradePackageAnalyzer()
            val actualChecksum = analyzer.calculateChecksum(file)
            val isValid = actualChecksum.equals(expectedChecksum, ignoreCase = true)
            
            if (!isValid) {
                LogUtils.w(TAG, "Checksum mismatch for ${file.name}: expected=$expectedChecksum, actual=$actualChecksum")
            }
            
            isValid
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error verifying file integrity: ${e.message}")
            false
        }
    }
    
    /**
     * 生成任务JSON文件
     */
    private fun generateTaskJson(task: UpgradeTaskInfo) {
        try {
            val taskJsonFile = File(task.targetPath, "upgrade_task.json")
            val taskJson = gson.toJson(task)
            taskJsonFile.writeText(taskJson)
            
            LogUtils.i(TAG, "Generated task JSON file: ${taskJsonFile.absolutePath}")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error generating task JSON: ${e.message}")
        }
    }
    
    /**
     * 通过DDS分发任务
     */
    private fun distributeTask(task: UpgradeTaskInfo) {
        try {
            upgradeTaskPublisher?.publishUpgradeTask(task)
            LogUtils.i(TAG, "Upgrade task distributed via DDS: ${task.taskId}")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error distributing task via DDS: ${e.message}")
        }
    }
    
    /**
     * 更新任务状态
     */
    private fun updateTaskStatus(task: UpgradeTaskInfo, status: UpgradeTaskStatus) {
        task.taskStatus = status
        activeTasks[task.taskId] = task
        
        // 通过DDS发布状态更新
        upgradeTaskPublisher?.publishTaskStatus(task)
        
        LogUtils.i(TAG, "Task ${task.taskId} status updated to: $status")
    }
    
    /**
     * 根据USB路径取消任务
     */
    fun cancelTasksByUsbPath(usbPath: String) {
        LogUtils.i(TAG, "Cancelling tasks for USB path: $usbPath")
        
        val tasksToCancel = activeTasks.values.filter { it.usbPath == usbPath }
        
        for (task in tasksToCancel) {
            updateTaskStatus(task, UpgradeTaskStatus.CANCELLED)
            activeTasks.remove(task.taskId)
            LogUtils.i(TAG, "Cancelled task: ${task.taskId}")
        }
    }
    
    /**
     * 获取活跃任务列表
     */
    fun getActiveTasks(): List<UpgradeTaskInfo> {
        return activeTasks.values.toList()
    }
    
    /**
     * 获取指定任务
     */
    fun getTask(taskId: String): UpgradeTaskInfo? {
        return activeTasks[taskId]
    }
}

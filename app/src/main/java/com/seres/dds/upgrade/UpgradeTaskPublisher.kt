package com.seres.dds.upgrade

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.google.gson.Gson
import com.seres.dds.server.api.UpgradeTaskInfo
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IAsyncResultCallback
import seres.s2s.internal.IS2SService
import java.util.concurrent.Executors

/**
 * 升级任务发布者
 * 负责通过S2S服务发布升级任务和状态更新
 */
class UpgradeTaskPublisher(private val context: Context) {

    private val TAG = "UpgradeTaskPublisher"
    private val gson = Gson()
    private val threadPool = Executors.newCachedThreadPool()

    // S2S服务相关
    private var s2sService: IS2SService? = null
    private var isServiceConnected = false

    // 升级任务相关常量
    companion object {
        const val UPGRADE_TASK_SERVICE_HASH = 1000 // 升级任务服务Hash ID
        const val UPGRADE_STATUS_SERVICE_HASH = 1001 // 升级状态服务Hash ID
        const val UPGRADE_APP_ID = 999 // 升级服务的App ID
    }

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            s2sService = IS2SService.Stub.asInterface(service)
            isServiceConnected = true
            LogUtils.i(TAG, "Connected to S2S service")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            s2sService = null
            isServiceConnected = false
            LogUtils.w(TAG, "Disconnected from S2S service")
        }
    }

    init {
        connectToS2SService()
        LogUtils.i(TAG, "UpgradeTaskPublisher initialized")
    }
    
    /**
     * 连接到S2S服务
     */
    private fun connectToS2SService() {
        try {
            val intent = Intent("com.example.remote.action").apply {
                setPackage("com.seres.dds")
            }
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
            LogUtils.i(TAG, "Binding to S2S service")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Failed to bind S2S service: ${e.message}")
        }
    }

    /**
     * 发布升级任务
     */
    fun publishUpgradeTask(taskInfo: UpgradeTaskInfo) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Publishing upgrade task: ${taskInfo.taskId}")

                if (!isServiceConnected) {
                    LogUtils.w(TAG, "S2S service not connected, attempting to reconnect")
                    connectToS2SService()
                    Thread.sleep(1000) // 等待连接
                }

                if (isServiceConnected) {
                    // 将任务信息转换为JSON字符串
                    val taskJson = gson.toJson(taskInfo)

                    // 创建参数Bundle
                    val params = Bundle().apply {
                        putString("action", "publish_upgrade_task")
                        putString("task_data", taskJson)
                        putLong("timestamp", System.currentTimeMillis())
                    }

                    // 通过S2S服务发布任务
                    s2sService?.invokeAsync(UPGRADE_APP_ID, UPGRADE_TASK_SERVICE_HASH, params,
                        object : IAsyncResultCallback.Stub() {
                            override fun onResult(bundle: Bundle?) {
                                val success = bundle?.getBoolean("success", false) ?: false
                                if (success) {
                                    LogUtils.i(TAG, "Upgrade task published successfully: ${taskInfo.taskId}")
                                } else {
                                    val error = bundle?.getString("error", "Unknown error")
                                    LogUtils.e(TAG, "Failed to publish upgrade task: $error")
                                }
                            }
                        })
                } else {
                    LogUtils.e(TAG, "Cannot publish task: S2S service not connected")
                }

            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing upgrade task: ${e.message}")
            }
        }
    }
    
    /**
     * 发布任务状态更新
     */
    fun publishTaskStatus(taskInfo: UpgradeTaskInfo) {
        threadPool.submit {
            try {
                LogUtils.d(TAG, "Publishing task status update: ${taskInfo.taskId} -> ${taskInfo.taskStatus}")

                if (!isServiceConnected) {
                    LogUtils.w(TAG, "S2S service not connected for status update")
                    return@submit
                }

                // 将状态信息转换为JSON字符串
                val statusData = mapOf(
                    "taskId" to taskInfo.taskId,
                    "taskStatus" to taskInfo.taskStatus.name,
                    "description" to taskInfo.description,
                    "timestamp" to System.currentTimeMillis()
                )
                val statusJson = gson.toJson(statusData)

                // 创建参数Bundle
                val params = Bundle().apply {
                    putString("action", "update_task_status")
                    putString("status_data", statusJson)
                    putLong("timestamp", System.currentTimeMillis())
                }

                // 通过S2S服务发布状态更新
                s2sService?.invokeAsync(UPGRADE_APP_ID, UPGRADE_STATUS_SERVICE_HASH, params,
                    object : IAsyncResultCallback.Stub() {
                        override fun onResult(bundle: Bundle?) {
                            val success = bundle?.getBoolean("success", false) ?: false
                            if (success) {
                                LogUtils.d(TAG, "Task status published successfully: ${taskInfo.taskId}")
                            } else {
                                val error = bundle?.getString("error", "Unknown error")
                                LogUtils.e(TAG, "Failed to publish task status: $error")
                            }
                        }
                    })

            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing task status: ${e.message}")
            }
        }
    }
    
    /**
     * 发布任务完成通知
     */
    fun publishTaskCompleted(taskId: String) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Publishing task completed notification: $taskId")

                if (!isServiceConnected) {
                    LogUtils.w(TAG, "S2S service not connected for completion notification")
                    return@submit
                }

                val completionData = mapOf(
                    "taskId" to taskId,
                    "status" to "COMPLETED",
                    "message" to "Task completed successfully",
                    "timestamp" to System.currentTimeMillis()
                )
                val completionJson = gson.toJson(completionData)

                val params = Bundle().apply {
                    putString("action", "task_completed")
                    putString("completion_data", completionJson)
                }

                s2sService?.invokeAsync(UPGRADE_APP_ID, UPGRADE_STATUS_SERVICE_HASH, params,
                    object : IAsyncResultCallback.Stub() {
                        override fun onResult(bundle: Bundle?) {
                            LogUtils.i(TAG, "Task completed notification sent: $taskId")
                        }
                    })

            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing task completed notification: ${e.message}")
            }
        }
    }
    
    /**
     * 发布任务失败通知
     */
    fun publishTaskFailed(taskId: String, errorMessage: String) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Publishing task failed notification: $taskId")

                if (!isServiceConnected) {
                    LogUtils.w(TAG, "S2S service not connected for failure notification")
                    return@submit
                }

                val failureData = mapOf(
                    "taskId" to taskId,
                    "status" to "FAILED",
                    "message" to "Task failed: $errorMessage",
                    "timestamp" to System.currentTimeMillis()
                )
                val failureJson = gson.toJson(failureData)

                val params = Bundle().apply {
                    putString("action", "task_failed")
                    putString("failure_data", failureJson)
                }

                s2sService?.invokeAsync(UPGRADE_APP_ID, UPGRADE_STATUS_SERVICE_HASH, params,
                    object : IAsyncResultCallback.Stub() {
                        override fun onResult(bundle: Bundle?) {
                            LogUtils.i(TAG, "Task failed notification sent: $taskId")
                        }
                    })

            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing task failed notification: ${e.message}")
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            if (isServiceConnected) {
                context.unbindService(serviceConnection)
                isServiceConnected = false
            }
            LogUtils.i(TAG, "UpgradeTaskPublisher cleanup completed")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error during cleanup: ${e.message}")
        }
    }
}

package com.seres.dds.upgrade

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.os.IBinder
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import com.seres.dds.utils.LogUtils
import java.io.File
import java.util.concurrent.Executors

/**
 * U盘检测服务
 * 监听U盘插入/拔出事件，检测升级包
 */
class UsbDetectionService : Service() {
    
    private val TAG = "UsbDetectionService"
    private val threadPool = Executors.newCachedThreadPool()
    private var upgradeTaskManager: UpgradeTaskManager? = null
    private var storageStatusManager: StorageStatusManager? = null

    companion object {
        const val ACTION_STORAGE_STATUS_CHANGED = "com.seres.dds.STORAGE_STATUS_CHANGED"
        const val EXTRA_STORAGE_DEVICES = "storage_devices"
        const val EXTRA_USB_CONNECTED = "usb_connected"
        const val EXTRA_USB_PATH = "usb_path"
    }
    
    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_MEDIA_MOUNTED -> {
                    val path = intent.data?.path
                    LogUtils.i(TAG, "USB mounted: $path")
                    path?.let { handleUsbMounted(it) }
                }
                Intent.ACTION_MEDIA_UNMOUNTED -> {
                    val path = intent.data?.path
                    LogUtils.i(TAG, "USB unmounted: $path")
                    path?.let { handleUsbUnmounted(it) }
                }
                Intent.ACTION_MEDIA_REMOVED -> {
                    val path = intent.data?.path
                    LogUtils.i(TAG, "USB removed: $path")
                    path?.let { handleUsbUnmounted(it) }
                }
            }
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "UsbDetectionService onCreate")

        upgradeTaskManager = UpgradeTaskManager.getInstance(this)
        storageStatusManager = StorageStatusManager(this)

        // 设置存储状态监听器
        setupStorageStatusListener()

        // 注册USB事件监听
        registerUsbReceiver()

        // 检查已挂载的USB设备
        checkExistingUsbDevices()

        // 初始广播存储状态
        broadcastStorageStatus()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "UsbDetectionService onStartCommand")
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "UsbDetectionService onDestroy")
        unregisterReceiver(usbReceiver)
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun registerUsbReceiver() {
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_MEDIA_MOUNTED)
            addAction(Intent.ACTION_MEDIA_UNMOUNTED)
            addAction(Intent.ACTION_MEDIA_REMOVED)
            addAction(Intent.ACTION_MEDIA_EJECT)
            addDataScheme("file")
        }
        registerReceiver(usbReceiver, filter)
        LogUtils.i(TAG, "USB receiver registered")
    }
    
    private fun checkExistingUsbDevices() {
        threadPool.submit {
            try {
                val storageManager = getSystemService(Context.STORAGE_SERVICE) as StorageManager
                val volumes = storageManager.storageVolumes
                
                for (volume in volumes) {
                    if (volume.isRemovable && volume.state == "mounted") {
                        val path = getVolumePath(volume)
                        path?.let { 
                            LogUtils.i(TAG, "Found existing USB device: $it")
                            handleUsbMounted(it)
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error checking existing USB devices: ${e.message}")
            }
        }
    }
    
    private fun getVolumePath(volume: StorageVolume): String? {
        return try {
            // 使用反射获取路径，因为getPath()在API 30+中被隐藏
            val getPathMethod = volume.javaClass.getMethod("getPath")
            getPathMethod.invoke(volume) as String
        } catch (e: Exception) {
            LogUtils.w(TAG, "Failed to get volume path: ${e.message}")
            null
        }
    }
    
    /**
     * 设置存储状态监听器
     */
    private fun setupStorageStatusListener() {
        storageStatusManager?.setStatusListener(object : StorageStatusManager.StorageStatusListener {
            override fun onStorageDeviceAdded(device: StorageStatusManager.StorageDeviceInfo) {
                LogUtils.i(TAG, "Storage device added: ${device.description} at ${device.path}")
                broadcastStorageStatus()
            }

            override fun onStorageDeviceRemoved(path: String) {
                LogUtils.i(TAG, "Storage device removed: $path")
                broadcastStorageStatus()
            }

            override fun onStorageStatusChanged(devices: List<StorageStatusManager.StorageDeviceInfo>) {
                LogUtils.d(TAG, "Storage status changed, ${devices.size} devices")
                broadcastStorageStatus()
            }
        })
    }

    /**
     * 广播存储状态变化
     */
    private fun broadcastStorageStatus() {
        try {
            val devices = storageStatusManager?.getAllStorageDevices() ?: emptyList()
            val usbConnected = storageStatusManager?.hasUsbDeviceConnected() ?: false
            val usbPath = storageStatusManager?.getFirstUsbDevicePath()

            val intent = Intent(ACTION_STORAGE_STATUS_CHANGED).apply {
                putExtra(EXTRA_USB_CONNECTED, usbConnected)
                putExtra(EXTRA_USB_PATH, usbPath)
                // 注意：这里简化处理，实际项目中可能需要序列化设备列表
                putExtra(EXTRA_STORAGE_DEVICES, devices.size)
            }

            sendBroadcast(intent)
            LogUtils.d(TAG, "Broadcasted storage status: USB=$usbConnected, devices=${devices.size}")

        } catch (e: Exception) {
            LogUtils.e(TAG, "Error broadcasting storage status: ${e.message}")
        }
    }

    private fun handleUsbMounted(path: String) {
        LogUtils.i(TAG, "Handling USB mounted: $path")

        // 刷新存储状态
        storageStatusManager?.refreshStorageStatus()

        threadPool.submit {
            try {
                val usbDir = File(path)
                if (usbDir.exists() && usbDir.isDirectory) {
                    // 检测升级包
                    val packageAnalyzer = UpgradePackageAnalyzer()
                    val upgradeTask = packageAnalyzer.analyzeUpgradePackages(usbDir)

                    if (upgradeTask != null && upgradeTask.packageList.isNotEmpty()) {
                        LogUtils.i(TAG, "Found upgrade packages: ${upgradeTask.packageList.size}")

                        // 处理升级任务
                        upgradeTaskManager?.processUpgradeTask(upgradeTask)
                    } else {
                        LogUtils.i(TAG, "No upgrade packages found in USB: $path")
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error handling USB mounted: ${e.message}")
            }
        }
    }
    
    private fun handleUsbUnmounted(path: String) {
        LogUtils.i(TAG, "Handling USB unmounted: $path")

        // 刷新存储状态
        storageStatusManager?.refreshStorageStatus()

        threadPool.submit {
            try {
                // 取消相关的升级任务
                upgradeTaskManager?.cancelTasksByUsbPath(path)
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error handling USB unmounted: ${e.message}")
            }
        }
    }
}

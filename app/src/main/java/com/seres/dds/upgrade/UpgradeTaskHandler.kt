package com.seres.dds.upgrade

import android.os.Bundle
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.seres.dds.commsvc.DomainContext
import com.seres.dds.sdk.DataWriter
import com.seres.dds.sdk.KScomNativeLib.kScomGetStatueMask
import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.core.StatusMask.Companion.publication_matched
import com.seres.dds.server.api.UpgradePackageInfo
import com.seres.dds.server.api.UpgradeTaskInfo
import com.seres.dds.server.api.UpgradeTaskStatus
import com.seres.dds.utils.LogUtils
import com.seres.upgrade.UpgradeTask_Status
import java.util.concurrent.Executors

/**
 * 升级任务处理器
 * 在S2S服务中处理升级任务的转发
 */
object UpgradeTaskHandler {
    
    private val TAG = "UpgradeTaskHandler"
    private val gson = Gson()
    private val threadPool = Executors.newCachedThreadPool()
    
    // DDS发布者组件
    private val topicName = "UPGRADE_TASK_STATUS"
    private val dataType = UpgradeTask_Status()
    private val topic: Topic by lazy { Topic(DomainContext.dp(), topicName, dataType) }
    private val writer: DataWriter by lazy { DataWriter(DomainContext.dp(), topic) }
    
    private var isInitialized = false
    
    /**
     * 初始化DDS发布者
     */
    private fun initializeDDSPublisher() {
        if (!isInitialized) {
            kScomSetStatusMask(writer.ref, publication_matched().toLong())
            isInitialized = true
            LogUtils.i(TAG, "DDS publisher initialized for topic: $topicName")
        }
    }
    
    /**
     * 处理升级任务发布请求
     */
    fun handleUpgradeTaskPublish(params: Bundle?): Bundle {
        val result = Bundle()
        
        try {
            val action = params?.getString("action")
            LogUtils.i(TAG, "Handling upgrade task action: $action")
            
            when (action) {
                "publish_upgrade_task" -> {
                    val taskData = params.getString("task_data")
                    if (taskData != null) {
                        publishUpgradeTaskViaDDS(taskData)
                        result.putBoolean("success", true)
                        result.putString("message", "Upgrade task published successfully")
                    } else {
                        result.putBoolean("success", false)
                        result.putString("error", "Missing task data")
                    }
                }
                "update_task_status" -> {
                    val statusData = params.getString("status_data")
                    if (statusData != null) {
                        publishTaskStatusViaDDS(statusData)
                        result.putBoolean("success", true)
                        result.putString("message", "Task status updated successfully")
                    } else {
                        result.putBoolean("success", false)
                        result.putString("error", "Missing status data")
                    }
                }
                "task_completed" -> {
                    val completionData = params.getString("completion_data")
                    if (completionData != null) {
                        publishTaskCompletionViaDDS(completionData)
                        result.putBoolean("success", true)
                        result.putString("message", "Task completion published successfully")
                    } else {
                        result.putBoolean("success", false)
                        result.putString("error", "Missing completion data")
                    }
                }
                "task_failed" -> {
                    val failureData = params.getString("failure_data")
                    if (failureData != null) {
                        publishTaskFailureViaDDS(failureData)
                        result.putBoolean("success", true)
                        result.putString("message", "Task failure published successfully")
                    } else {
                        result.putBoolean("success", false)
                        result.putString("error", "Missing failure data")
                    }
                }
                else -> {
                    result.putBoolean("success", false)
                    result.putString("error", "Unknown action: $action")
                }
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error handling upgrade task request: ${e.message}")
            result.putBoolean("success", false)
            result.putString("error", "Internal error: ${e.message}")
        }
        
        return result
    }
    
    /**
     * 通过DDS发布升级任务
     */
    private fun publishUpgradeTaskViaDDS(taskDataJson: String) {
        threadPool.submit {
            try {
                initializeDDSPublisher()
                
                // 解析任务数据
                val taskInfo = gson.fromJson(taskDataJson, UpgradeTaskInfo::class.java)
                LogUtils.i(TAG, "Publishing upgrade task via DDS: ${taskInfo.taskId}")
                
                // 等待订阅者连接
                waitForSubscribers()
                
                // 转换为DDS数据类型
                val ddsData = convertToUpgradeTaskStatus(taskInfo)
                
                // 发布数据
                writer.write(ddsData)
                
                LogUtils.i(TAG, "Upgrade task published via DDS successfully: ${taskInfo.taskId}")
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing upgrade task via DDS: ${e.message}")
            }
        }
    }
    
    /**
     * 通过DDS发布任务状态更新
     */
    private fun publishTaskStatusViaDDS(statusDataJson: String) {
        threadPool.submit {
            try {
                initializeDDSPublisher()
                
                // 解析状态数据
                val statusData = gson.fromJson(statusDataJson, Map::class.java) as Map<String, Any>
                val taskId = statusData["taskId"] as String
                val taskStatus = statusData["taskStatus"] as String
                val description = statusData["description"] as String
                
                LogUtils.d(TAG, "Publishing task status via DDS: $taskId -> $taskStatus")
                
                waitForSubscribers()
                
                // 创建DDS数据
                val ddsData = UpgradeTask_Status()
                ddsData.set_taskId(taskId)
                ddsData.set_taskStatus(UpgradeTaskStatus.valueOf(taskStatus).ordinal)
                ddsData.set_description(description)
                
                writer.write(ddsData)
                
                LogUtils.d(TAG, "Task status published via DDS successfully: $taskId")
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing task status via DDS: ${e.message}")
            }
        }
    }
    
    /**
     * 通过DDS发布任务完成通知
     */
    private fun publishTaskCompletionViaDDS(completionDataJson: String) {
        threadPool.submit {
            try {
                initializeDDSPublisher()
                
                val completionData = gson.fromJson(completionDataJson, Map::class.java) as Map<String, Any>
                val taskId = completionData["taskId"] as String
                val message = completionData["message"] as String
                
                LogUtils.i(TAG, "Publishing task completion via DDS: $taskId")
                
                waitForSubscribers()
                
                val ddsData = UpgradeTask_Status()
                ddsData.set_taskId(taskId)
                ddsData.set_taskStatus(UpgradeTaskStatus.COMPLETED.ordinal)
                ddsData.set_description(message)
                
                writer.write(ddsData)
                
                LogUtils.i(TAG, "Task completion published via DDS: $taskId")
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing task completion via DDS: ${e.message}")
            }
        }
    }
    
    /**
     * 通过DDS发布任务失败通知
     */
    private fun publishTaskFailureViaDDS(failureDataJson: String) {
        threadPool.submit {
            try {
                initializeDDSPublisher()
                
                val failureData = gson.fromJson(failureDataJson, Map::class.java) as Map<String, Any>
                val taskId = failureData["taskId"] as String
                val message = failureData["message"] as String
                
                LogUtils.i(TAG, "Publishing task failure via DDS: $taskId")
                
                waitForSubscribers()
                
                val ddsData = UpgradeTask_Status()
                ddsData.set_taskId(taskId)
                ddsData.set_taskStatus(UpgradeTaskStatus.FAILED.ordinal)
                ddsData.set_description(message)
                
                writer.write(ddsData)
                
                LogUtils.i(TAG, "Task failure published via DDS: $taskId")
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error publishing task failure via DDS: ${e.message}")
            }
        }
    }
    
    /**
     * 等待订阅者连接
     */
    private fun waitForSubscribers() {
        var status: Long = 0L
        var retryCount = 0
        val maxRetries = 50 // 最多等待5秒
        
        while (((status and publication_matched().toLong()).toInt() == 0) && retryCount < maxRetries) {
            status = kScomGetStatueMask(writer.ref)
            if ((status and publication_matched().toLong()).toInt() == 0) {
                Thread.sleep(100)
                retryCount++
            }
        }
        
        if (retryCount >= maxRetries) {
            LogUtils.w(TAG, "No subscribers found after waiting, publishing anyway")
        } else {
            LogUtils.d(TAG, "Found subscribers, ready to publish")
        }
    }
    
    /**
     * 将UpgradeTaskInfo转换为UpgradeTask_Status
     */
    private fun convertToUpgradeTaskStatus(taskInfo: UpgradeTaskInfo): UpgradeTask_Status {
        val ddsData = UpgradeTask_Status()
        
        ddsData.set_taskId(taskInfo.taskId)
        ddsData.set_taskName(taskInfo.taskName)
        ddsData.set_taskVersion(taskInfo.taskVersion)
        ddsData.set_createTime(taskInfo.createTime)
        ddsData.set_usbPath(taskInfo.usbPath)
        ddsData.set_targetPath(taskInfo.targetPath)
        ddsData.set_totalSize(taskInfo.totalSize)
        ddsData.set_taskStatus(taskInfo.taskStatus.ordinal)
        ddsData.set_description(taskInfo.description)
        ddsData.set_packageCount(taskInfo.packageList.size)
        
        // 将包列表转换为JSON字符串
        val packageListJson = gson.toJson(taskInfo.packageList)
        ddsData.set_packageListJson(packageListJson)
        
        return ddsData
    }
}

package com.seres.dds.commsvc.rpc

import com.seres.dds.commsvc.DomainContext
import com.seres.dds.sdk.ClientParam
import com.seres.dds.utils.LogUtils
import seres.hpcm.HPCM_ControlClient
import seres.zcuf.ZCUF_ControlClient
import seres.zcufr.ZCUFR_ControlClient
import seres.zcur.ZCUR_ControlClient

object ClientManager {
    private const val TAG = "ClientManager"

    private const val serviceNameMcu:String = "HPCM_MCU_Service"
    private lateinit var clientMcu:HPCM_ControlClient
    private var clientMcuReady:Boolean = false

    private val serviceNameZcuR:String = "ZCU_R_Service"
    private lateinit var clientZcuR:ZCUR_ControlClient
    private var clientZcuRReady:Boolean = false

    private val serviceNameZcuF:String = "ZCU_F_Service"
    private lateinit var clientZcuF: ZCUF_ControlClient
    private var clientZcuFReady:Boolean = false

    private val serviceNameZcuFR:String = "ZCU_FR_Service"
    private lateinit var clientZcuFR:ZCUFR_ControlClient
    private var clientZcuFRReady:Boolean = false

    //init client
    fun clientInit()
    {
        clientMcu = createClientHPCM()
        clientMcuReady = true
        LogUtils.d(TAG, "Create client : $serviceNameMcu successful ")

        clientZcuR = createClientZcuR()
        clientZcuRReady = true
        LogUtils.d(TAG, "Create client : $serviceNameZcuR successful ")

        clientZcuF = createClientZcuF()
        clientZcuFReady = true
        LogUtils.d(TAG, "Create client : $serviceNameZcuF successful ")

        clientZcuFR = createClientZcuFR()
        clientZcuFRReady = true
        LogUtils.d(TAG, "Create client : $serviceNameZcuFR successful ")
    }

    private fun createClientHPCM(): HPCM_ControlClient
    {
        val param = ClientParam(DomainContext.dp(), null, null)
        param.set_service_name(serviceNameMcu)

        val client = HPCM_ControlClient(param)

        return client
    }

    private fun createClientZcuR():ZCUR_ControlClient
    {
        val param = ClientParam(DomainContext.dp(), null, null)
        param.set_service_name(serviceNameZcuR)

        val client = ZCUR_ControlClient(param)

        return client
    }

    private fun createClientZcuF():ZCUF_ControlClient
    {
        val param = ClientParam(DomainContext.dp(), null, null)
        param.set_service_name(serviceNameZcuF)

        val client = ZCUF_ControlClient(param)

        return client
    }

    private fun createClientZcuFR():ZCUFR_ControlClient
    {
        val param = ClientParam(DomainContext.dp(), null, null)
        param.set_service_name(serviceNameZcuFR)

        val client = ZCUFR_ControlClient(param)

        return client
    }

    fun getClientHPCM():HPCM_ControlClient{
        return clientMcu
    }

    fun getClientZcuFR():ZCUFR_ControlClient{
        return clientZcuFR
    }

    fun getClientZcuR():ZCUR_ControlClient{
        return clientZcuR
    }

    fun getClientZcuF():ZCUF_ControlClient{
        return clientZcuF
    }

    fun checkClientStatus(serviceName:String) : Boolean{
        var ret : Boolean = false

        when(serviceName){
            serviceNameMcu -> ret =  clientMcuReady
            serviceNameZcuR -> ret = clientZcuRReady
            serviceNameZcuF ->  ret = clientZcuFReady
            serviceNameZcuFR -> ret = clientZcuFRReady
        }

        return  ret
    }
}
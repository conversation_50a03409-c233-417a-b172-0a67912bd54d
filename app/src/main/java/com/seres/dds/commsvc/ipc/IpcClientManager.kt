package com.seres.dds.commsvc.ipc

import android.os.Bundle
import com.seres.dds.database.DataBase.dbGetChangeNodeInfo
import com.seres.dds.database.DataBase.subscribeNode
import com.seres.dds.server.consts.InvokeConsts
import com.seres.dds.server.consts.SignalHash
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IS2SReportListener
import java.util.concurrent.ConcurrentHashMap

object IpcClientManager {
    private const val TAG = "IpcClientManager"
    private val dataListenerMap = ConcurrentHashMap<Int, IS2SReportListener>()
    private val bundleMap = ConcurrentHashMap<Int,ArrayList<Bundle>>()
    
    fun listenerRegister(appID:Int, listener: IS2SReportListener, array:IntArray?){
        dataListenerMap[appID] = listener
        bundleMap[appID] = ArrayList<Bundle>()
        
        LogUtils.i(TAG,"Register app[${appID}] to S2sService")
        subscribeNode(appID,array)
        
    }

    fun listenerUnregister(appID:Int){
        dataListenerMap.remove(appID)
        bundleMap.remove(appID)
        LogUtils.i(TAG,"Unregister app[${appID}] to S2sService")
    }
    
    fun pushData(appID:Int,bundle: Bundle){
        if(bundleMap[appID] != null)
        {
            bundleMap[appID]?.add(bundle)
            LogUtils.i(TAG, "push bundle to app[${appID}]")
        }
        else
        {
            LogUtils.e(TAG, "app[id] unregister")
        }
    }
    
    fun report(){
        while (true){
            Thread.sleep(50)
            
            //Clear the list cache corresponding to all apps
            bundleMap.forEach{(_,list) ->
                list.clear()
            }
            
            //Traverse all the signals that have changed
            for(key in enumValues<SignalHash>())
            {
                dbGetChangeNodeInfo(key.ordinal)
            }
            
            //report
            bundleMap.forEach{(id,list) ->
                if(list.isNotEmpty())
                {
                    val bundle = Bundle()
                    bundle.putParcelableArray(InvokeConsts.KEY_CHANGE_PROP_LIST,list.toTypedArray())
                    dataListenerMap[id]?.notify(bundle)
                }
            }
        }
        
    }
}
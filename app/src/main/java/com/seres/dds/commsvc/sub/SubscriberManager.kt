package com.seres.dds.commsvc.sub

import com.seres.dds.utils.LogUtils
import com.seres.dds.commsvc.DomainContext
import com.seres.dds.database.DataBase
import com.seres.dds.sdk.DataReader
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.TypeBase
import com.seres.dds.server.consts.SignalHash
import com.seres.dds.threadpool.ThreadPool
import seres.hpcm.HPCM_Status
import seres.zcuf.ZCUF_Status
import seres.zcufr.ZCUFR_Status
import seres.zcur.ZCUR_Status

object SubscriberManager{
    private val tag = "SubscriberManager"
    private val topicNameHPCM:String = "HPCM_MCU_STATUS"
    private val dataTypeHPCM:TypeBase = HPCM_Status()
    private lateinit var topicHPCM : Topic
    private lateinit var readerHPCM : DataReader

    private val topicNameZCUFR:String = "ZCU_FR_STATUS"
    private val dataTypeZCUFR: ZCUFR_Status = ZCUFR_Status()
    private lateinit var topicZCUFR : Topic
    private lateinit var readerZCUFR : DataReader

    private val topicNameZCUF:String = "ZCU_F_STATUS"
    private val dataTypeZCUF: ZCUF_Status = ZCUF_Status()
    private lateinit var topicZCUF : Topic
    private lateinit var readerZCUF : DataReader

    private val topicNameZCUR:String = "ZCU_R_STATUS"
    private val dataTypeZCUR: ZCUR_Status = ZCUR_Status()
    private lateinit var topicZCUR : Topic
    private lateinit var readerZCUR : DataReader

    //创建4个sub
    fun subscriberInit(){
        topicHPCM = Topic(DomainContext.dp(), topicNameHPCM, dataTypeHPCM)
        readerHPCM = DataReader(DomainContext.dp(), topicHPCM)

        topicZCUFR = Topic(DomainContext.dp(), topicNameZCUFR, dataTypeZCUFR)
        readerZCUFR = DataReader(DomainContext.dp(), topicZCUFR)

        topicZCUF = Topic(DomainContext.dp(), topicNameZCUF, dataTypeZCUF)
        readerZCUF = DataReader(DomainContext.dp(), topicZCUF)

        topicZCUR = Topic(DomainContext.dp(), topicNameZCUR, dataTypeZCUR)
        readerZCUR = DataReader(DomainContext.dp(), topicZCUR)
        LogUtils.i(tag,"Create sub success")
    }

    private fun updateHpcmStatus(){
        val task = Runnable {
            while (true){
                val samples = readerHPCM.take()
                samples.sample_list!!.forEach{ sample ->
                    val data = sample.type as HPCM_Status

//                    LogUtils.d(tag,"data.get_mainXDirFL: "+ data.get_mainXDirFL())
//                    DataBase.setNodeValue("mainXDirFL",data.get_mainXDirFL())
//                    LogUtils.d(tag,"data.get_mainXDirFR: "+ data.get_mainXDirFR())
//                    DataBase.setNodeValue("mainXDirFR",data.get_mainXDirFR())
//                    LogUtils.d(tag,"data.get_heatingLevelFL: " + data.get_heatingLevelFL())
//                    LogUtils.d(tag,"data.get_heatingStatusFR: " + data.get_heatingStatusFR())
//                    LogUtils.d(tag,"data.get_heatingLevelFR: " + data.get_heatingLevelFR())
//                    LogUtils.d(tag,"data.get_heatingLevelFL: " + data.get_heatingLevelFL())
//                    LogUtils.d(tag,"data.get_heatingStatusFL: " + data.get_heatingStatusFL())
//                    LogUtils.d(tag,"data.get_ventilatingStatusFL: "+ data.get_ventilatingStatusFL())
//                    LogUtils.d(tag,"data.get_ventilatingStatusFR: " + data.get_ventilatingStatusFR())
//                    LogUtils.d(tag,"data.get_ventilationFL: "+ data.get_ventilationFL())
//                    LogUtils.d(tag,"data.get_ventilationFR: " + data.get_ventilationFR())
//                    LogUtils.d(tag,"data.get_seat_FL_XActuateStatus(): " +data.get_seat_FL_XActuateStatus())
//                    LogUtils.d(tag,"data.get_seat_FR_XActuateStatus(): " +data.get_seat_FR_XActuateStatus())
                }

                Thread.sleep(20) //接收周期暂定20ms
            }
        }

        ThreadPool.submitTask(task)
    }

    private fun updateZcuFRStatus(){
        val task = Runnable {
            while (true){
                val samplesZCUFR = readerZCUFR.take()
                samplesZCUFR.sample_list!!.forEach{ sample ->
                    val data = sample.type as ZCUFR_Status

                    LogUtils.d(tag,"data.get_heatingLevelFL: " + data.get_heatingLevelFL())
                    LogUtils.d(tag,"data.get_heatingStatusFR: " + data.get_heatingStatusFR())
                    LogUtils.d(tag,"data.get_heatingLevelFR: " + data.get_heatingLevelFR())
                    LogUtils.d(tag,"data.get_heatingLevelFL: " + data.get_heatingLevelFL())
                    LogUtils.d(tag,"data.get_heatingStatusFL: " + data.get_heatingStatusFL())
                    LogUtils.d(tag,"data.get_ventilatingStatusFL: "+ data.get_ventilatingStatusFL())
                    LogUtils.d(tag,"data.get_ventilatingStatusFR: " + data.get_ventilatingStatusFR())
                    LogUtils.d(tag,"data.get_positionFR()"+data.get_positionFR())
                    LogUtils.d(tag,"data.get_actuateStatusFL()"+data.get_actuateStatusFL())
                    LogUtils.d(tag,"data.get_actuateStatusFR()"+data.get_actuateStatusFR())
                }

                Thread.sleep(20)
            }
        }
        ThreadPool.submitTask(task)
    }

    private fun updateZcuFStatus(){
        val task = Runnable {
            while (true) {
                val samplesZCUF = readerZCUF.take()
                samplesZCUF.sample_list!!.forEach { sample ->
                    val data = sample.type as ZCUF_Status

                    LogUtils.d(tag,"data.get_commonlight_hood: " + data.get_commonlight_hood())
                }

                Thread.sleep(20)
            }
        }
        ThreadPool.submitTask(task)
    }

    private fun updateZcuRStatus(){
        LogUtils.d(tag,"updateZcuRStatus run")
        val task = Runnable{
            while (true)
            {
                val samplesZCUR = readerZCUR.take()
                samplesZCUR.sample_list!!.forEach{ sample ->
                    val data = sample.type as ZCUR_Status

                    LogUtils.d(tag,"data.get_commonlight_trunk_lightstatus: " + data.get_commonlight_trunk_lightstatus())
                    DataBase.dbSetNodeValue(SignalHash.commonlight_trunk_lightstatus_Hash.ordinal, data.get_commonlight_trunk_lightstatus())
                }

                Thread.sleep(20)
            }
        }

        ThreadPool.submitTask(task)
    }

    //4个sub同时接收数据，作为回调
    fun subReceive(){
        updateHpcmStatus()
        updateZcuFRStatus()
        updateZcuFStatus()
        updateZcuRStatus()
    }
}
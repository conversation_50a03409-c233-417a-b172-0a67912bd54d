package com.seres.dds

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.GsonBuilder
import com.seres.dds.databinding.ActivityMainBinding
import com.seres.dds.testdemo.example.ClientService
import com.seres.dds.example.IClientService
import com.seres.dds.testdemo.hpcm_demo.pubsub.pubMain
import com.seres.dds.s2sservicesdk.S2sServiceSDK
import com.seres.dds.testdemo.example.SubService
import com.seres.dds.upgrade.UpgradeTaskManager
import com.seres.dds.upgrade.UpgradeTaskSubscriber
import com.seres.dds.upgrade.UpgradeTaskClient
import com.seres.dds.upgrade.UpgradeTaskListener
import com.seres.dds.upgrade.StorageStatusManager
import com.seres.dds.upgrade.StorageDeviceAdapter
import com.seres.dds.upgrade.UsbDetectionService
import com.seres.dds.server.api.UpgradeTaskInfo
import com.seres.dds.utils.BcmStatusUtils
import com.seres.dds.utils.LogUtils
import com.seres.dds.testdemo.zcufr_demo.rpc.ServerDemo
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

class MainActivity : AppCompatActivity() {
    private var binding: ActivityMainBinding? = null
    private val threadPool = Executors.newCachedThreadPool()
    private var serverDemo: ServerDemo? = null
    private var clientService: IClientService? = null
    private val gson = GsonBuilder().setPrettyPrinting().create()
    private var upgradeTaskSubscriber: UpgradeTaskSubscriber? = null
    private var upgradeTaskClient: UpgradeTaskClient? = null
    private var storageStatusManager: StorageStatusManager? = null
    private var storageDeviceAdapter: StorageDeviceAdapter? = null

    // 存储状态广播接收器
    private val storageStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                UsbDetectionService.ACTION_STORAGE_STATUS_CHANGED -> {
                    val usbConnected = intent.getBooleanExtra(UsbDetectionService.EXTRA_USB_CONNECTED, false)
                    val usbPath = intent.getStringExtra(UsbDetectionService.EXTRA_USB_PATH)

                    LogUtils.i(TAG, "Storage status changed: USB=$usbConnected, path=$usbPath")
                    updateStorageStatus()
                    updateUsbStatus(usbConnected, usbPath)
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化S2s所有服务
        // S2sServiceSDK.init(this)
        
        binding = ActivityMainBinding.inflate(getLayoutInflater())
        setContentView(binding?.root)
        enableEdgeToEdge()
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        initView()
        initStorageStatus()
        registerStorageStatusReceiver()
    }
    
    private fun initView() {
        binding?.btnStartSubscriber?.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View?) {
                threadPool.submit(object : Runnable {
                    override fun run() {
                        LogUtils.i(TAG, "Click sub main")
                        val intent = Intent(this@MainActivity, SubService::class.java)
                        startService(intent)
                    }
                })
            }
        })
        binding?.btnStartPublisher?.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View?) {
                threadPool.submit(object : Runnable {
                    override fun run() {
                        LogUtils.i(TAG, "Click pub main")
//                        test.pubMain()
//                        pubMain()
                        pubMain()
                    }
                })
            }
        })
        binding?.btnStartClient?.setOnClickListener(object : View.OnClickListener {
            override fun onClick(p0: View?) {
                LogUtils.i(TAG, "Click client start")
                val intent = Intent(this@MainActivity, ClientService::class.java)
                startService(intent)
            }
        })
        binding?.btnStartServer?.setOnClickListener {
            threadPool.submit(object : Runnable {
                override fun run() {
                    LogUtils.i(TAG, "Click server start")
                    serverDemo = ServerDemo()
                    serverDemo?.start()
                }
            })
        }
        
        binding?.btnStartPrintStatus?.setOnClickListener {
            startPrintBcmStatus()
        }
        binding?.btnStopPrintStatus?.setOnClickListener {
            BcmStatusUtils.cancelTask()
        }

        // 添加升级任务相关按钮
        binding?.btnStartUpgradeSubscriber?.setOnClickListener {
            startUpgradeTaskSubscriber()
        }

        binding?.btnStopUpgradeSubscriber?.setOnClickListener {
            stopUpgradeTaskSubscriber()
        }

        binding?.btnShowUpgradeTasks?.setOnClickListener {
            showActiveTasks()
        }

        binding?.btnStartUpgradeClient?.setOnClickListener {
            startUpgradeTaskClient()
        }

        binding?.btnStopUpgradeClient?.setOnClickListener {
            stopUpgradeTaskClient()
        }

        binding?.btnRefreshStorage?.setOnClickListener {
            refreshStorageStatus()
        }
    }
    
    private fun startPrintBcmStatus() {
        BcmStatusUtils.cancelTask()
        BcmStatusUtils.scheduleTask(20, TimeUnit.MILLISECONDS) {
            val bcmStatus = BcmStatusUtils.getCurrentBcmStatus()
            val bcmStatusJson = gson.toJson(bcmStatus)
            LogUtils.i(TAG, "Current bcm status:${bcmStatusJson}")
            runOnUiThread {
                binding?.tvBtmStatus?.text = bcmStatusJson
            }
            BcmStatusUtils.recycleBcmStatus(bcmStatus)
        }
    }
    
    /**
     * 启动升级任务订阅者
     */
    private fun startUpgradeTaskSubscriber() {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Starting upgrade task subscriber")
                upgradeTaskSubscriber = UpgradeTaskSubscriber()
                upgradeTaskSubscriber?.setTaskListener(object : UpgradeTaskListener {
                    override fun onTaskReceived(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Received upgrade task: ${task.taskId}")
                        runOnUiThread {
                            // 可以在这里更新UI显示收到的任务
                        }
                    }

                    override fun onTaskReady(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Upgrade task ready: ${task.taskId}")
                        val taskJson = gson.toJson(task)
                        LogUtils.i(TAG, "Task details:\n$taskJson")
                    }

                    override fun onTaskDistributing(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Upgrade task distributing: ${task.taskId}")
                    }

                    override fun onTaskCompleted(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Upgrade task completed: ${task.taskId}")
                    }

                    override fun onTaskFailed(task: UpgradeTaskInfo) {
                        LogUtils.w(TAG, "Upgrade task failed: ${task.taskId}")
                    }

                    override fun onTaskCancelled(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Upgrade task cancelled: ${task.taskId}")
                    }

                    override fun onTaskStatusChanged(task: UpgradeTaskInfo) {
                        LogUtils.d(TAG, "Upgrade task status changed: ${task.taskId} -> ${task.taskStatus}")
                    }
                })
                LogUtils.i(TAG, "Upgrade task subscriber started successfully")
            } catch (e: Exception) {
                LogUtils.e(TAG, "Failed to start upgrade task subscriber: ${e.message}")
            }
        }
    }

    /**
     * 停止升级任务订阅者
     */
    private fun stopUpgradeTaskSubscriber() {
        upgradeTaskSubscriber = null
        LogUtils.i(TAG, "Upgrade task subscriber stopped")
    }

    /**
     * 启动升级任务客户端（用于其他域控）
     */
    private fun startUpgradeTaskClient() {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Starting upgrade task client")
                val appId = 100 // 示例App ID，实际使用时应该从配置获取
                upgradeTaskClient = UpgradeTaskClient(this@MainActivity, appId)
                upgradeTaskClient?.setTaskListener(object : UpgradeTaskListener {
                    override fun onTaskReceived(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Client received upgrade task: ${task.taskId}")
                        val taskJson = gson.toJson(task)
                        LogUtils.i(TAG, "Task details:\n$taskJson")
                    }

                    override fun onTaskReady(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Client: Upgrade task ready: ${task.taskId}")
                        // 模拟执行升级任务
                        simulateTaskExecution(task)
                    }

                    override fun onTaskDistributing(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Client: Upgrade task distributing: ${task.taskId}")
                    }

                    override fun onTaskCompleted(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Client: Upgrade task completed: ${task.taskId}")
                    }

                    override fun onTaskFailed(task: UpgradeTaskInfo) {
                        LogUtils.w(TAG, "Client: Upgrade task failed: ${task.taskId}")
                    }

                    override fun onTaskCancelled(task: UpgradeTaskInfo) {
                        LogUtils.i(TAG, "Client: Upgrade task cancelled: ${task.taskId}")
                    }

                    override fun onTaskStatusChanged(task: UpgradeTaskInfo) {
                        LogUtils.d(TAG, "Client: Upgrade task status changed: ${task.taskId} -> ${task.taskStatus}")
                    }
                })
                LogUtils.i(TAG, "Upgrade task client started successfully")
            } catch (e: Exception) {
                LogUtils.e(TAG, "Failed to start upgrade task client: ${e.message}")
            }
        }
    }

    /**
     * 停止升级任务客户端
     */
    private fun stopUpgradeTaskClient() {
        upgradeTaskClient?.cleanup()
        upgradeTaskClient = null
        LogUtils.i(TAG, "Upgrade task client stopped")
    }

    /**
     * 模拟任务执行
     */
    private fun simulateTaskExecution(task: UpgradeTaskInfo) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Simulating task execution: ${task.taskId}")

                // 模拟执行时间
                Thread.sleep(3000)

                // 模拟成功执行
                val success = true
                val message = if (success) "Task executed successfully" else "Task execution failed"

                upgradeTaskClient?.reportTaskResult(task.taskId, success, message)

                LogUtils.i(TAG, "Task execution simulation completed: ${task.taskId}")

            } catch (e: Exception) {
                LogUtils.e(TAG, "Error simulating task execution: ${e.message}")
                upgradeTaskClient?.reportTaskResult(task.taskId, false, "Execution error: ${e.message}")
            }
        }
    }

    /**
     * 显示活跃的升级任务
     */
    private fun showActiveTasks() {
        threadPool.submit {
            try {
                val taskManager = UpgradeTaskManager.getInstance(this@MainActivity)
                val activeTasks = taskManager.getActiveTasks()

                LogUtils.i(TAG, "Active upgrade tasks count: ${activeTasks.size}")

                activeTasks.forEach { task ->
                    val taskJson = gson.toJson(task)
                    LogUtils.i(TAG, "Active task:\n$taskJson")
                }

                if (activeTasks.isEmpty()) {
                    LogUtils.i(TAG, "No active upgrade tasks")
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error showing active tasks: ${e.message}")
            }
        }
    }

    /**
     * 初始化存储状态
     */
    private fun initStorageStatus() {
        try {
            storageStatusManager = StorageStatusManager(this)
            storageDeviceAdapter = StorageDeviceAdapter(this)

            // 设置RecyclerView
            binding?.recyclerViewStorageDevices?.apply {
                layoutManager = LinearLayoutManager(this@MainActivity)
                adapter = storageDeviceAdapter
            }

            // 初始加载存储状态
            updateStorageStatus()

            LogUtils.i(TAG, "Storage status initialized")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error initializing storage status: ${e.message}")
        }
    }

    /**
     * 注册存储状态广播接收器
     */
    private fun registerStorageStatusReceiver() {
        try {
            val filter = IntentFilter(UsbDetectionService.ACTION_STORAGE_STATUS_CHANGED)
            registerReceiver(storageStatusReceiver, filter)
            LogUtils.i(TAG, "Storage status receiver registered")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error registering storage status receiver: ${e.message}")
        }
    }

    /**
     * 更新存储状态
     */
    private fun updateStorageStatus() {
        threadPool.submit {
            try {
                val devices = storageStatusManager?.getAllStorageDevices() ?: emptyList()

                runOnUiThread {
                    storageDeviceAdapter?.updateDevices(devices)

                    // 更新存储状态文本
                    val statusText = "检测到 ${devices.size} 个存储设备"
                    binding?.tvStorageStatus?.text = statusText

                    LogUtils.d(TAG, "Storage status updated: ${devices.size} devices")
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error updating storage status: ${e.message}")
            }
        }
    }

    /**
     * 更新USB状态
     */
    private fun updateUsbStatus(usbConnected: Boolean, usbPath: String?) {
        runOnUiThread {
            try {
                if (usbConnected && usbPath != null) {
                    binding?.tvUsbStatus?.text = "USB已连接: $usbPath"
                    binding?.tvUsbStatus?.setTextColor(getColor(android.R.color.holo_green_dark))
                    binding?.btnOpenUsb?.isEnabled = true
                    binding?.btnOpenUsb?.setOnClickListener {
                        openUsbDevice(usbPath)
                    }
                } else {
                    binding?.tvUsbStatus?.text = "未检测到USB设备"
                    binding?.tvUsbStatus?.setTextColor(getColor(android.R.color.holo_red_dark))
                    binding?.btnOpenUsb?.isEnabled = false
                    binding?.btnOpenUsb?.setOnClickListener(null)
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error updating USB status: ${e.message}")
            }
        }
    }

    /**
     * 刷新存储状态
     */
    private fun refreshStorageStatus() {
        LogUtils.i(TAG, "Refreshing storage status")
        storageStatusManager?.refreshStorageStatus()
        updateStorageStatus()
    }

    /**
     * 打开USB设备
     */
    private fun openUsbDevice(usbPath: String) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "Opening USB device: $usbPath")

                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = android.net.Uri.parse("file://$usbPath")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                if (intent.resolveActivity(packageManager) != null) {
                    startActivity(intent)
                    LogUtils.i(TAG, "USB device opened successfully")
                } else {
                    LogUtils.w(TAG, "No app available to open USB device")
                    runOnUiThread {
                        // 可以显示一个Toast提示用户
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "Error opening USB device: ${e.message}")
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            unregisterReceiver(storageStatusReceiver)
            upgradeTaskClient?.cleanup()
            LogUtils.i(TAG, "MainActivity destroyed")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error during destroy: ${e.message}")
        }
    }

    companion object {
        private const val TAG = "MainActivity"
    }
}
package com.seres.dds

import android.app.Application
import android.content.Context
import com.seres.dds.s2sservicesdk.S2sServiceSDK
import com.seres.dds.utils.LogUtils

class S2sApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "onCreate")
        instance = this
        // 初始化S2s服务
        S2sServiceSDK.init(this)
    }
    
    companion object {
        private const val TAG = "S2sApplication"
        var instance: S2sApplication? = null
            private set
        
        val appContext: Context
            get() = instance!!.applicationContext
    }
}
package seres.zcufr

import com.seres.dds.sdk.TypeUnion
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType


class ZCUFR_Control_Call : TypeUnion() {
    private var _d: ZCUFR_Control_descriminator = ZCUFR_Control_descriminator.NONE

    inner class union {
        private var BCM_Window_FL_AdjustPosition_SetIn: ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_In =
            ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_In()
        private var BCM_Window_FR_AdjustPosition_SetIn: ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In =
            ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In()
        private var BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn: ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In =
            ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In()
        private var BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn: ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In =
            ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In()

        fun initialize() {
            when (_d) {
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d -> _u_prop.change_prop_param(
                    "BCM_Window_FL_AdjustPosition_SetIn",
                    PropType.STRUCT,
                    4u
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d -> _u_prop.change_prop_param(
                    "BCM_Window_FR_AdjustPosition_SetIn",
                    PropType.STRUCT,
                    4u
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> _u_prop.change_prop_param(
                    "BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn",
                    PropType.STRUCT,
                    4u
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> _u_prop.change_prop_param(
                    "BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn",
                    PropType.STRUCT,
                    4u
                )

                else -> println("Data initialize error")
            }
        }

        fun serialize(buffer: Buffer) {
            when (_d) {
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d -> BCM_Window_FL_AdjustPosition_SetIn.serialize(
                    buffer
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d -> BCM_Window_FR_AdjustPosition_SetIn.serialize(
                    buffer
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn.serialize(
                    buffer
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn.serialize(
                    buffer
                )

                else -> println("Data serialize error")
            }
        }

        fun deserialize(buffer: Buffer) {
            when (_d) {
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d -> BCM_Window_FL_AdjustPosition_SetIn.deserialize(
                    buffer
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d -> BCM_Window_FR_AdjustPosition_SetIn.deserialize(
                    buffer
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn.deserialize(
                    buffer
                )

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn.deserialize(
                    buffer
                )

                else -> println("Data deserialize error")
            }
        }

        fun get_value(): Any {
            return when (_d) {
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d -> BCM_Window_FL_AdjustPosition_SetIn
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d -> BCM_Window_FR_AdjustPosition_SetIn
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn
                else -> println("Data get_value error")
            }
        }

        fun set_value(value: Any) {
            return when (_d) {
                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d -> BCM_Window_FL_AdjustPosition_SetIn =
                    value as ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_In

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d -> BCM_Window_FR_AdjustPosition_SetIn =
                    value as ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d -> BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECUIn =
                    value as ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In

                ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d -> BCM_CushionTemp_FR_HeatCtrlLeagacyECUIn =
                    value as ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In

                else -> println("Data get_value error")
            }
        }

    }

    private var _u: union = union()

    init {
        typename = "kotlinidl:ZCUFR_Control_Call"
        prop_create()
    }

    private fun prop_create() {
        super.prop = Prop("ZCUFR_Control_Call", 0u)
        prop!!.create_member_prop(Prop("_d", 1u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(_u_prop)
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                subprop.machine!!.serialize(buffer, _d.value)
            }
            if (subprop.m_id == 2.toUInt()) {
                this._u.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Control_Call {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                var res = subprop.machine!!.deserialize(buffer)
                this._d = ZCUFR_Control_descriminator.fromValue(res as Int)
            }
            if (subprop.m_id == 2.toUInt()) {
                this._u.deserialize(buffer)
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Control_Call) {
        this._d = value.get__d()
        this._u.set_value(value.get__u())
    }

    fun get__d(): ZCUFR_Control_descriminator {
        return this._d
    }

    fun set__d(value: ZCUFR_Control_descriminator) {
        this._d = value
        this._u.initialize()
    }

    fun get__u(): Any {
        return this._u.get_value()
    }

    fun set__u(value: Any) {
        this._u.set_value(value)
    }

}
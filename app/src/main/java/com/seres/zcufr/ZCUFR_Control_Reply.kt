package seres.zcufr

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType
import dds.rpc.ReplyHeader

class ZCUFR_Control_Reply : TypeStruct() {
    private var header: ReplyHeader = ReplyHeader()
    private var data: ZCUFR_Control_Return = ZCUFR_Control_Return()

    init {
        typename = "seres::zcufr::ZCUFR_Control_Reply"
        prop_create()
    }

    private fun prop_create() {
        super.prop = Prop("ZCUFR_Control_Reply", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("header", 1u, PropType.STRUCT, 4u))
        prop!!.create_member_prop(Prop("data", 2u, PropType.UNION, 4u))
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                header.serialize(buffer)
            }
            if (subprop.m_id == 2.toUInt()) {
                data.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Control_Reply {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                this.header = header.deserialize(buffer)
            }
            if (subprop.m_id == 2.toUInt()) {
                this.data = data.deserialize(buffer)
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Control_Reply) {
        header.copy(value.header)
        data.copy(value.data)
    }

    fun get_header(): ReplyHeader {
        return header
    }

    fun set_header(value: ReplyHeader) {
        header.copy(value)
    }

    fun get_data(): ZCUFR_Control_Return {
        return data
    }

    fun set_data(value: ZCUFR_Control_Return) {
        data.copy(value)
    }
}
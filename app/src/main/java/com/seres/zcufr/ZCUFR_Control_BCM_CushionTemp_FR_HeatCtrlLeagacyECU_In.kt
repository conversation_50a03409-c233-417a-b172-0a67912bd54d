package seres.zcufr
import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType


class ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In : TypeStruct() {
    private var bcm_Legacy_SeatHeatingLevel: Int = 0

    init {
        typename = "seres::zcufr::ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In"
        prop_create()
    }

    private fun prop_create() {
        super.prop =
            Prop("ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(
            Prop(
                "bcm_Legacy_SeatHeatingLevel",
                1u,
                PropType.PRIMITIVE,
                4u,
                "Int"
            )
        )
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                subprop.machine!!.serialize(buffer, bcm_Legacy_SeatHeatingLevel)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                var res = subprop.machine!!.deserialize(buffer)
                this.bcm_Legacy_SeatHeatingLevel = res as Int
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In) {
        bcm_Legacy_SeatHeatingLevel = value.bcm_Legacy_SeatHeatingLevel
    }

    fun get_bcm_Legacy_SeatHeatingLevel(): Int {
        return bcm_Legacy_SeatHeatingLevel
    }

    fun set_bcm_Legacy_SeatHeatingLevel(value: Int) {
        bcm_Legacy_SeatHeatingLevel = value
    }
}
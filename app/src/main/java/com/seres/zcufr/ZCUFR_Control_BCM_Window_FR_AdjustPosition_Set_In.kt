package seres.zcufr

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType

class ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In : TypeStruct() {
    private var winPosCmd: Int = 0

    init {
        typename = "seres::zcufr::ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In"
        prop_create()
    }

    private fun prop_create() {
        super.prop =
            Prop("ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("winPosCmd", 1u, PropType.PRIMITIVE, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                subprop.machine!!.serialize(buffer, winPosCmd)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                var res = subprop.machine!!.deserialize(buffer)
                this.winPosCmd = res as Int
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In) {
        winPosCmd = value.winPosCmd
    }

    fun get_winPosCmd(): Int {
        return winPosCmd
    }

    fun set_winPosCmd(value: Int) {
        winPosCmd = value
    }
}
package seres.zcufr


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.*


abstract class ZCUFR_Control_base {
    abstract fun BCM_Window_FL_AdjustPosition_Set(winPosCmd: Int): ReturnCode
    abstract fun BCM_Window_FR_AdjustPosition_Set(winPosCmd: Int): ReturnCode
    abstract fun BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode
    abstract fun BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode
}

class ZCUFR_Control_dispatcher(requestType: TypeBase, replyType: TypeBase): Dispatcher<ZCUFR_Control_base>(requestType, replyType) {
    lateinit var serviceImpl: ZCUFR_Control_base
    lateinit var replier: Replier

    override fun process(request: TypeBase?) {
        var data: Any? = null
        var desc: ZCUFR_Control_descriminator = ZCUFR_Control_descriminator.NONE
        var sampleIdentity : SampleIdentity = SampleIdentity()
        desc = (request as ZCUFR_Control_Request).get_data().get__d()
        sampleIdentity = (request as ZCUFR_Control_Request).get_header().get_requestId()
        var result = ZCUFR_Control_Reply()
        var header = ReplyHeader()
        header.set_relatedRequestId(sampleIdentity)
        result.set_header(header)
        when (desc.value) {
            ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d.value -> {
                val inData = (request as ZCUFR_Control_Request).get_data().get__u() as ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_In
                val winPosCmd = inData.get_winPosCmd()

                val retcode = serviceImpl.BCM_Window_FL_AdjustPosition_Set(winPosCmd)

                var outData = ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_Result()
                outData.set__return(retcode)

                result.get_data().set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d.value -> {
                val inData = (request as ZCUFR_Control_Request).get_data().get__u() as ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In
                val winPosCmd = inData.get_winPosCmd()

                val retcode = serviceImpl.BCM_Window_FR_AdjustPosition_Set(winPosCmd)

                var outData = ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result()
                outData.set__return(retcode)

                result.get_data().set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d.value -> {
                val inData = (request as ZCUFR_Control_Request).get_data().get__u() as ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In
                val bcm_Legacy_SeatVentilationLevel = inData.get_bcm_Legacy_SeatVentilationLevel()

                val retcode = serviceImpl.BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel)

                var outData = ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }

            ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d.value -> {
                val inData = (request as ZCUFR_Control_Request).get_data().get__u() as ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In
                val bcm_Legacy_SeatHeatingLevel = inData.get_bcm_Legacy_SeatHeatingLevel()

                val retcode = serviceImpl.BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel)

                var outData = ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result()
                outData.set__return(retcode)

                result.get_data().set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }
        }
    }

    override fun add_service_impl(serviceImpl: ZCUFR_Control_base) {
        this.serviceImpl = serviceImpl
    }

    override fun set_replier(replier: Replier) {
        this.replier = replier
    }
}

class ZCUFR_ControlService(
    serviceImpl: ZCUFR_Control_base,
    server: Server,
    param: ServiceParam,
    dispatcher: Dispatcher<ZCUFR_Control_base> = ZCUFR_Control_dispatcher(ZCUFR_Control_Request(), ZCUFR_Control_Reply())
) : ServiceEndpoint<ZCUFR_Control_base>(serviceImpl, param, server, dispatcher) {
}

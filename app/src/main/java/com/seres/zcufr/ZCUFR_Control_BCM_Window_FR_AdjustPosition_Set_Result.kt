package seres.zcufr

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType

class ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result : TypeStruct() {
    private var BCM_Window_FR_AdjustPosition_SetOut: ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out =
        ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out()
    private var _return: ReturnCode = ReturnCode.NONE

    init {
        typename = "seres::zcufr::ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result"
        prop_create()
    }

    private fun prop_create() {
        super.prop =
            Prop("ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(
            Prop(
                "BCM_Window_FR_AdjustPosition_SetOut",
                1u,
                PropType.STRUCT,
                4u
            )
        )
        prop!!.create_member_prop(Prop("_return", 2u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                BCM_Window_FR_AdjustPosition_SetOut.serialize(buffer)
            }
            if (subprop.m_id == 2.toUInt()) {
                subprop.machine!!.serialize(buffer, _return.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                this.BCM_Window_FR_AdjustPosition_SetOut =
                    BCM_Window_FR_AdjustPosition_SetOut.deserialize(buffer)
            }
            if (subprop.m_id == 2.toUInt()) {
                var res = subprop.machine!!.deserialize(buffer)
                this._return = ReturnCode.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result) {
        BCM_Window_FR_AdjustPosition_SetOut.copy(value.BCM_Window_FR_AdjustPosition_SetOut)
        _return = value._return
    }

    fun get_BCM_Window_FR_AdjustPosition_SetOut(): ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out {
        return BCM_Window_FR_AdjustPosition_SetOut
    }

    fun set_BCM_Window_FR_AdjustPosition_SetOut(value: ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out) {
        BCM_Window_FR_AdjustPosition_SetOut.copy(value)
    }

    fun get__return(): ReturnCode {
        return _return
    }

    fun set__return(value: ReturnCode) {
        _return = value
    }
}
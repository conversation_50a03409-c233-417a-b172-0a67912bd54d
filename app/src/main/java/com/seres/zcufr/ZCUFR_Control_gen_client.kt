package seres.zcufr

import com.seres.dds.sdk.ClientEndpoint
import com.seres.dds.sdk.ClientParam
import com.seres.dds.sdk.TypeBase
import dds.rpc.EntityId_t
import dds.rpc.GUID_t
import dds.rpc.RequestHeader
import dds.rpc.SampleIdentity


class ZCUFR_ControlClient : ClientEndpoint {
    constructor(
        param: ClientParam,
        rpcRequest: TypeBase = ZCUFR_Control_Request(),
        rpcReply: TypeBase = ZCUFR_Control_Reply()
    ) : super(param, rpcRequest, rpcReply)

    fun BCM_Window_FL_AdjustPosition_Set(winPosCmd: Int): ReturnCode {
        var ret: ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = ZCUFR_Control_Request()
        request.set_header(header)
        val call = ZCUFR_Control_Call()
        val request_data = ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_In()
        request_data.set_winPosCmd(winPosCmd as Int)

        call.set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber() + 1u)
        send_request(request)
        val reply = receive_reply() as ZCUFR_Control_Reply
        if (request.get_header().get_requestId().get_sequence_number()
                .get_low() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_low()
            && request.get_header().get_requestId().get_sequence_number()
                .get_high() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId()
                        .get_entityKey()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId()
                .get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid()
                .get_entityId().get_entityKind()
        ) {

            if (reply.get_data()
                    .get__d() == ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_d
            ) {
                var return_val = reply.get_data()
                    .get__u() as ZCUFR_Control_BCM_Window_FL_AdjustPosition_Set_Result
                return return_val.get__return()
            } else {
                throw Exception("[ZCUFR_ControlClient]--> call BCM_Window_FL_AdjustPosition_Set unkown _d() value")
            }
        } else {
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Window_FR_AdjustPosition_Set(winPosCmd: Int): ReturnCode {
        var ret: ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = ZCUFR_Control_Request()
        request.set_header(header)
        val call = ZCUFR_Control_Call()
        val request_data = ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_In()
        request_data.set_winPosCmd(winPosCmd as Int)

        call.set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber() + 1u)
        send_request(request)
        val reply = receive_reply() as ZCUFR_Control_Reply
        if (request.get_header().get_requestId().get_sequence_number()
                .get_low() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_low()
            && request.get_header().get_requestId().get_sequence_number()
                .get_high() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId()
                        .get_entityKey()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId()
                .get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid()
                .get_entityId().get_entityKind()
        ) {

            if (reply.get_data()
                    .get__d() == ZCUFR_Control_descriminator.ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_d
            ) {
                var return_val = reply.get_data()
                    .get__u() as ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Result
                return return_val.get__return()
            } else {
                throw Exception("[ZCUFR_ControlClient]--> call BCM_Window_FR_AdjustPosition_Set unkown _d() value")
            }
        } else {
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode {
        var ret: ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = ZCUFR_Control_Request()
        request.set_header(header)
        val call = ZCUFR_Control_Call()
        val request_data = ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatVentilationLevel(bcm_Legacy_SeatVentilationLevel)

        call.set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber() + 1u)
        send_request(request)
        val reply = receive_reply() as ZCUFR_Control_Reply
        if (request.get_header().get_requestId().get_sequence_number()
                .get_low() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_low()
            && request.get_header().get_requestId().get_sequence_number()
                .get_high() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId()
                        .get_entityKey()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId()
                .get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid()
                .get_entityId().get_entityKind()
        ) {

            if (reply.get_data()
                    .get__d() == ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_d
            ) {
                var return_val = reply.get_data()
                    .get__u() as ZCUFR_Control_BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU_Result
                return return_val.get__return()
            } else {
                throw Exception("[ZCUFR_ControlClient]--> call BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU unkown _d() value")
            }
        } else {
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode {
        var ret: ReturnCode = ReturnCode.ERROR
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.get_sequence_number().set_low((seq and 0xFFFFuL).toUInt())
        sampleIdentity.get_sequence_number().set_high((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.set_guidPrefix(get_guidPrefix().toTypedArray())
        entityId.set_entityKey(get_entityKey().toTypedArray())
        entityId.set_entityKind(get_entityKind())
        guid.set_entityId(entityId)
        sampleIdentity.set_writer_guid(guid)
        header.set_requestId(sampleIdentity)

        val request = ZCUFR_Control_Request()
        request.set_header(header)
        val call = ZCUFR_Control_Call()
        val request_data = ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_In()
        request_data.set_bcm_Legacy_SeatHeatingLevel(bcm_Legacy_SeatHeatingLevel as Int)

        call.set__d(ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d)
        call.set__u(request_data)
        request.set_data(call)
        this.set_sequenceNumber(get_sequenceNumber() + 1u)
        send_request(request)
        val reply = receive_reply() as ZCUFR_Control_Reply
        if (request.get_header().get_requestId().get_sequence_number()
                .get_low() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_low()
            && request.get_header().get_requestId().get_sequence_number()
                .get_high() == reply.get_header().get_relatedRequestId().get_sequence_number()
                .get_high()
            && request.get_header().get_requestId().get_writer_guid().get_guidPrefix()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_guidPrefix()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId().get_entityKey()
                .contentEquals(
                    reply.get_header().get_relatedRequestId().get_writer_guid().get_entityId()
                        .get_entityKey()
                )
            && request.get_header().get_requestId().get_writer_guid().get_entityId()
                .get_entityKind() == reply.get_header().get_relatedRequestId().get_writer_guid()
                .get_entityId().get_entityKind()
        ) {

            if (reply.get_data()
                    .get__d() == ZCUFR_Control_descriminator.ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_d
            ) {
                var return_val = reply.get_data()
                    .get__u() as ZCUFR_Control_BCM_CushionTemp_FR_HeatCtrlLeagacyECU_Result
                return return_val.get__return()
            } else {
                throw Exception("[ZCUFR_ControlClient]--> call BCM_CushionTemp_FR_HeatCtrlLeagacyECU unkown _d() value")
            }
        } else {
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }
}

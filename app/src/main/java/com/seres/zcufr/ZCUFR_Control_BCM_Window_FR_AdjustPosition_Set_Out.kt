package seres.zcufr

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType

class ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out : TypeStruct() {
    private var _default: Byte = 0

    init {
        typename = "seres::zcufr::ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out"
        prop_create()
    }

    private fun prop_create() {
        super.prop =
            Prop("ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("_default", 1u, PropType.PRIMITIVE, 4u, "Byte"))
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                subprop.machine!!.serialize(buffer, _default)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out {
        prop!!.members_prop.forEach { subprop ->
            if (subprop.m_id == 1.toUInt()) {
                var res = subprop.machine!!.deserialize(buffer)
                this._default = res as Byte
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Control_BCM_Window_FR_AdjustPosition_Set_Out) {
        _default = value._default
    }

    fun get__default(): Byte {
        return _default
    }

    fun set__default(value: Byte) {
        _default = value
    }
}
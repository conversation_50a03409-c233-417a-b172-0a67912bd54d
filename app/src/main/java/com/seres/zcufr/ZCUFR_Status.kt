package seres.zcufr

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class ZCUFR_Status : TypeStruct(){
    private var positionFL : Int = 0
    private var positionFR : Int = 0
    private var heatingStatusFL : Boolean = false
    private var heatingLevelFL : Int = 0
    private var ventilatingStatusFL : <PERSON>olean = false
    private var heatingStatusFR : Boolean = false
    private var ventilatingStatusFR : Boolean = false
    private var heatingLevelFR : Int = 0
    private var actuateStatusFL : BCM_Window_ActuateStatus = BCM_Window_ActuateStatus.IDLE
    private var actuateStatusFR : BCM_Window_ActuateStatus = BCM_Window_ActuateStatus.IDLE

    init{
        typename = "seres::zcufr::ZCUFR_Status"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUFR_Status", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("positionFL", 1u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("positionFR", 2u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("heatingStatusFL", 3u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("heatingLevelFL", 4u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("ventilatingStatusFL", 5u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("heatingStatusFR", 6u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("ventilatingStatusFR", 7u, PropType.PRIMITIVE, 4u, "Boolean"))
        prop!!.create_member_prop(Prop("heatingLevelFR", 8u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("actuateStatusFL", 9u, PropType.ENUM, 4u, "Int"))
        prop!!.create_member_prop(Prop("actuateStatusFR", 10u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, positionFL)
            }
            if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, positionFR)
            }
            if(subprop.m_id == 3.toUInt()){
                subprop.machine!!.serialize(buffer, heatingStatusFL)
            }
            if(subprop.m_id == 4.toUInt()){
                subprop.machine!!.serialize(buffer, heatingLevelFL)
            }
            if(subprop.m_id == 5.toUInt()){
                subprop.machine!!.serialize(buffer, ventilatingStatusFL)
            }
            if(subprop.m_id == 6.toUInt()){
                subprop.machine!!.serialize(buffer, heatingStatusFR)
            }
            if(subprop.m_id == 7.toUInt()){
                subprop.machine!!.serialize(buffer, ventilatingStatusFR)
            }
            if(subprop.m_id == 8.toUInt()){
                subprop.machine!!.serialize(buffer, heatingLevelFR)
            }
            if(subprop.m_id == 9.toUInt()){
                subprop.machine!!.serialize(buffer, actuateStatusFL.value)
            }
            if(subprop.m_id == 10.toUInt()){
                subprop.machine!!.serialize(buffer, actuateStatusFR.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUFR_Status{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.positionFL = res as Int
            }
            if(subprop.m_id == 2.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.positionFR = res as Int
            }
            if(subprop.m_id == 3.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingStatusFL = res as Boolean
            }
            if(subprop.m_id == 4.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingLevelFL = res as Int
            }
            if(subprop.m_id == 5.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.ventilatingStatusFL = res as Boolean
            }
            if(subprop.m_id == 6.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingStatusFR = res as Boolean
            }
            if(subprop.m_id == 7.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.ventilatingStatusFR = res as Boolean
            }
            if(subprop.m_id == 8.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.heatingLevelFR = res as Int
            }
            if(subprop.m_id == 9.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.actuateStatusFL = BCM_Window_ActuateStatus.fromValue(res as Int)
            }
            if(subprop.m_id == 10.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.actuateStatusFR = BCM_Window_ActuateStatus.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: ZCUFR_Status){
        positionFL = value.positionFL
        positionFR = value.positionFR
        heatingStatusFL = value.heatingStatusFL
        heatingLevelFL = value.heatingLevelFL
        ventilatingStatusFL = value.ventilatingStatusFL
        heatingStatusFR = value.heatingStatusFR
        ventilatingStatusFR = value.ventilatingStatusFR
        heatingLevelFR = value.heatingLevelFR
        actuateStatusFL = value.actuateStatusFL
        actuateStatusFR = value.actuateStatusFR
    }

    fun get_positionFL(): Int{
        return positionFL
    }

    fun set_positionFL(value: Int){
        positionFL = value
    }

    fun get_positionFR(): Int{
        return positionFR
    }

    fun set_positionFR(value: Int){
        positionFR = value
    }

    fun get_heatingStatusFL(): Boolean{
        return heatingStatusFL
    }

    fun set_heatingStatusFL(value: Boolean){
        heatingStatusFL = value
    }

    fun get_heatingLevelFL(): Int{
        return heatingLevelFL
    }

    fun set_heatingLevelFL(value: Int){
        heatingLevelFL = value
    }

    fun get_ventilatingStatusFL(): Boolean{
        return ventilatingStatusFL
    }

    fun set_ventilatingStatusFL(value: Boolean){
        ventilatingStatusFL = value
    }

    fun get_heatingStatusFR(): Boolean{
        return heatingStatusFR
    }

    fun set_heatingStatusFR(value: Boolean){
        heatingStatusFR = value
    }

    fun get_ventilatingStatusFR(): Boolean{
        return ventilatingStatusFR
    }

    fun set_ventilatingStatusFR(value: Boolean){
        ventilatingStatusFR = value
    }

    fun get_heatingLevelFR(): Int{
        return heatingLevelFR
    }

    fun set_heatingLevelFR(value: Int){
        heatingLevelFR = value
    }

    fun get_actuateStatusFL(): BCM_Window_ActuateStatus{
        return actuateStatusFL
    }

    fun set_actuateStatusFL(value: BCM_Window_ActuateStatus){
        actuateStatusFL = value
    }

    fun get_actuateStatusFR():BCM_Window_ActuateStatus{
        return actuateStatusFR
    }

    fun set_actuateStatusFR(value: BCM_Window_ActuateStatus){
        actuateStatusFR = value
    }
}
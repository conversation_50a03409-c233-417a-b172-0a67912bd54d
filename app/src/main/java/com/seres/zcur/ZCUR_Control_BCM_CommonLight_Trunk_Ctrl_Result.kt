package seres.zcur
import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

class ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Result : TypeStruct(){
    private var BCM_CommonLight_Trunk_CtrlOut : ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Out = ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Out()
    private var _return : seres.zcur.ReturnCode = seres.zcur.ReturnCode.NONE

    init{
        typename = "seres::zcur::ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Result"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Result", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("BCM_CommonLight_Trunk_CtrlOut", 1u, PropType.STRUCT, 4u))
        prop!!.create_member_prop(Prop("_return", 2u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                BCM_CommonLight_Trunk_CtrlOut.serialize(buffer)
            }
               if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, _return.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Result{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                this.BCM_CommonLight_Trunk_CtrlOut = BCM_CommonLight_Trunk_CtrlOut.deserialize(buffer)
              }
            if(subprop.m_id == 2.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._return = seres.zcur.ReturnCode.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Result){
        BCM_CommonLight_Trunk_CtrlOut.copy(value.BCM_CommonLight_Trunk_CtrlOut)
        _return = value._return
    }

    fun get_BCM_CommonLight_Trunk_CtrlOut(): ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Out{
        return BCM_CommonLight_Trunk_CtrlOut
    }

    fun set_BCM_CommonLight_Trunk_CtrlOut(value: ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Out){
        BCM_CommonLight_Trunk_CtrlOut.copy(value)
    }

    fun get__return(): seres.zcur.ReturnCode{
        return _return
    }

    fun set__return(value: seres.zcur.ReturnCode){
        _return = value
    }
}
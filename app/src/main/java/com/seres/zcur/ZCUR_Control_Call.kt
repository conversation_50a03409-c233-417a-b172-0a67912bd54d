package seres.zcur

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
enum class ZCUR_Control_descriminator(val value: Int){
    NONE(0),
    ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d(ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_HASH);
    companion object {
        private val valueMap = entries.associateBy { it.value }
          fun fromValue(value: Int): ZCUR_Control_descriminator{
            return  valueMap[value]?: NONE
        }
    }
}

class ZCUR_Control_Call: TypeUnion(){
    private var _d : ZCUR_Control_descriminator = ZCUR_Control_descriminator.NONE
    inner class union{
        private var BCM_CommonLight_Trunk_CtrlIn: ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In = ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In() 
        fun initialize(){
            when(_d){
                ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d -> _u_prop.change_prop_param("BCM_CommonLight_Trunk_CtrlIn", PropType.STRUCT, 4u)
                else -> println("Data initialize error")
            }
        }
        fun serialize(buffer: Buffer){
            when(_d){
                ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d -> BCM_CommonLight_Trunk_CtrlIn.serialize(buffer)
                else -> println("Data serialize error")
            }
        }

        fun deserialize(buffer: Buffer){
            when(_d){
                ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d -> BCM_CommonLight_Trunk_CtrlIn.deserialize(buffer)
                else -> println("Data deserialize error")
            }
        }

        fun get_value():Any{
            return when(_d){
                ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d -> BCM_CommonLight_Trunk_CtrlIn
                else -> println("Data get_value error")
            }
        }

        fun set_value(value: Any){
            return when(_d){
                ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d -> BCM_CommonLight_Trunk_CtrlIn = value as ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In
                else -> println("Data get_value error")
            }
        }

    }
    private var _u: union = union()

    init{
        typename = "kotlinidl:ZCUR_Control_Call"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUR_Control_Call", 0u)
        prop!!.create_member_prop(Prop("_d",  1u, PropType.ENUM, 4u, "Int"))
        prop!!.create_member_prop(_u_prop)
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, _d.value)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUR_Control_Call{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this._d = ZCUR_Control_descriminator.fromValue(res as Int)
            }
            if(subprop.m_id == 2.toUInt()){
                this._u.deserialize(buffer)
            }
        }
        return this
    }

    fun copy(value: ZCUR_Control_Call){
        this._d = value.get__d()
        this._u.set_value(value.get__u())
    }

    fun get__d():ZCUR_Control_descriminator{
        return this._d
    }

    fun set__d(value: ZCUR_Control_descriminator){
        this._d = value
        this._u.initialize()
    }

    fun get__u():Any{
        return this._u.get_value()
    }

    fun set__u(value: Any){
        this._u.set_value(value)
    }

}
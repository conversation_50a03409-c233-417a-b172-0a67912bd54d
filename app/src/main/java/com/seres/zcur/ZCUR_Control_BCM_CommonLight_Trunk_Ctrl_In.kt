package seres.zcur

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In : TypeStruct(){
    private var onOffCmd : seres.zcur.OnOffCmd = seres.zcur.OnOffCmd.NO_REQUEST

    init{
        typename = "seres::zcur::ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("onOffCmd", 1u, PropType.ENUM, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, onOffCmd.value)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.onOffCmd = seres.zcur.OnOffCmd.fromValue(res as Int)
            }
        }
        return this
    }

    fun copy(value: ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In){
        onOffCmd = value.onOffCmd
    }

    fun get_onOffCmd(): seres.zcur.OnOffCmd{
        return onOffCmd
    }

    fun set_onOffCmd(value: seres.zcur.OnOffCmd){
        onOffCmd = value
    }
}
package seres.zcur

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class ZCUR_Status : TypeStruct(){
    private var commonlight_trunk_lightstatus : Int = 0

    init{
        typename = "seres::zcur::ZCUR_Status"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ZCUR_Status", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("commonlight_trunk_lightstatus", 1u, PropType.PRIMITIVE, 4u, "Int"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, commonlight_trunk_lightstatus)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ZCUR_Status{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.commonlight_trunk_lightstatus = res as Int
            }
        }
        return this
    }

    fun copy(value: ZCUR_Status){
        commonlight_trunk_lightstatus = value.commonlight_trunk_lightstatus
    }

    fun get_commonlight_trunk_lightstatus(): Int{
        return commonlight_trunk_lightstatus
    }

    fun set_commonlight_trunk_lightstatus(value: Int){
        commonlight_trunk_lightstatus = value
    }
}
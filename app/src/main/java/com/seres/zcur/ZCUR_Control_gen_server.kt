package seres.zcur



import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.*

abstract class ZCUR_Control_base {
    abstract fun BCM_CommonLight_Trunk_Ctrl(onOffCmd: seres.zcur.OnOffCmd): ReturnCode
}

class ZCUR_Control_dispatcher(requestType: TypeBase, replyType: TypeBase): Dispatcher<ZCUR_Control_base>(requestType, replyType) {
    lateinit var serviceImpl: ZCUR_Control_base
    lateinit var replier: Replier
  
    override fun process(request: TypeBase?) {
        var data: Any? = null
        var desc: ZCUR_Control_descriminator = ZCUR_Control_descriminator.NONE
        var sampleIdentity : SampleIdentity = SampleIdentity()
        desc = (request as ZCUR_Control_Request).get_data().get__d()
        sampleIdentity = (request as ZCUR_Control_Request).get_header().get_requestId()
        var result = ZCUR_Control_Reply()
        var header = ReplyHeader()
        header.set_relatedRequestId(sampleIdentity)
        result.set_header(header)
        when (desc.value) {
            ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d.value -> {
                val inData = (request as ZCUR_Control_Request).get_data().get__u() as ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_In
                val onOffCmd = inData.get_onOffCmd()

                val retcode = serviceImpl.BCM_CommonLight_Trunk_Ctrl(onOffCmd)

                var outData = ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_Result()
                outData.set__return(retcode)

                result.get_data().set__d(ZCUR_Control_descriminator.ZCUR_Control_BCM_CommonLight_Trunk_Ctrl_d)
                result.get_data().set__u(outData)

                replier.send_reply(result)
            }
        }
    }

    override fun add_service_impl(serviceImpl: ZCUR_Control_base) {
        this.serviceImpl = serviceImpl
    }  

    override fun set_replier(replier: Replier) {
        this.replier = replier
    }  
}

class ZCUR_ControlService(
    serviceImpl: ZCUR_Control_base,
    server: Server,
    param: ServiceParam,
    dispatcher: Dispatcher<ZCUR_Control_base> = ZCUR_Control_dispatcher(ZCUR_Control_Request(), ZCUR_Control_Reply())
    ) : ServiceEndpoint<ZCUR_Control_base>(serviceImpl, param, server, dispatcher) {
}

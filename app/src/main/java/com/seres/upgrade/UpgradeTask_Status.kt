package com.seres.upgrade

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*

/**
 * DDS升级任务状态数据类型
 */
class UpgradeTask_Status : TypeStruct() {
    private var taskId: String = ""
    private var taskName: String = ""
    private var taskVersion: String = ""
    private var createTime: Long = 0L
    private var usbPath: String = ""
    private var targetPath: String = ""
    private var totalSize: Long = 0L
    private var taskStatus: Int = 0 // 0=PENDING, 1=COPYING, 2=READY, 3=DISTRIBUTING, 4=COMPLETED, 5=FAILED, 6=CANCELLED
    private var description: String = ""
    private var packageCount: Int = 0
    private var packageListJson: String = "" // JSON格式的包列表

    init {
        typename = "com::seres::upgrade::UpgradeTask_Status"
        prop_create()
    }

    private fun prop_create() {
        super.prop = Prop("UpgradeTask_Status", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("taskId", 1u, PropType.PRIMITIVE, 4u, "String"))
        prop!!.create_member_prop(Prop("taskName", 2u, PropType.PRIMITIVE, 4u, "String"))
        prop!!.create_member_prop(Prop("taskVersion", 3u, PropType.PRIMITIVE, 4u, "String"))
        prop!!.create_member_prop(Prop("createTime", 4u, PropType.PRIMITIVE, 4u, "Long"))
        prop!!.create_member_prop(Prop("usbPath", 5u, PropType.PRIMITIVE, 4u, "String"))
        prop!!.create_member_prop(Prop("targetPath", 6u, PropType.PRIMITIVE, 4u, "String"))
        prop!!.create_member_prop(Prop("totalSize", 7u, PropType.PRIMITIVE, 4u, "Long"))
        prop!!.create_member_prop(Prop("taskStatus", 8u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("description", 9u, PropType.PRIMITIVE, 4u, "String"))
        prop!!.create_member_prop(Prop("packageCount", 10u, PropType.PRIMITIVE, 4u, "Int"))
        prop!!.create_member_prop(Prop("packageListJson", 11u, PropType.PRIMITIVE, 4u, "String"))
    }

    override fun serialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            when (subprop.m_id.toInt()) {
                1 -> subprop.machine!!.serialize(buffer, taskId)
                2 -> subprop.machine!!.serialize(buffer, taskName)
                3 -> subprop.machine!!.serialize(buffer, taskVersion)
                4 -> subprop.machine!!.serialize(buffer, createTime)
                5 -> subprop.machine!!.serialize(buffer, usbPath)
                6 -> subprop.machine!!.serialize(buffer, targetPath)
                7 -> subprop.machine!!.serialize(buffer, totalSize)
                8 -> subprop.machine!!.serialize(buffer, taskStatus)
                9 -> subprop.machine!!.serialize(buffer, description)
                10 -> subprop.machine!!.serialize(buffer, packageCount)
                11 -> subprop.machine!!.serialize(buffer, packageListJson)
            }
        }
    }

    override fun deserialize(buffer: Buffer) {
        prop!!.members_prop.forEach { subprop ->
            when (subprop.m_id.toInt()) {
                1 -> taskId = subprop.machine!!.deserialize(buffer) as String
                2 -> taskName = subprop.machine!!.deserialize(buffer) as String
                3 -> taskVersion = subprop.machine!!.deserialize(buffer) as String
                4 -> createTime = subprop.machine!!.deserialize(buffer) as Long
                5 -> usbPath = subprop.machine!!.deserialize(buffer) as String
                6 -> targetPath = subprop.machine!!.deserialize(buffer) as String
                7 -> totalSize = subprop.machine!!.deserialize(buffer) as Long
                8 -> taskStatus = subprop.machine!!.deserialize(buffer) as Int
                9 -> description = subprop.machine!!.deserialize(buffer) as String
                10 -> packageCount = subprop.machine!!.deserialize(buffer) as Int
                11 -> packageListJson = subprop.machine!!.deserialize(buffer) as String
            }
        }
    }

    // Getter和Setter方法
    fun get_taskId(): String = taskId
    fun set_taskId(value: String) { taskId = value }
    
    fun get_taskName(): String = taskName
    fun set_taskName(value: String) { taskName = value }
    
    fun get_taskVersion(): String = taskVersion
    fun set_taskVersion(value: String) { taskVersion = value }
    
    fun get_createTime(): Long = createTime
    fun set_createTime(value: Long) { createTime = value }
    
    fun get_usbPath(): String = usbPath
    fun set_usbPath(value: String) { usbPath = value }
    
    fun get_targetPath(): String = targetPath
    fun set_targetPath(value: String) { targetPath = value }
    
    fun get_totalSize(): Long = totalSize
    fun set_totalSize(value: Long) { totalSize = value }
    
    fun get_taskStatus(): Int = taskStatus
    fun set_taskStatus(value: Int) { taskStatus = value }
    
    fun get_description(): String = description
    fun set_description(value: String) { description = value }
    
    fun get_packageCount(): Int = packageCount
    fun set_packageCount(value: Int) { packageCount = value }
    
    fun get_packageListJson(): String = packageListJson
    fun set_packageListJson(value: String) { packageListJson = value }
}

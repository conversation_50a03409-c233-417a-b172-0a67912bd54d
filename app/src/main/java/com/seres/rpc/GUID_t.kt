package dds.rpc

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.Prop
import com.seres.dds.sdk.idl.PropType

//class
class GUID_t : TypeStruct(){
    private var guidPrefix : Array<Byte> =  Array<Byte>(12) { 0 }
    private var entityId : EntityId_t = EntityId_t()

    init{
        typename = "dds::rpc::GUID_t"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("GUID_t", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("guidPrefix", 1u, PropType.ARRAY, 12u, "Byte"))
        prop!!.create_member_prop(Prop("entityId", 2u, PropType.STRUCT, 4u))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, guidPrefix)
            }
               if(subprop.m_id == 2.toUInt()){
                entityId.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): GUID_t{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                val size = (res as Array<*>).size
                for(i in 0 until size){
                    this.guidPrefix.set(i, res.get(i) as Byte)
                }

//                this.guidPrefix = res as
            }
            if(subprop.m_id == 2.toUInt()){
//                var res = subprop.machine!!.deserialize(buffer)
                this.entityId = entityId.deserialize(buffer)
              }
        }
        return this
    }

    fun copy(value: GUID_t):GUID_t{
        guidPrefix = value.guidPrefix
        entityId.copy(value.entityId)
        return this
    }

    fun get_guidPrefix(): Array<Byte>{
        return guidPrefix
    }

    fun set_guidPrefix(value: Array<Byte>){
        guidPrefix = value
    }

    fun get_entityId(): EntityId_t{
        return entityId
    }

    fun set_entityId(value: EntityId_t){
        entityId.copy(value)
    }
}
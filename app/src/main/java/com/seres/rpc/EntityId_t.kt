package dds.rpc

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.*


class EntityId_t : TypeStruct(){
    private var entityKey : Array<Byte> = Array<Byte>(3) { 0 }
    private var entityKind : Byte = 0

    init{
        typename = "dds::rpc::EntityId_t"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("EntityId_t", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("entityKey", 1u, PropType.ARRAY, 3u,"Byte"))
        prop!!.create_member_prop(Prop("entityKind", 2u, PropType.PRIMITIVE, 4u,"Byte"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, entityKey)
            }
               if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, entityKind)
            }
        }
    }

    override fun deserialize(buffer: Buffer): EntityId_t{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                val res = subprop.machine!!.deserialize(buffer)
                val size = (res as Array<*>).size
                for(i in 0 until size){
                    this.entityKey.set(i, res.get(i) as Byte)
                }

            }
            if(subprop.m_id == 2.toUInt()){
                val res = subprop.machine!!.deserialize(buffer)
                this.entityKind = res as Byte
            }
        }
        return this
    }

    fun copy(value: EntityId_t){
        entityKey = value.entityKey
        entityKind = value.entityKind
    }

    fun get_entityKey(): Array<Byte>{
        return entityKey
    }

    fun set_entityKey(value: Array<Byte>){
        entityKey = value
    }

    fun get_entityKind(): Byte{
        return entityKind
    }

    fun set_entityKind(value: Byte){
        entityKind = value
    }
}
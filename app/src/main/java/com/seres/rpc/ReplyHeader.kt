package dds.rpc

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
class ReplyHeader : TypeStruct(){
    private var relatedRequestId : SampleIdentity = SampleIdentity()

    init{
        typename = "dds::rpc::ReplyHeader"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("ReplyHeader", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("relatedRequestId", 1u, PropType.STRUCT, 4u))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                relatedRequestId.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): ReplyHeader{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
//                var res = subprop.machine!!.deserialize(buffer)
                this.relatedRequestId = relatedRequestId.deserialize(buffer)
              }
        }
        return this
    }

    fun copy(value: ReplyHeader){
        relatedRequestId.copy(value.relatedRequestId)
    }

    fun get_relatedRequestId(): SampleIdentity{
        return this.relatedRequestId
    }

    fun set_relatedRequestId(value: SampleIdentity){
        relatedRequestId.copy(value)
    }
}
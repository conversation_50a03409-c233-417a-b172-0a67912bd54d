package dds.rpc

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class RequestHeader : TypeStruct(){
    private var requestId : SampleIdentity = SampleIdentity()

    init{
        typename = "dds::rpc::RequestHeader"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("RequestHeader", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("requestId", 1u, PropType.STRUCT, 4u))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                requestId.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): RequestHeader{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
//                var res = subprop.machine!!.deserialize(buffer)
                this.requestId = requestId.deserialize(buffer)
              }
        }
        return this
    }

    fun copy(value: RequestHeader){
        requestId.copy(value.requestId)
    }

    fun get_requestId(): SampleIdentity{
        return requestId
    }

    fun set_requestId(value: SampleIdentity){
        requestId.copy(value)
    }
}
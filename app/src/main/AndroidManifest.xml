<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.hardware.usb.host" />

    <queries>
        <package android:name="cn.seres.auto.launcher" />
    </queries>
    <application
        android:name=".S2sApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.Scomkotlin"
        tools:targetApi="31">
        <activity
            android:name="com.seres.dds.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name="com.seres.dds.server.IpcServer"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.example.remote.action" />
            </intent-filter>
        </service>
        <service
            android:name="com.seres.dds.testdemo.example.SubService"
            android:exported="true"
            android:process=":sub" />

        <service
            android:name="com.seres.dds.testdemo.example.ClientService"
            android:exported="true"
            android:process=":sub">
            <intent-filter>
                <action android:name="com.seres.dds.testdemo.example.ClientService" />
            </intent-filter>
            <meta-data
                android:name="com.seres.dds.example.IClientService"
                android:value="com.seres.dds.example.IClientService" />
        </service>

        <service
            android:name=".bootservice.BootCompleteService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync" />

        <service
            android:name=".upgrade.UsbDetectionService"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver
            android:name=".bootservice.BootCompleteReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
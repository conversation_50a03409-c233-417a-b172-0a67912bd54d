<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="pubsub测试:"
            android:textSize="24dp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnStartSubscriber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Subscriber" />

        <Button
            android:id="@+id/btnStartPublisher"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Publisher" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="rpc测试:"
            android:textSize="24dp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnStartClient"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Client" />

        <Button
            android:id="@+id/btnStartServer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Server" />
    </LinearLayout>

    <Button
        android:id="@+id/btnSendMsgToAndroid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="向Android发送消息" />

    <Button
        android:id="@+id/btnStartPrintStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开始显示BCM状态数据" />
    <Button
        android:id="@+id/btnStopPrintStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="停止显示BCM状态数据" />

    <!-- 存储状态显示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="20dp"
        android:background="@drawable/storage_section_background"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="存储设备状态"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/tvStorageStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="检测到 0 个存储设备"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray" />

        <!-- USB状态显示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvUsbStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="未检测到USB设备"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_red_dark" />

            <Button
                android:id="@+id/btnOpenUsb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="打开USB"
                android:textSize="12sp"
                android:enabled="false"
                android:background="@drawable/button_background"
                android:textColor="@android:color/white"
                android:padding="8dp" />

        </LinearLayout>

        <Button
            android:id="@+id/btnRefreshStorage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="刷新存储状态"
            android:textSize="14sp"
            android:background="@drawable/button_background"
            android:textColor="@android:color/white"
            android:padding="8dp" />

        <!-- 存储设备列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewStorageDevices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:maxHeight="300dp"
            android:scrollbars="vertical" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="升级任务测试:"
            android:textSize="24dp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnStartUpgradeSubscriber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动升级任务订阅者" />

        <Button
            android:id="@+id/btnStopUpgradeSubscriber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="停止升级任务订阅者" />

        <Button
            android:id="@+id/btnShowUpgradeTasks"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="显示活跃升级任务" />

        <Button
            android:id="@+id/btnStartUpgradeClient"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动升级任务客户端" />

        <Button
            android:id="@+id/btnStopUpgradeClient"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="停止升级任务客户端" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvBtmStatusTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        android:text="车辆状态:" />

    <TextView
        android:id="@+id/tvBtmStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        tools:text="主驾驶座椅通风状态:打开" />

</LinearLayout>

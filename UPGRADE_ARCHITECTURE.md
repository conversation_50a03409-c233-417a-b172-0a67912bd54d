# U盘升级系统架构说明

## 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   U盘升级服务    │    │    S2S服务      │    │   其他域控       │
│                │    │                │    │                │
│ UsbDetection    │    │ UpgradeTask     │    │ UpgradeTask     │
│ Service         │    │ Handler         │    │ Client          │
│                │    │                │    │                │
│ UpgradeTask     │    │                │    │                │
│ Manager         │    │                │    │                │
│                │    │                │    │                │
│ UpgradeTask     │    │                │    │                │
│ Publisher       │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │      AIDL             │        DDS            │
         │   (JSON String)       │    (UpgradeTask       │
         │                       │     _Status)          │
         └───────────────────────┼───────────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │   DDS订阅者      │
                    │                │
                    │ UpgradeTask     │
                    │ Subscriber      │
                    │                │
                    │ (测试用)        │
                    └─────────────────┘
```

## 通信流程

### 1. 升级任务发布流程

```
1. U盘插入 → UsbDetectionService 检测
2. UpgradePackageAnalyzer 分析升级包
3. UpgradeTaskManager 处理任务
4. UpgradeTaskPublisher 通过AIDL发送到S2S
5. S2S的UpgradeTaskHandler 接收并处理
6. UpgradeTaskHandler 通过DDS转发到各域控
7. 各域控的UpgradeTaskClient或Subscriber接收任务
```

### 2. 数据流转换

```
UpgradeTaskInfo (Java对象)
         ↓
JSON String (AIDL传输)
         ↓
UpgradeTask_Status (DDS对象)
         ↓
各域控接收并解析
```

## 关键接口

### AIDL接口 (升级服务 ↔ S2S)

```kotlin
// 服务Hash ID
const val UPGRADE_TASK_SERVICE_HASH = 1000
const val UPGRADE_STATUS_SERVICE_HASH = 1001
const val UPGRADE_RESULT_SERVICE_HASH = 1002

// 调用示例
val params = Bundle().apply {
    putString("action", "publish_upgrade_task")
    putString("task_data", taskJson)
}
s2sService.invokeAsync(appId, UPGRADE_TASK_SERVICE_HASH, params, callback)
```

### DDS接口 (S2S ↔ 域控)

```kotlin
// Topic名称
const val TOPIC_NAME = "UPGRADE_TASK_STATUS"

// 数据类型
class UpgradeTask_Status : TypeStruct() {
    private var taskId: String = ""
    private var taskStatus: Int = 0
    private var packageListJson: String = ""
    // ... 其他字段
}
```

## 服务Hash映射

| 服务类型 | Hash ID | 用途 |
|---------|---------|------|
| UPGRADE_TASK_SERVICE | 1000 | 发布升级任务 |
| UPGRADE_STATUS_SERVICE | 1001 | 更新任务状态 |
| UPGRADE_RESULT_SERVICE | 1002 | 报告执行结果 |

## 信号Hash映射

| 信号类型 | Hash ID | 用途 |
|---------|---------|------|
| upgrade_task_signal | 2000 | 升级任务信号 |
| upgrade_status_signal | 2001 | 状态更新信号 |
| upgrade_result_signal | 2002 | 结果报告信号 |

## 错误处理

### 1. 连接失败处理
- AIDL连接失败：重试机制，最多重试3次
- DDS连接失败：等待订阅者连接，超时后继续发布

### 2. 数据传输失败
- JSON解析失败：记录错误日志，返回失败状态
- DDS发布失败：重试发布，记录失败原因

### 3. 任务执行失败
- 文件拷贝失败：清理已拷贝文件，更新任务状态为失败
- 域控执行失败：接收失败报告，记录错误信息

## 配置参数

### 升级任务配置
```kotlin
// 目标路径
const val TARGET_PATH = "/data/upgrade"

// 超时设置
const val COPY_TIMEOUT = 300000 // 5分钟
const val PUBLISH_TIMEOUT = 30000 // 30秒

// 重试次数
const val MAX_RETRY_COUNT = 3
```

### DDS配置
```kotlin
// Domain ID
const val DOMAIN_ID = 1

// Topic配置
const val TOPIC_NAME = "UPGRADE_TASK_STATUS"
const val QOS_RELIABILITY = "RELIABLE"
```

## 安全考虑

### 1. 权限控制
- 只有授权的App ID才能发布升级任务
- 验证升级包的数字签名
- 限制目标路径的写入权限

### 2. 数据完整性
- 文件MD5校验
- JSON数据格式验证
- DDS消息序列号检查

### 3. 异常恢复
- 任务状态持久化
- 断电恢复机制
- 回滚功能支持

## 性能优化

### 1. 并发处理
- 使用线程池处理文件拷贝
- 异步AIDL调用避免阻塞
- DDS发布使用独立线程

### 2. 内存管理
- 大文件分块传输
- 及时释放临时对象
- 控制并发任务数量

### 3. 网络优化
- DDS消息压缩
- 批量状态更新
- 智能重传机制

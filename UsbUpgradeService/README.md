# USB升级服务

## 项目概述

USB升级服务是一个独立的Android应用程序，专门用于检测U盘插入、分析升级包、管理升级任务，并通过S2S服务将升级任务分发到车内各域控。

## 主要功能

### 🔌 USB设备检测
- 实时监听U盘插入/拔出事件
- 自动检测已挂载的USB设备
- 支持多种USB存储设备

### 📦 升级包分析
- 自动扫描U盘中的升级文件
- 支持多种文件格式（bin, hex, img, tar, zip, apk等）
- 解析升级配置文件
- 生成升级任务信息

### 💾 存储管理
- 显示所有存储设备状态
- 实时监控存储空间使用情况
- 提供设备快速访问功能

### 📋 任务管理
- 升级文件拷贝到指定目录
- 生成JSON任务描述文件
- 任务状态跟踪和管理

### 🌐 S2S通信
- 通过AIDL与S2S服务通信
- 使用JSON格式传输数据
- 支持异步任务分发

## 项目结构

```
UsbUpgradeService/
├── src/main/java/com/seres/usb/upgrade/
│   ├── UsbUpgradeApplication.kt          # 应用程序类
│   ├── MainActivity.kt                   # 主界面
│   ├── adapter/
│   │   └── StorageDeviceAdapter.kt       # 存储设备适配器
│   ├── analyzer/
│   │   └── UpgradePackageAnalyzer.kt     # 升级包分析器
│   ├── manager/
│   │   └── UpgradeTaskManager.kt         # 升级任务管理器
│   ├── model/
│   │   ├── UpgradePackageInfo.kt         # 升级包信息模型
│   │   └── UpgradeTaskInfo.kt            # 升级任务信息模型
│   ├── publisher/
│   │   └── UpgradeTaskPublisher.kt       # 任务发布者
│   ├── receiver/
│   │   └── BootCompleteReceiver.kt       # 开机启动接收器
│   ├── service/
│   │   └── UsbDetectionService.kt        # USB检测服务
│   ├── storage/
│   │   └── StorageStatusManager.kt       # 存储状态管理器
│   └── utils/
│       └── LogUtils.kt                   # 日志工具类
├── src/main/aidl/                        # AIDL接口文件
├── src/main/res/                         # 资源文件
└── build.gradle                          # 构建配置
```

## 核心组件

### 1. UsbDetectionService
- **功能**: 前台服务，监听USB设备变化
- **特性**: 
  - 开机自启动
  - 实时状态通知
  - 广播状态变化

### 2. UpgradePackageAnalyzer
- **功能**: 分析U盘中的升级包
- **支持格式**: bin, hex, img, tar, zip, apk, so, conf, json, xml
- **特性**:
  - 自动识别文件类型
  - 提取版本信息
  - 计算文件校验和

### 3. UpgradeTaskManager
- **功能**: 管理升级任务生命周期
- **特性**:
  - 文件拷贝和验证
  - 任务状态跟踪
  - 并发任务处理

### 4. UpgradeTaskPublisher
- **功能**: 与S2S服务通信
- **特性**:
  - AIDL接口通信
  - JSON数据格式
  - 异步回调处理

### 5. StorageStatusManager
- **功能**: 存储设备状态管理
- **特性**:
  - 实时设备监控
  - 存储空间统计
  - 设备访问控制

## 安装和配置

### 系统要求
- Android 12+ (API 31+)
- 车载Android系统
- 存储访问权限

### 权限配置
```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

### 构建和安装
```bash
# 构建APK
./gradlew assembleDebug

# 安装到设备
adb install app/build/outputs/apk/debug/app-debug.apk
```

## 使用方法

### 1. 启动应用
- 应用会自动启动USB检测服务
- 开机后自动启动服务

### 2. 插入U盘
- 插入包含升级文件的U盘
- 服务自动检测并分析升级包
- 生成升级任务

### 3. 查看状态
- 打开应用查看存储设备状态
- 监控升级任务进度
- 查看日志信息

### 4. 升级配置
在U盘根目录创建 `upgrade_config.json`:
```json
{
  "taskName": "车载系统升级包 v2.1.0",
  "version": "2.1.0",
  "description": "包含HPCM、ZCUF等域控的固件升级包",
  "packages": [
    {
      "fileName": "hpcm_firmware_v2.1.0.bin",
      "name": "HPCM固件升级包",
      "version": "2.1.0",
      "targetEcu": "HPCM",
      "type": "firmware",
      "required": true
    }
  ]
}
```

## 与S2S服务集成

### 通信协议
- **接口**: AIDL
- **数据格式**: JSON字符串
- **传输方式**: Bundle参数

### 服务Hash映射
- `UPGRADE_TASK_SERVICE_HASH = 1000`: 发布升级任务
- `UPGRADE_STATUS_SERVICE_HASH = 1001`: 更新任务状态

### 集成步骤
1. 确保S2S服务正在运行
2. 配置正确的服务包名和Action
3. 在S2S服务中添加升级任务处理器
4. 实现DDS转发功能

## 日志和调试

### 日志文件
- 位置: `/Android/data/com.seres.usb.upgrade/files/logs/`
- 格式: `usb_upgrade_YYYYMMDD.log`
- 自动清理: 保留7天

### 调试标签
- `UsbUpgrade-UsbDetectionService`: USB检测
- `UsbUpgrade-UpgradePackageAnalyzer`: 包分析
- `UsbUpgrade-UpgradeTaskManager`: 任务管理
- `UsbUpgrade-UpgradeTaskPublisher`: 任务发布
- `UsbUpgrade-StorageStatusManager`: 存储管理

## 故障排除

### 常见问题

1. **USB检测不到**
   - 检查权限设置
   - 确认USB格式（推荐FAT32）
   - 查看服务运行状态

2. **升级包识别失败**
   - 检查文件扩展名
   - 验证配置文件格式
   - 查看分析日志

3. **S2S通信失败**
   - 确认S2S服务运行状态
   - 检查包名和Action配置
   - 验证AIDL接口版本

4. **文件拷贝失败**
   - 检查目标目录权限
   - 确认存储空间充足
   - 验证文件完整性

### 性能优化
- 使用线程池处理文件操作
- 异步处理大文件拷贝
- 及时释放资源
- 控制并发任务数量

## 扩展开发

### 添加新文件类型
在 `UpgradePackageAnalyzer` 中修改 `supportedExtensions`

### 自定义目标ECU
在 `guessTargetEcu()` 方法中添加识别规则

### 扩展通信协议
实现新的AIDL接口或添加新的服务Hash

## 版本历史

- **v1.0.0**: 初始版本，基本USB检测和升级包分析功能
- 支持多种文件格式
- 集成S2S通信
- 完整的存储管理界面

## 许可证

本项目为内部开发项目，版权归公司所有。

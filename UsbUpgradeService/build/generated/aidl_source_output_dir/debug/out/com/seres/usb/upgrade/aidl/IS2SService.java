/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.seres.usb.upgrade.aidl;
/** S2S服务接口 */
public interface IS2SService extends android.os.IInterface
{
  /** Default implementation for IS2SService. */
  public static class Default implements com.seres.usb.upgrade.aidl.IS2SService
  {
    /** 同步调用服务 */
    @Override public android.os.Bundle invoke(int appId, int serviceHashId, android.os.Bundle params) throws android.os.RemoteException
    {
      return null;
    }
    /** 异步调用服务 */
    @Override public void invokeAsync(int appId, int serviceHashId, android.os.Bundle params, com.seres.usb.upgrade.aidl.IAsyncResultCallback callback) throws android.os.RemoteException
    {
    }
    /** 注册信号监听器 */
    @Override public void registerS2SSignalListener(int appId, com.seres.usb.upgrade.aidl.IS2SReportListener listener, int[] initSignalHashIdList) throws android.os.RemoteException
    {
    }
    /** 取消注册信号监听器 */
    @Override public void unregisterS2SSignalListener(int appId) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.seres.usb.upgrade.aidl.IS2SService
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.seres.usb.upgrade.aidl.IS2SService interface,
     * generating a proxy if needed.
     */
    public static com.seres.usb.upgrade.aidl.IS2SService asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.seres.usb.upgrade.aidl.IS2SService))) {
        return ((com.seres.usb.upgrade.aidl.IS2SService)iin);
      }
      return new com.seres.usb.upgrade.aidl.IS2SService.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_invoke:
        {
          int _arg0;
          _arg0 = data.readInt();
          int _arg1;
          _arg1 = data.readInt();
          android.os.Bundle _arg2;
          _arg2 = _Parcel.readTypedObject(data, android.os.Bundle.CREATOR);
          android.os.Bundle _result = this.invoke(_arg0, _arg1, _arg2);
          reply.writeNoException();
          _Parcel.writeTypedObject(reply, _result, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
          break;
        }
        case TRANSACTION_invokeAsync:
        {
          int _arg0;
          _arg0 = data.readInt();
          int _arg1;
          _arg1 = data.readInt();
          android.os.Bundle _arg2;
          _arg2 = _Parcel.readTypedObject(data, android.os.Bundle.CREATOR);
          com.seres.usb.upgrade.aidl.IAsyncResultCallback _arg3;
          _arg3 = com.seres.usb.upgrade.aidl.IAsyncResultCallback.Stub.asInterface(data.readStrongBinder());
          this.invokeAsync(_arg0, _arg1, _arg2, _arg3);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_registerS2SSignalListener:
        {
          int _arg0;
          _arg0 = data.readInt();
          com.seres.usb.upgrade.aidl.IS2SReportListener _arg1;
          _arg1 = com.seres.usb.upgrade.aidl.IS2SReportListener.Stub.asInterface(data.readStrongBinder());
          int[] _arg2;
          _arg2 = data.createIntArray();
          this.registerS2SSignalListener(_arg0, _arg1, _arg2);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_unregisterS2SSignalListener:
        {
          int _arg0;
          _arg0 = data.readInt();
          this.unregisterS2SSignalListener(_arg0);
          reply.writeNoException();
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.seres.usb.upgrade.aidl.IS2SService
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      /** 同步调用服务 */
      @Override public android.os.Bundle invoke(int appId, int serviceHashId, android.os.Bundle params) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.Bundle _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(appId);
          _data.writeInt(serviceHashId);
          _Parcel.writeTypedObject(_data, params, 0);
          boolean _status = mRemote.transact(Stub.TRANSACTION_invoke, _data, _reply, 0);
          _reply.readException();
          _result = _Parcel.readTypedObject(_reply, android.os.Bundle.CREATOR);
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      /** 异步调用服务 */
      @Override public void invokeAsync(int appId, int serviceHashId, android.os.Bundle params, com.seres.usb.upgrade.aidl.IAsyncResultCallback callback) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(appId);
          _data.writeInt(serviceHashId);
          _Parcel.writeTypedObject(_data, params, 0);
          _data.writeStrongInterface(callback);
          boolean _status = mRemote.transact(Stub.TRANSACTION_invokeAsync, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      /** 注册信号监听器 */
      @Override public void registerS2SSignalListener(int appId, com.seres.usb.upgrade.aidl.IS2SReportListener listener, int[] initSignalHashIdList) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(appId);
          _data.writeStrongInterface(listener);
          _data.writeIntArray(initSignalHashIdList);
          boolean _status = mRemote.transact(Stub.TRANSACTION_registerS2SSignalListener, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      /** 取消注册信号监听器 */
      @Override public void unregisterS2SSignalListener(int appId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(appId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_unregisterS2SSignalListener, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
    }
    static final int TRANSACTION_invoke = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_invokeAsync = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_registerS2SSignalListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_unregisterS2SSignalListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
  }
  public static final java.lang.String DESCRIPTOR = "com.seres.usb.upgrade.aidl.IS2SService";
  /** 同步调用服务 */
  public android.os.Bundle invoke(int appId, int serviceHashId, android.os.Bundle params) throws android.os.RemoteException;
  /** 异步调用服务 */
  public void invokeAsync(int appId, int serviceHashId, android.os.Bundle params, com.seres.usb.upgrade.aidl.IAsyncResultCallback callback) throws android.os.RemoteException;
  /** 注册信号监听器 */
  public void registerS2SSignalListener(int appId, com.seres.usb.upgrade.aidl.IS2SReportListener listener, int[] initSignalHashIdList) throws android.os.RemoteException;
  /** 取消注册信号监听器 */
  public void unregisterS2SSignalListener(int appId) throws android.os.RemoteException;
  /** @hide */
  static class _Parcel {
    static private <T> T readTypedObject(
        android.os.Parcel parcel,
        android.os.Parcelable.Creator<T> c) {
      if (parcel.readInt() != 0) {
          return c.createFromParcel(parcel);
      } else {
          return null;
      }
    }
    static private <T extends android.os.Parcelable> void writeTypedObject(
        android.os.Parcel parcel, T value, int parcelableFlags) {
      if (value != null) {
        parcel.writeInt(1);
        value.writeToParcel(parcel, parcelableFlags);
      } else {
        parcel.writeInt(0);
      }
    }
  }
}

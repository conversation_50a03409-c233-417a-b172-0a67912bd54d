<libraries>
  <library
      name="androidx.databinding:viewbinding:8.6.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/146863973234875abc30cdcc92af2b0c/transformed/viewbinding-8.6.0/jars/classes.jar"
      resolved="androidx.databinding:viewbinding:8.6.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/146863973234875abc30cdcc92af2b0c/transformed/viewbinding-8.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.10.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/jars/classes.jar"
      resolved="com.google.android.material:material:1.10.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/83955a3621cb4fdfaa7dc6b5ec8ff01f/transformed/viewpager2-1.1.0-beta02/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0-beta02"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/83955a3621cb4fdfaa7dc6b5ec8ff01f/transformed/viewpager2-1.1.0-beta02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/cc45b9ac07c9a4e553b7e37f5d5d866f/transformed/fragment-1.3.6/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/cc45b9ac07c9a4e553b7e37f5d5d866f/transformed/fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/0d68aef391a1bef8cf69c14377ecc7db/transformed/activity-1.8.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/0d68aef391a1bef8cf69c14377ecc7db/transformed/activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.2@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/e613dbf6633ca10a897becde81915dea/transformed/recyclerview-1.3.2/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.2"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/e613dbf6633ca10a897becde81915dea/transformed/recyclerview-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/83d01cdcfc97a9ee1fa9cc750528eec7/transformed/appcompat-resources-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/83d01cdcfc97a9ee1fa9cc750528eec7/transformed/appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/20f2b4e8c0e7c2394a7da189c0ea435b/transformed/drawerlayout-1.1.1/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/20f2b4e8c0e7c2394a7da189c0ea435b/transformed/drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/cc0cf4df6578d734ca157e2604285932/transformed/coordinatorlayout-1.1.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/cc0cf4df6578d734ca157e2604285932/transformed/coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/49db75becd20ccde1c6547fe074d871b/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/49db75becd20ccde1c6547fe074d871b/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/29a1cbc1498aa2d2dc598ea4aef7531e/transformed/transition-1.2.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/29a1cbc1498aa2d2dc598ea4aef7531e/transformed/transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/c41ae0981c4e37b7f6687f6266df5d19/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/c41ae0981c4e37b7f6687f6266df5d19/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/a793849a76dabef9fb07a79cb2b4eca7/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/a793849a76dabef9fb07a79cb2b4eca7/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/48d3072d62b6e14d8958dffdbfca9eb0/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/48d3072d62b6e14d8958dffdbfca9eb0/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/36206f3ca5f1efcd5d76add3849a6416/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/36206f3ca5f1efcd5d76add3849a6416/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f0be03a2227e3e5e9a725862189d13a2/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f0be03a2227e3e5e9a725862189d13a2/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/df2cf72cc21e0b25a43581f7211b4825/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/df2cf72cc21e0b25a43581f7211b4825/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.10.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/jars/classes.jar"
      resolved="androidx.core:core:1.10.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/36e3f2de08975dc5e32f5ba858d74418/transformed/lifecycle-livedata-2.6.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/36e3f2de08975dc5e32f5ba858d74418/transformed/lifecycle-livedata-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.6.1/10f354fdb64868baecd67128560c5a0d6312c495/lifecycle-common-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.1"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/170ebf14fe6c2628397944d746c89cee/transformed/lifecycle-livedata-core-2.6.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/170ebf14fe6c2628397944d746c89cee/transformed/lifecycle-livedata-core-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/44823380f01d3b61034ea4642f0a606c/transformed/lifecycle-viewmodel-2.6.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/44823380f01d3b61034ea4642f0a606c/transformed/lifecycle-viewmodel-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/79c0fd544c62f74a7a7e75812eb06a1a/transformed/lifecycle-runtime-2.6.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/79c0fd544c62f74a7a7e75812eb06a1a/transformed/lifecycle-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f7f1e67d3a1d990fd27c335eb234d635/transformed/lifecycle-viewmodel-savedstate-2.6.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f7f1e67d3a1d990fd27c335eb234d635/transformed/lifecycle-viewmodel-savedstate-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.10.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/0e780b3bcd3646138bc1bd95d093899c/transformed/core-ktx-1.10.1/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.10.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/0e780b3bcd3646138bc1bd95d093899c/transformed/core-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:s2s-sdk-api::release"
      project=":s2s-sdk-api"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/156de66f05358e44cfb2fa9bde8c9f29/transformed/savedstate-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/156de66f05358e44cfb2fa9bde8c9f29/transformed/savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/40e363994e21beada7604a54f43c3c7e/transformed/annotation-experimental-1.3.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/40e363994e21beada7604a54f43c3c7e/transformed/annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/1335f47639b9d8ad8ede95e4cbc029ec/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/1335f47639b9d8ad8ede95e4cbc029ec/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/6bff64edba13d70e91a489be5347be91/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/6bff64edba13d70e91a489be5347be91/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/fde77f2acb4a205ab10bd3079ebd36ae/transformed/core-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/fde77f2acb4a205ab10bd3079ebd36ae/transformed/core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/ac5bc8f2471abf6cbff1c9ebdc2d0848/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/ac5bc8f2471abf6cbff1c9ebdc2d0848/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/5020891c53660ca6d738d1966fcc7307/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/5020891c53660ca6d738d1966fcc7307/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/6462eb6f120c2c633a0264aa5c519b5b/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/6462eb6f120c2c633a0264aa5c519b5b/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/13a692efac45ce84e3c1717540fe168c/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/13a692efac45ce84e3c1717540fe168c/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.6.0/a7257339a052df0f91433cf9651231bbb802b502/annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.6.4/2c997cd1c0ef33f3e751d3831929aeff1390cb30/kotlinx-coroutines-core-jvm-1.6.4.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.6.4/f955fc8b2ad196e2f4429598440e15f7492eeb2b/kotlinx-coroutines-android-1.6.4.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.22/b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1/kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.8.22/4dabb8248310d833bb6a8b516024a91fd3d275c/kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.24/9928532f12c66ad816a625b3f9984f8368ca6d2b/kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.10.1/b3add478d4382b78ea20b1671390a858002feb6c/gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.resourceinspection/resourceinspection-annotation/1.0.1/8c21f8ff5d96d5d52c948707f7e4d6ca6773feef/resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/853784ca09e9924abb5958855432070b/transformed/emoji2-views-helper-1.2.0/jars/classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/853784ca09e9924abb5958855432070b/transformed/emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/jars/libs/repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/912bdbf86f2221318b1f11a74465cffc/transformed/customview-poolingcontainer-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/912bdbf86f2221318b1f11a74465cffc/transformed/customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/jars/classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/dab1e70fbc520ac05302103961983552/transformed/tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/dab1e70fbc520ac05302103961983552/transformed/tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.1.0/50b7fb98350d5f42a4e49704b03278542293ba48/concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.15.0/38c8485a652f808c8c149150da4e5c2b0bd17f9a/error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-core/1.0.4/29cdbe03ded6b0980f63fa5da2579a430e911c40/constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>

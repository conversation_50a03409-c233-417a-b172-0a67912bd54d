<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res"><file name="activity_main" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/layout/activity_main.xml" qualifiers="" type="layout"/><file name="item_storage_device" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/layout/item_storage_device.xml" qualifiers="" type="layout"/><file name="file_paths" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/xml/file_paths.xml" qualifiers="" type="xml"/><file path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">USB升级服务</string></file><file name="ic_usb" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/drawable/ic_usb.xml" qualifiers="" type="drawable"/><file name="status_background" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/drawable/status_background.xml" qualifiers="" type="drawable"/><file name="section_background" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/drawable/section_background.xml" qualifiers="" type="drawable"/><file name="button_background" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/drawable/button_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-anydpi/ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-anydpi/ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-hdpi/ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-hdpi/ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-mdpi/ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-mdpi/ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xhdpi/ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xhdpi/ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="logo" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xhdpi/logo.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xxhdpi/ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xxhdpi/ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xxxhdpi/ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/values/colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/values/themes.xml" qualifiers=""><style name="Base.Theme.UsbUpgradeService" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.UsbUpgradeService" parent="Base.Theme.UsbUpgradeService"/></file><file path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/values-night/themes.xml" qualifiers="night-v8"><style name="Base.Theme.UsbUpgradeService" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/xml/backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/xml/data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_launcher_background" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/drawable/ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/res/drawable/ic_launcher_foreground.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/dds/s2sService/UsbUpgradeService/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/dds/s2sService/UsbUpgradeService/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/dds/s2sService/UsbUpgradeService/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
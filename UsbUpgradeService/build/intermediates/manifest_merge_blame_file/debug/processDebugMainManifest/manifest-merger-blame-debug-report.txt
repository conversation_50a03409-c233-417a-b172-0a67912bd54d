1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.seres.usb.upgrade"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:5-81
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:5-77
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:5-87
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:22-84
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:9:5-80
15-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:10:5-81
16-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
17-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:5-82
17-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:22-79
18    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
18-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:5-84
18-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:22-81
19    <uses-permission android:name="android.hardware.usb.host" />
19-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:5-65
19-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:22-62
20    <uses-permission android:name="android.permission.INTERNET" />
20-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:5-67
20-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:22-64
21
22    <permission
22-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
23        android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
27
28    <application
28-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:16:5-79:19
29        android:name="com.seres.usb.upgrade.UsbUpgradeApplication"
29-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:17:9-46
30        android:allowBackup="true"
30-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:18:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:9-54
36        android:icon="@mipmap/ic_launcher"
36-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:9-43
37        android:label="@string/app_name"
37-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:22:9-41
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:23:9-54
39        android:supportsRtl="true"
39-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:9-35
40        android:theme="@style/Theme.UsbUpgradeService" >
40-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:25:9-55
41
42        <!-- 主Activity -->
43        <activity
43-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:29:9-37:20
44            android:name="com.seres.usb.upgrade.MainActivity"
44-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:13-41
45            android:exported="true"
45-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:31:13-36
46            android:theme="@style/Theme.UsbUpgradeService" >
46-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:32:13-59
47            <intent-filter>
47-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:33:13-36:29
48                <action android:name="android.intent.action.MAIN" />
48-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:17-69
48-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:17-77
50-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:27-74
51            </intent-filter>
52        </activity>
53
54        <!-- USB检测服务 -->
55        <service
55-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:40:9-44:56
56            android:name="com.seres.usb.upgrade.service.UsbDetectionService"
56-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:41:13-56
57            android:enabled="true"
57-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:42:13-35
58            android:exported="false"
58-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:43:13-37
59            android:foregroundServiceType="dataSync" />
59-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:44:13-53
60
61        <!-- 升级任务服务 -->
62        <service
62-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:47:9-54:19
63            android:name="com.seres.usb.upgrade.service.UpgradeTaskService"
63-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:48:13-55
64            android:enabled="true"
64-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:49:13-35
65            android:exported="true" >
65-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:50:13-36
66            <intent-filter>
66-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:13-53:29
67                <action android:name="com.seres.usb.upgrade.action.UPGRADE_SERVICE" />
67-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:17-87
67-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:25-84
68            </intent-filter>
69        </service>
70
71        <!-- 开机启动接收器 -->
72        <receiver
72-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:57:9-66:20
73            android:name="com.seres.usb.upgrade.receiver.BootCompleteReceiver"
73-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:58:13-58
74            android:enabled="true"
74-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:59:13-35
75            android:exported="true" >
75-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:60:13-36
76            <intent-filter android:priority="1000" >
76-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:13-65:29
76-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:28-51
77                <action android:name="android.intent.action.BOOT_COMPLETED" />
77-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:62:17-79
77-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:62:25-76
78                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
78-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:63:17-82
78-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:63:25-79
79
80                <category android:name="android.intent.category.DEFAULT" />
80-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:17-76
80-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:27-73
81            </intent-filter>
82        </receiver>
83
84        <!-- FileProvider -->
85        <provider
86            android:name="androidx.core.content.FileProvider"
86-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:70:13-62
87            android:authorities="com.seres.usb.upgrade.fileprovider"
87-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:71:13-64
88            android:exported="false"
88-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:72:13-37
89            android:grantUriPermissions="true" >
89-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:73:13-47
90            <meta-data
90-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:74:13-76:54
91                android:name="android.support.FILE_PROVIDER_PATHS"
91-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:75:17-67
92                android:resource="@xml/file_paths" />
92-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:76:17-51
93        </provider>
94        <provider
94-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
96            android:authorities="com.seres.usb.upgrade.androidx-startup"
96-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
99                android:name="androidx.emoji2.text.EmojiCompatInitializer"
99-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
100                android:value="androidx.startup" />
100-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
107        </provider>
108
109        <receiver
109-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
110            android:name="androidx.profileinstaller.ProfileInstallReceiver"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
111            android:directBootAware="false"
111-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
112            android:enabled="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
113            android:exported="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
114            android:permission="android.permission.DUMP" >
114-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
116                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
116-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
119                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
119-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
122                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
122-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
125                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
125-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
125-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
126            </intent-filter>
127        </receiver>
128    </application>
129
130</manifest>

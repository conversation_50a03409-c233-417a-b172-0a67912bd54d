1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.seres.usb.upgrade"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:5-81
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:5-77
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:5-87
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:22-84
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:9:5-80
15-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:10:5-81
16-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
17-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:5-82
17-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:22-79
18    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
18-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:5-84
18-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:22-81
19    <uses-permission android:name="android.hardware.usb.host" />
19-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:5-65
19-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:22-62
20    <uses-permission android:name="android.permission.INTERNET" />
20-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:5-67
20-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:22-64
21
22    <permission
22-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
23        android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
27
28    <application
28-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:16:5-79:19
29        android:name="com.seres.usb.upgrade.UsbUpgradeApplication"
29-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:17:9-46
30        android:allowBackup="true"
30-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:18:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:9-65
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:9-54
35        android:icon="@mipmap/ic_launcher"
35-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:9-43
36        android:label="@string/app_name"
36-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:22:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:23:9-54
38        android:supportsRtl="true"
38-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:9-35
39        android:theme="@style/Theme.UsbUpgradeService" >
39-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:25:9-55
40
41        <!-- 主Activity -->
42        <activity
42-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:29:9-37:20
43            android:name="com.seres.usb.upgrade.MainActivity"
43-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:13-41
44            android:exported="true"
44-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:31:13-36
45            android:theme="@style/Theme.UsbUpgradeService" >
45-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:32:13-59
46            <intent-filter>
46-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:33:13-36:29
47                <action android:name="android.intent.action.MAIN" />
47-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:17-69
47-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:17-77
49-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- USB检测服务 -->
54        <service
54-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:40:9-44:56
55            android:name="com.seres.usb.upgrade.service.UsbDetectionService"
55-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:41:13-56
56            android:enabled="true"
56-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:42:13-35
57            android:exported="false"
57-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:43:13-37
58            android:foregroundServiceType="dataSync" />
58-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:44:13-53
59
60        <!-- 升级任务服务 -->
61        <service
61-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:47:9-54:19
62            android:name="com.seres.usb.upgrade.service.UpgradeTaskService"
62-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:48:13-55
63            android:enabled="true"
63-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:49:13-35
64            android:exported="true" >
64-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:50:13-36
65            <intent-filter>
65-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:13-53:29
66                <action android:name="com.seres.usb.upgrade.action.UPGRADE_SERVICE" />
66-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:17-87
66-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:25-84
67            </intent-filter>
68        </service>
69
70        <!-- 开机启动接收器 -->
71        <receiver
71-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:57:9-66:20
72            android:name="com.seres.usb.upgrade.receiver.BootCompleteReceiver"
72-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:58:13-58
73            android:enabled="true"
73-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:59:13-35
74            android:exported="true" >
74-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:60:13-36
75            <intent-filter android:priority="1000" >
75-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:13-65:29
75-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:28-51
76                <action android:name="android.intent.action.BOOT_COMPLETED" />
76-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:62:17-79
76-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:62:25-76
77                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
77-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:63:17-82
77-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:63:25-79
78
79                <category android:name="android.intent.category.DEFAULT" />
79-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:17-76
79-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:27-73
80            </intent-filter>
81        </receiver>
82
83        <!-- FileProvider -->
84        <provider
85            android:name="androidx.core.content.FileProvider"
85-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:70:13-62
86            android:authorities="com.seres.usb.upgrade.fileprovider"
86-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:71:13-64
87            android:exported="false"
87-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:72:13-37
88            android:grantUriPermissions="true" >
88-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:73:13-47
89            <meta-data
89-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:74:13-76:54
90                android:name="android.support.FILE_PROVIDER_PATHS"
90-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:75:17-67
91                android:resource="@xml/file_paths" />
91-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:76:17-51
92        </provider>
93        <provider
93-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
94            android:name="androidx.startup.InitializationProvider"
94-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
95            android:authorities="com.seres.usb.upgrade.androidx-startup"
95-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
96            android:exported="false" >
96-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
97            <meta-data
97-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
98                android:name="androidx.emoji2.text.EmojiCompatInitializer"
98-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
99                android:value="androidx.startup" />
99-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
104                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
104-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
105                android:value="androidx.startup" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
106        </provider>
107
108        <receiver
108-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
109            android:name="androidx.profileinstaller.ProfileInstallReceiver"
109-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
110            android:directBootAware="false"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
111            android:enabled="true"
111-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
112            android:exported="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
113            android:permission="android.permission.DUMP" >
113-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
115                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
115-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
115-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
118                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
118-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
118-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
119            </intent-filter>
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
121                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
121-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
121-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
122            </intent-filter>
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
124                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
124-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
124-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
125            </intent-filter>
126        </receiver>
127    </application>
128
129</manifest>

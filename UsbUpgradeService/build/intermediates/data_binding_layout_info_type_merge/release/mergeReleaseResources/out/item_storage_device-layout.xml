<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_storage_device" modulePackage="com.seres.usb.upgrade" filePath="UsbUpgradeService/src/main/res/layout/item_storage_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_storage_device_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="110" endOffset="35"/></Target><Target id="@+id/tvDeviceName" view="TextView"><Expressions/><location startLine="22" startOffset="12" endLine="30" endOffset="58"/></Target><Target id="@+id/tvDeviceStatus" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="41" endOffset="58"/></Target><Target id="@+id/tvDevicePath" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="54" endOffset="44"/></Target><Target id="@+id/tvStorageInfo" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="64" endOffset="54"/></Target><Target id="@+id/progressBarUsage" view="ProgressBar"><Expressions/><location startLine="74" startOffset="12" endLine="82" endOffset="72"/></Target><Target id="@+id/tvUsagePercentage" view="TextView"><Expressions/><location startLine="84" startOffset="12" endLine="92" endOffset="58"/></Target><Target id="@+id/btnOpenDevice" view="Button"><Expressions/><location startLine="97" startOffset="8" endLine="106" endOffset="35"/></Target></Targets></Layout>
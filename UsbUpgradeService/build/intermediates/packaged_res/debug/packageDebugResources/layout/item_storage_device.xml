<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 设备名称和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvDeviceName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="设备名称"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/tvDeviceStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已挂载"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@drawable/status_background"
                android:padding="4dp"
                android:textColor="@android:color/white" />

        </LinearLayout>

        <!-- 设备路径 -->
        <TextView
            android:id="@+id/tvDevicePath"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="/storage/device_path"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:fontFamily="monospace" />

        <!-- 存储信息 -->
        <TextView
            android:id="@+id/tvStorageInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="总容量: 32.0 GB | 已用: 8.5 GB | 可用: 23.5 GB"
            android:textSize="12sp"
            android:textColor="@android:color/black" />

        <!-- 使用率进度条 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ProgressBar
                android:id="@+id/progressBarUsage"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="26"
                android:progressTint="@android:color/holo_green_light" />

            <TextView
                android:id="@+id/tvUsagePercentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="26%"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <Button
            android:id="@+id/btnOpenDevice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="打开设备"
            android:textSize="14sp"
            android:background="@drawable/button_background"
            android:textColor="@android:color/white"
            android:padding="8dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>

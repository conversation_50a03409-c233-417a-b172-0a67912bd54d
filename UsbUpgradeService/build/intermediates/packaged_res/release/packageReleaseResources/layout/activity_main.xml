<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="USB升级服务"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="20dp" />

        <!-- 服务控制区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="服务控制"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tvServiceStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="服务状态: 运行中"
                android:textSize="14sp"
                android:textColor="@android:color/holo_green_dark"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnStartService"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="启动服务"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btnStopService"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="停止服务"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 存储状态区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="存储设备状态"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tvStorageStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="检测到 0 个存储设备"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <!-- USB状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/tvUsbStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="未检测到USB设备"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/holo_red_dark" />

                <Button
                    android:id="@+id/btnOpenUsb"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="打开USB"
                    android:textSize="12sp"
                    android:enabled="false" />

            </LinearLayout>

            <Button
                android:id="@+id/btnRefreshStorage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="刷新存储状态"
                android:layout_marginBottom="12dp" />

            <!-- 存储设备列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewStorageDevices"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="300dp"
                android:scrollbars="vertical" />

        </LinearLayout>

        <!-- 任务管理区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="任务管理"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tvTaskCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="活跃任务: 0"
                android:textSize="14sp"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnShowActiveTasks"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="显示活跃任务"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btnClearLogs"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="清理日志"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>

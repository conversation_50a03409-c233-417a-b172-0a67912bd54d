com.seres.usb.upgrade:xml/file_paths = 0x7f120002
com.seres.usb.upgrade:styleable/include = 0x7f110098
com.seres.usb.upgrade:styleable/ViewTransition = 0x7f110097
com.seres.usb.upgrade:styleable/View = 0x7f110093
com.seres.usb.upgrade:styleable/Variant = 0x7f110092
com.seres.usb.upgrade:styleable/Transform = 0x7f110090
com.seres.usb.upgrade:styleable/TextAppearance = 0x7f110089
com.seres.usb.upgrade:styleable/TabLayout = 0x7f110088
com.seres.usb.upgrade:styleable/TabItem = 0x7f110087
com.seres.usb.upgrade:styleable/Snackbar = 0x7f11007e
com.seres.usb.upgrade:styleable/ShapeableImageView = 0x7f11007b
com.seres.usb.upgrade:styleable/SearchView = 0x7f110079
com.seres.usb.upgrade:styleable/ScrimInsetsFrameLayout = 0x7f110076
com.seres.usb.upgrade:styleable/RadialViewGroup = 0x7f110072
com.seres.usb.upgrade:styleable/PropertySet = 0x7f110071
com.seres.usb.upgrade:styleable/NavigationRailView = 0x7f11006b
com.seres.usb.upgrade:styleable/NavigationBarActiveIndicator = 0x7f110069
com.seres.usb.upgrade:styleable/MotionLayout = 0x7f110066
com.seres.usb.upgrade:styleable/MotionEffect = 0x7f110063
com.seres.usb.upgrade:styleable/MockView = 0x7f110061
com.seres.usb.upgrade:styleable/MenuView = 0x7f110060
com.seres.usb.upgrade:styleable/MenuGroup = 0x7f11005e
com.seres.usb.upgrade:styleable/MaterialTextAppearance = 0x7f11005a
com.seres.usb.upgrade:styleable/MaterialSwitch = 0x7f110059
com.seres.usb.upgrade:styleable/MaterialRadioButton = 0x7f110057
com.seres.usb.upgrade:styleable/MaterialCheckBoxStates = 0x7f110055
com.seres.usb.upgrade:styleable/MaterialCheckBox = 0x7f110054
com.seres.usb.upgrade:styleable/MaterialCardView = 0x7f110053
com.seres.usb.upgrade:styleable/MaterialCalendar = 0x7f110051
com.seres.usb.upgrade:styleable/MaterialButton = 0x7f11004f
com.seres.usb.upgrade:styleable/MaterialAutoCompleteTextView = 0x7f11004e
com.seres.usb.upgrade:styleable/MaterialAlertDialogTheme = 0x7f11004d
com.seres.usb.upgrade:styleable/LinearProgressIndicator = 0x7f11004a
com.seres.usb.upgrade:styleable/Layout = 0x7f110047
com.seres.usb.upgrade:styleable/KeyTimeCycle = 0x7f110045
com.seres.usb.upgrade:styleable/KeyFramesVelocity = 0x7f110043
com.seres.usb.upgrade:styleable/FragmentContainerView = 0x7f11003a
com.seres.usb.upgrade:styleable/ForegroundLinearLayout = 0x7f110038
com.seres.usb.upgrade:styleable/FontFamilyFont = 0x7f110037
com.seres.usb.upgrade:styleable/FontFamily = 0x7f110036
com.seres.usb.upgrade:styleable/FlowLayout = 0x7f110035
com.seres.usb.upgrade:styleable/FloatingActionButton = 0x7f110033
com.seres.usb.upgrade:styleable/ExtendedFloatingActionButton = 0x7f110031
com.seres.usb.upgrade:styleable/CoordinatorLayout = 0x7f11002c
com.seres.usb.upgrade:styleable/ConstraintSet = 0x7f11002b
com.seres.usb.upgrade:styleable/ConstraintOverride = 0x7f11002a
com.seres.usb.upgrade:styleable/ConstraintLayout_placeholder = 0x7f110029
com.seres.usb.upgrade:styleable/Constraint = 0x7f110026
com.seres.usb.upgrade:styleable/CompoundButton = 0x7f110025
com.seres.usb.upgrade:styleable/ColorStateListItem = 0x7f110024
com.seres.usb.upgrade:styleable/CollapsingToolbarLayout = 0x7f110022
com.seres.usb.upgrade:styleable/ClockHandView = 0x7f110021
com.seres.usb.upgrade:styleable/ClockFaceView = 0x7f110020
com.seres.usb.upgrade:styleable/CardView = 0x7f11001a
com.seres.usb.upgrade:styleable/Capability = 0x7f110019
com.seres.usb.upgrade:styleable/BottomNavigationView = 0x7f110016
com.seres.usb.upgrade:styleable/BaseProgressIndicator = 0x7f110014
com.seres.usb.upgrade:styleable/Badge = 0x7f110013
com.seres.usb.upgrade:styleable/AppCompatTheme = 0x7f110012
com.seres.usb.upgrade:styleable/AppCompatTextView = 0x7f110011
com.seres.usb.upgrade:styleable/AppCompatTextHelper = 0x7f110010
com.seres.usb.upgrade:styleable/AppCompatSeekBar = 0x7f11000f
com.seres.usb.upgrade:styleable/AppBarLayout_Layout = 0x7f11000c
com.seres.usb.upgrade:styleable/AppBarLayoutStates = 0x7f11000b
com.seres.usb.upgrade:styleable/AppBarLayout = 0x7f11000a
com.seres.usb.upgrade:styleable/AnimatedStateListDrawableCompat = 0x7f110007
com.seres.usb.upgrade:styleable/ActionMode = 0x7f110004
com.seres.usb.upgrade:styleable/ActionMenuView = 0x7f110003
com.seres.usb.upgrade:styleable/ActionBarLayout = 0x7f110001
com.seres.usb.upgrade:styleable/ActionBar = 0x7f110000
com.seres.usb.upgrade:style/Widget.Support.CoordinatorLayout = 0x7f10045c
com.seres.usb.upgrade:style/Widget.MaterialComponents.Tooltip = 0x7f10045b
com.seres.usb.upgrade:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f10045a
com.seres.usb.upgrade:style/Widget.MaterialComponents.Toolbar = 0x7f100457
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f100456
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f100455
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f100453
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f100452
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Display = 0x7f100450
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextView = 0x7f10044c
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f10044b
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f100448
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f100442
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f100440
com.seres.usb.upgrade:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f10043e
com.seres.usb.upgrade:style/Widget.MaterialComponents.ProgressIndicator = 0x7f100437
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialDivider = 0x7f10042c
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f100429
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f100428
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f100425
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f100424
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f100421
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f10041f
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f10041e
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f10041c
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f10041b
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f100419
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f100416
com.seres.usb.upgrade:styleable/StateListDrawableItem = 0x7f110083
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f100415
com.seres.usb.upgrade:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f10040b
com.seres.usb.upgrade:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f10040a
com.seres.usb.upgrade:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f100406
com.seres.usb.upgrade:style/Widget.MaterialComponents.ChipGroup = 0x7f100405
com.seres.usb.upgrade:style/Widget.MaterialComponents.Chip.Entry = 0x7f100403
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f1003fe
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f1003fb
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f1003f8
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f1003f6
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f1003f0
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomNavigationView = 0x7f1003ee
com.seres.usb.upgrade:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1003e6
com.seres.usb.upgrade:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1003e4
com.seres.usb.upgrade:style/Widget.MaterialComponents.ActionMode = 0x7f1003e2
com.seres.usb.upgrade:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1003e0
com.seres.usb.upgrade:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1003de
com.seres.usb.upgrade:style/Widget.Material3.Tooltip = 0x7f1003dd
com.seres.usb.upgrade:style/Widget.Material3.Toolbar.OnSurface = 0x7f1003db
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1003d9
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1003d8
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1003d6
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1003d5
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1003d4
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1003d3
com.seres.usb.upgrade:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1003d0
com.seres.usb.upgrade:style/Widget.Material3.TabLayout.OnSurface = 0x7f1003cc
com.seres.usb.upgrade:style/Widget.Material3.TabLayout = 0x7f1003cb
com.seres.usb.upgrade:style/Widget.Material3.Snackbar.FullWidth = 0x7f1003c9
com.seres.usb.upgrade:style/Widget.Material3.Snackbar = 0x7f1003c8
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f100427
com.seres.usb.upgrade:style/Widget.Material3.Slider.Label = 0x7f1003c7
com.seres.usb.upgrade:style/Widget.Material3.SideSheet.Modal = 0x7f1003c4
com.seres.usb.upgrade:style/Widget.Material3.SearchView.Prefix = 0x7f1003c0
com.seres.usb.upgrade:style/Widget.Material3.SearchBar.Outlined = 0x7f1003be
com.seres.usb.upgrade:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1003bb
com.seres.usb.upgrade:style/Widget.Material3.PopupMenu.Overflow = 0x7f1003ba
com.seres.usb.upgrade:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1003b9
com.seres.usb.upgrade:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1003b8
com.seres.usb.upgrade:style/Widget.Material3.PopupMenu = 0x7f1003b7
com.seres.usb.upgrade:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1003b4
com.seres.usb.upgrade:style/Widget.Material3.NavigationRailView = 0x7f1003b3
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1003af
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1003ae
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1003ab
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1003a5
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Item = 0x7f1003a1
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1003a0
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f10039c
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f100399
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f100394
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f100393
com.seres.usb.upgrade:style/Widget.Material3.LinearProgressIndicator = 0x7f10038f
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f10038d
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f10038b
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f100389
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f100388
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Primary = 0x7f100386
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f100385
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f100384
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f100383
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f100382
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f10037f
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f10037a
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker = 0x7f10044d
com.seres.usb.upgrade:style/Widget.Material3.SideSheet = 0x7f1003c2
com.seres.usb.upgrade:style/Widget.Material3.DrawerLayout = 0x7f100379
com.seres.usb.upgrade:style/Widget.Material3.CompoundButton.RadioButton = 0x7f100377
com.seres.usb.upgrade:style/Widget.Material3.CompoundButton.CheckBox = 0x7f100375
com.seres.usb.upgrade:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f100374
com.seres.usb.upgrade:style/Widget.Material3.CollapsingToolbar.Large = 0x7f100373
com.seres.usb.upgrade:style/Widget.Material3.CollapsingToolbar = 0x7f100372
com.seres.usb.upgrade:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f100370
com.seres.usb.upgrade:style/Widget.Material3.ChipGroup = 0x7f10036d
com.seres.usb.upgrade:style/Widget.Material3.Chip.Input.Elevated = 0x7f100368
com.seres.usb.upgrade:style/Widget.Material3.Chip.Assist.Elevated = 0x7f100364
com.seres.usb.upgrade:style/Widget.Material3.Chip.Assist = 0x7f100363
com.seres.usb.upgrade:style/Widget.Material3.CardView.Filled = 0x7f100360
com.seres.usb.upgrade:style/Widget.Material3.Button.TonalButton.Icon = 0x7f10035d
com.seres.usb.upgrade:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f100355
com.seres.usb.upgrade:style/Widget.Material3.Button.IconButton.Outlined = 0x7f100353
com.seres.usb.upgrade:style/Widget.Material3.Button.Icon = 0x7f10034f
com.seres.usb.upgrade:style/Widget.Material3.BottomSheet.Modal = 0x7f10034b
com.seres.usb.upgrade:style/Widget.Material3.BottomSheet.DragHandle = 0x7f10034a
com.seres.usb.upgrade:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f100345
com.seres.usb.upgrade:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f100340
com.seres.usb.upgrade:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f10033f
com.seres.usb.upgrade:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f10033e
com.seres.usb.upgrade:style/Widget.Material3.ActionBar.Solid = 0x7f10033b
com.seres.usb.upgrade:styleable/ScrollingViewBehavior_Layout = 0x7f110077
com.seres.usb.upgrade:style/Widget.Design.TabLayout = 0x7f100338
com.seres.usb.upgrade:style/Widget.Design.Snackbar = 0x7f100337
com.seres.usb.upgrade:style/Widget.Design.NavigationView = 0x7f100335
com.seres.usb.upgrade:style/Widget.Design.BottomSheet.Modal = 0x7f100332
com.seres.usb.upgrade:style/Widget.AppCompat.Toolbar = 0x7f10032c
com.seres.usb.upgrade:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f10032b
com.seres.usb.upgrade:style/Widget.AppCompat.Spinner.DropDown = 0x7f100327
com.seres.usb.upgrade:style/Widget.AppCompat.SeekBar = 0x7f100324
com.seres.usb.upgrade:style/Widget.AppCompat.RatingBar.Indicator = 0x7f100320
com.seres.usb.upgrade:style/Widget.AppCompat.RatingBar = 0x7f10031f
com.seres.usb.upgrade:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f10042f
com.seres.usb.upgrade:style/Widget.AppCompat.PopupWindow = 0x7f10031c
com.seres.usb.upgrade:style/Widget.AppCompat.ListView.Menu = 0x7f100319
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f10042b
com.seres.usb.upgrade:style/Widget.AppCompat.ListView.DropDown = 0x7f100318
com.seres.usb.upgrade:style/Widget.AppCompat.ListView = 0x7f100317
com.seres.usb.upgrade:style/Widget.AppCompat.ListMenuView = 0x7f100315
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f100310
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f10030c
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f10030b
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f100309
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionButton = 0x7f100308
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f100306
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f100304
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f100300
com.seres.usb.upgrade:styleable/AppCompatImageView = 0x7f11000e
com.seres.usb.upgrade:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f1002fc
com.seres.usb.upgrade:style/Widget.AppCompat.DrawerArrowToggle = 0x7f1002fb
com.seres.usb.upgrade:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f1002f8
com.seres.usb.upgrade:style/Widget.AppCompat.Button.Small = 0x7f1002f5
com.seres.usb.upgrade:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f10031e
com.seres.usb.upgrade:style/Widget.AppCompat.Button = 0x7f1002f0
com.seres.usb.upgrade:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1002ec
com.seres.usb.upgrade:style/Widget.AppCompat.SearchView = 0x7f100322
com.seres.usb.upgrade:style/Widget.AppCompat.ActionButton = 0x7f1002ea
com.seres.usb.upgrade:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1002e7
com.seres.usb.upgrade:style/Widget.AppCompat.ActionBar.Solid = 0x7f1002e6
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1002e4
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1002e3
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1002e0
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1002df
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1002dc
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1002d8
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1002d7
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1002d4
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1002d2
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Light = 0x7f1002cf
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1002cd
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1002cc
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1002c9
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1002c7
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1002c6
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1002c4
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1002bf
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents = 0x7f1002bd
com.seres.usb.upgrade:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1002b9
com.seres.usb.upgrade:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1002b7
com.seres.usb.upgrade:style/ThemeOverlay.Material3.TabLayout = 0x7f1002b5
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Snackbar = 0x7f1002b4
com.seres.usb.upgrade:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1002b3
com.seres.usb.upgrade:style/ThemeOverlay.Material3.NavigationView = 0x7f1002b0
com.seres.usb.upgrade:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1002af
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1002a9
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1002a8
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Light = 0x7f1002a6
com.seres.usb.upgrade:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1002a3
com.seres.usb.upgrade:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1002a2
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1002d1
com.seres.usb.upgrade:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f10029d
com.seres.usb.upgrade:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f10029c
com.seres.usb.upgrade:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f10029b
com.seres.usb.upgrade:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f10029a
com.seres.usb.upgrade:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f100299
com.seres.usb.upgrade:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f100295
com.seres.usb.upgrade:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f100294
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Chip.Assist = 0x7f100291
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Chip = 0x7f100290
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f10028e
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.TextButton = 0x7f10028d
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f10028c
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.IconButton = 0x7f10028a
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f100289
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button = 0x7f100288
com.seres.usb.upgrade:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f100287
com.seres.usb.upgrade:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f100285
com.seres.usb.upgrade:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f10031b
com.seres.usb.upgrade:style/ThemeOverlay.Material3.BottomAppBar = 0x7f100284
com.seres.usb.upgrade:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f100282
com.seres.usb.upgrade:style/ThemeOverlay.Material3.ActionBar = 0x7f10027e
com.seres.usb.upgrade:style/ThemeOverlay.Design.TextInputEditText = 0x7f10027c
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.Light = 0x7f10027b
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f10027a
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f100276
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.Dark = 0x7f100275
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f1003ef
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.ActionBar = 0x7f100274
com.seres.usb.upgrade:style/Theme.UsbUpgradeService = 0x7f100272
com.seres.usb.upgrade:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f100271
com.seres.usb.upgrade:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f10036c
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f10026f
com.seres.usb.upgrade:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f100348
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f10026c
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f10026b
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f10026a
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f100269
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f100267
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f100266
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f100264
com.seres.usb.upgrade:style/Widget.AppCompat.SeekBar.Discrete = 0x7f100325
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Bridge = 0x7f100262
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light = 0x7f100260
com.seres.usb.upgrade:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f10025f
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.Alert = 0x7f100258
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f100255
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f10024f
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f10024c
com.seres.usb.upgrade:styleable/Carousel = 0x7f11001b
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f10024b
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f100249
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight = 0x7f100247
com.seres.usb.upgrade:style/Theme.MaterialComponents.Bridge = 0x7f100245
com.seres.usb.upgrade:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f100244
com.seres.usb.upgrade:style/Theme.Material3.Light.SideSheetDialog = 0x7f100242
com.seres.usb.upgrade:style/Theme.Material3.Light.NoActionBar = 0x7f100241
com.seres.usb.upgrade:style/Theme.Material3.Light.DialogWhenLarge = 0x7f100240
com.seres.usb.upgrade:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f10023f
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f1003ed
com.seres.usb.upgrade:style/Theme.Material3.Light.BottomSheetDialog = 0x7f10023c
com.seres.usb.upgrade:style/Theme.Material3.Light = 0x7f10023b
com.seres.usb.upgrade:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f100237
com.seres.usb.upgrade:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f100235
com.seres.usb.upgrade:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1002bc
com.seres.usb.upgrade:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f100233
com.seres.usb.upgrade:style/Theme.Material3.Dark.SideSheetDialog = 0x7f10022f
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar = 0x7f100391
com.seres.usb.upgrade:style/Theme.Material3.Dark.NoActionBar = 0x7f10022e
com.seres.usb.upgrade:style/Theme.Material3.Dark = 0x7f100228
com.seres.usb.upgrade:style/Theme.Design.Light.BottomSheetDialog = 0x7f100225
com.seres.usb.upgrade:style/Theme.Design.BottomSheetDialog = 0x7f100223
com.seres.usb.upgrade:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f10021d
com.seres.usb.upgrade:style/Theme.AppCompat.Light.Dialog = 0x7f10021c
com.seres.usb.upgrade:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f100408
com.seres.usb.upgrade:style/Theme.AppCompat.Light.DarkActionBar = 0x7f10021b
com.seres.usb.upgrade:style/Theme.AppCompat.Light = 0x7f10021a
com.seres.usb.upgrade:style/Theme.AppCompat.Empty = 0x7f100219
com.seres.usb.upgrade:style/Theme.AppCompat.DialogWhenLarge = 0x7f100218
com.seres.usb.upgrade:style/Theme.AppCompat.Dialog.MinWidth = 0x7f100217
com.seres.usb.upgrade:style/Theme.AppCompat.Dialog.Alert = 0x7f100216
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f100213
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight = 0x7f10020e
com.seres.usb.upgrade:style/Theme.AppCompat = 0x7f10020c
com.seres.usb.upgrade:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f10020a
com.seres.usb.upgrade:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f100209
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Tooltip = 0x7f100208
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f100207
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f100206
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Headline5 = 0x7f100202
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Headline4 = 0x7f100201
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Chip = 0x7f1001fd
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Caption = 0x7f1001fc
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Body1 = 0x7f1001f9
com.seres.usb.upgrade:style/TextAppearance.Material3.TitleSmall = 0x7f1001f7
com.seres.usb.upgrade:style/TextAppearance.Material3.TitleMedium = 0x7f1001f6
com.seres.usb.upgrade:style/TextAppearance.Material3.SearchBar = 0x7f1001f2
com.seres.usb.upgrade:style/TextAppearance.Material3.HeadlineLarge = 0x7f1001eb
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f100278
com.seres.usb.upgrade:style/TextAppearance.Material3.DisplayMedium = 0x7f1001e9
com.seres.usb.upgrade:style/TextAppearance.Material3.DisplayLarge = 0x7f1001e8
com.seres.usb.upgrade:style/TextAppearance.Material3.BodyLarge = 0x7f1001e5
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1001de
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1001da
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1001d8
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1001d6
com.seres.usb.upgrade:style/TextAppearance.Design.Tab = 0x7f1001d3
com.seres.usb.upgrade:style/TextAppearance.Design.Suffix = 0x7f1001d2
com.seres.usb.upgrade:style/TextAppearance.Design.Prefix = 0x7f1001d0
com.seres.usb.upgrade:style/TextAppearance.Design.Placeholder = 0x7f1001cf
com.seres.usb.upgrade:style/TextAppearance.Design.Error = 0x7f1001cc
com.seres.usb.upgrade:style/TextAppearance.Design.Counter.Overflow = 0x7f1001cb
com.seres.usb.upgrade:style/TextAppearance.Compat.Notification.Title = 0x7f1001c8
com.seres.usb.upgrade:style/TextAppearance.Compat.Notification.Line2 = 0x7f1001c6
com.seres.usb.upgrade:style/TextAppearance.Compat.Notification.Info = 0x7f1001c5
com.seres.usb.upgrade:styleable/CustomAttribute = 0x7f11002e
com.seres.usb.upgrade:style/TextAppearance.Compat.Notification = 0x7f1001c4
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1001c3
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1001c0
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1001be
com.seres.usb.upgrade:style/TextAppearance.Material3.TitleLarge = 0x7f1001f5
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1001b6
com.seres.usb.upgrade:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f10030d
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1001b4
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1001b1
com.seres.usb.upgrade:style/Widget.Material3.Button.TextButton.Dialog = 0x7f100357
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Tooltip = 0x7f1001b0
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1001ad
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Subhead = 0x7f1001ac
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1001ab
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f100303
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Small = 0x7f1001aa
com.seres.usb.upgrade:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1001a9
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Medium = 0x7f1001a5
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1001a4
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1001a1
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1001a0
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Inverse = 0x7f10019e
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Display2 = 0x7f10019a
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Display1 = 0x7f100199
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Caption = 0x7f100198
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f100193
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f10018e
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f10018c
com.seres.usb.upgrade:styleable/Spinner = 0x7f110080
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f10018b
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f100188
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.Icon = 0x7f1003f4
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f100186
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f100185
com.seres.usb.upgrade:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f10017e
com.seres.usb.upgrade:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f10017d
com.seres.usb.upgrade:style/ShapeAppearance.MaterialComponents = 0x7f10017b
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Tooltip = 0x7f10017a
com.seres.usb.upgrade:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f100178
com.seres.usb.upgrade:style/ShapeAppearance.Material3.MediumComponent = 0x7f100177
com.seres.usb.upgrade:style/ShapeAppearance.Material3.LargeComponent = 0x7f100176
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.None = 0x7f100174
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.Full = 0x7f100171
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f100170
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f10016c
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f10016a
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f100169
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f100168
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f100167
com.seres.usb.upgrade:style/Theme.Material3.Dark.Dialog = 0x7f10022a
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f100165
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f100162
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f100160
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f10015f
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f10015e
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomSheet = 0x7f1003f1
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f10015d
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f100151
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f10014f
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f10014e
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f10014c
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f10014a
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f100149
com.seres.usb.upgrade:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f100145
com.seres.usb.upgrade:style/Platform.Widget.AppCompat.Spinner = 0x7f100144
com.seres.usb.upgrade:style/Platform.V25.AppCompat = 0x7f100142
com.seres.usb.upgrade:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f10013e
com.seres.usb.upgrade:style/Platform.MaterialComponents.Light.Dialog = 0x7f10013c
com.seres.usb.upgrade:style/Platform.MaterialComponents = 0x7f100139
com.seres.usb.upgrade:style/Platform.AppCompat.Light = 0x7f100138
com.seres.usb.upgrade:style/Platform.AppCompat = 0x7f100137
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f100251
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f100136
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f100130
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f10012f
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents = 0x7f10012d
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Title.Text = 0x7f10012b
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f10012a
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f100129
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f100128
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f100126
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Body.Text = 0x7f100125
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Animation = 0x7f100124
com.seres.usb.upgrade:styleable/MotionScene = 0x7f110067
com.seres.usb.upgrade:style/CardView.Light = 0x7f100122
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.TextView = 0x7f10011f
com.seres.usb.upgrade:style/Widget.MaterialComponents.Badge = 0x7f1003ea
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f10011e
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.Slider = 0x7f10011b
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f10011a
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f100119
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f100117
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f100115
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.Chip = 0x7f100114
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f100112
com.seres.usb.upgrade:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f10010c
com.seres.usb.upgrade:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f10010a
com.seres.usb.upgrade:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f100108
com.seres.usb.upgrade:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f100105
com.seres.usb.upgrade:style/Base.Widget.Material3.CardView = 0x7f100101
com.seres.usb.upgrade:style/Base.Widget.Material3.BottomNavigationView = 0x7f100100
com.seres.usb.upgrade:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1000fe
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1002bb
com.seres.usb.upgrade:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f10010d
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1000fc
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Toolbar = 0x7f1000fb
com.seres.usb.upgrade:style/TextAppearance.Material3.SearchView.Prefix = 0x7f1001f4
com.seres.usb.upgrade:style/Base.Widget.AppCompat.TextView = 0x7f1000f9
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar = 0x7f1002ff
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1000f8
com.seres.usb.upgrade:style/Base.Widget.AppCompat.SeekBar = 0x7f1000f5
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1002cb
com.seres.usb.upgrade:style/Base.Widget.AppCompat.SearchView = 0x7f1000f3
com.seres.usb.upgrade:style/Widget.Material3.Button.TonalButton = 0x7f10035c
com.seres.usb.upgrade:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1000f2
com.seres.usb.upgrade:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1000f1
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1000e9
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ListView = 0x7f1000e8
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1000e7
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1000e4
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1000e3
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1000e2
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1000df
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f10039f
com.seres.usb.upgrade:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1000db
com.seres.usb.upgrade:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1000da
com.seres.usb.upgrade:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1000d8
com.seres.usb.upgrade:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1000d6
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1000d0
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Button = 0x7f1000ce
com.seres.usb.upgrade:style/Widget.AppCompat.ButtonBar = 0x7f1002f6
com.seres.usb.upgrade:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1000cd
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1000cc
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionMode = 0x7f1000cb
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1000ca
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1000c9
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1000c7
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1000c5
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1000c4
com.seres.usb.upgrade:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1000c2
com.seres.usb.upgrade:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1000c0
com.seres.usb.upgrade:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1000bc
com.seres.usb.upgrade:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1000b8
com.seres.usb.upgrade:style/Base.V26.Theme.AppCompat = 0x7f1000b6
com.seres.usb.upgrade:style/Base.V24.Theme.Material3.Dark = 0x7f1000b2
com.seres.usb.upgrade:style/Base.V23.Theme.AppCompat.Light = 0x7f1000b1
com.seres.usb.upgrade:style/Base.V23.Theme.AppCompat = 0x7f1000b0
com.seres.usb.upgrade:style/Base.V22.Theme.AppCompat = 0x7f1000ae
com.seres.usb.upgrade:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1000ac
com.seres.usb.upgrade:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1000a9
com.seres.usb.upgrade:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1000a8
com.seres.usb.upgrade:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1000a5
com.seres.usb.upgrade:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1000a3
com.seres.usb.upgrade:style/Base.V21.Theme.AppCompat = 0x7f1000a2
com.seres.usb.upgrade:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f10009d
com.seres.usb.upgrade:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f10009c
com.seres.usb.upgrade:style/TextAppearance.Material3.HeadlineSmall = 0x7f1001ed
com.seres.usb.upgrade:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f10009b
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f100099
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f100097
com.seres.usb.upgrade:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f10029f
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f100094
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f100093
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents = 0x7f100092
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f100091
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f100090
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f10008c
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1001d5
com.seres.usb.upgrade:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f100087
com.seres.usb.upgrade:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f100086
com.seres.usb.upgrade:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f100083
com.seres.usb.upgrade:style/Widget.Material3.Button.TextButton.Icon = 0x7f10035a
com.seres.usb.upgrade:style/Base.ThemeOverlay.Material3.Dialog = 0x7f100082
com.seres.usb.upgrade:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f100080
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat.Light = 0x7f10007f
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f10007a
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat = 0x7f100079
com.seres.usb.upgrade:style/Base.Theme.UsbUpgradeService = 0x7f100078
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f100077
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f100076
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f100074
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f100073
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f100071
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f100070
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Body2 = 0x7f100196
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light = 0x7f10006e
com.seres.usb.upgrade:styleable/ButtonBarLayout = 0x7f110018
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f10006d
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Dialog = 0x7f100068
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Bridge = 0x7f100066
com.seres.usb.upgrade:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f100064
com.seres.usb.upgrade:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f100063
com.seres.usb.upgrade:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f100062
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f10037d
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1000de
com.seres.usb.upgrade:style/Base.Theme.Material3.Light.Dialog = 0x7f100061
com.seres.usb.upgrade:style/TextAppearance.Design.Snackbar.Message = 0x7f1001d1
com.seres.usb.upgrade:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f100060
com.seres.usb.upgrade:style/Base.Theme.Material3.Light = 0x7f10005f
com.seres.usb.upgrade:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f10005e
com.seres.usb.upgrade:style/Base.Theme.Material3.Dark.Dialog = 0x7f10005b
com.seres.usb.upgrade:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f10005a
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f100058
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f100248
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f100056
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light.Dialog = 0x7f100054
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1003d7
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light = 0x7f100052
com.seres.usb.upgrade:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f100051
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f100050
com.seres.usb.upgrade:style/TextAppearance.Design.Hint = 0x7f1001ce
com.seres.usb.upgrade:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f10004a
com.seres.usb.upgrade:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f100047
com.seres.usb.upgrade:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f100044
com.seres.usb.upgrade:style/Base.TextAppearance.Material3.Search = 0x7f100043
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f100042
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f100041
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f100040
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f10003e
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f10003d
com.seres.usb.upgrade:styleable/SwitchMaterial = 0x7f110086
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f10003b
com.seres.usb.upgrade:style/Widget.MaterialComponents.Slider = 0x7f100439
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f100036
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f100035
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f100034
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f100033
com.seres.usb.upgrade:style/Widget.Material3.Light.ActionBar.Solid = 0x7f10038e
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f100032
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Title = 0x7f10002f
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.Medium = 0x7f100173
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f10002c
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Small = 0x7f10002b
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Medium = 0x7f100025
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f100024
com.seres.usb.upgrade:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f10027f
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Large = 0x7f100021
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Inverse = 0x7f100020
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Display4 = 0x7f10001e
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Display3 = 0x7f10001d
com.seres.usb.upgrade:styleable/KeyFramesAcceleration = 0x7f110042
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Display2 = 0x7f10001c
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Display1 = 0x7f10001b
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Caption = 0x7f10001a
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Button = 0x7f100019
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Body2 = 0x7f100018
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f100189
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f100067
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Body1 = 0x7f100017
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat = 0x7f100016
com.seres.usb.upgrade:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f100014
com.seres.usb.upgrade:style/Base.Animation.AppCompat.DropDownUp = 0x7f10000e
com.seres.usb.upgrade:style/Base.Animation.AppCompat.Dialog = 0x7f10000d
com.seres.usb.upgrade:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f10000a
com.seres.usb.upgrade:style/Animation.Material3.SideSheetDialog.Right = 0x7f100009
com.seres.usb.upgrade:style/Animation.Material3.SideSheetDialog = 0x7f100007
com.seres.usb.upgrade:style/Animation.Material3.BottomSheetDialog = 0x7f100006
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f100159
com.seres.usb.upgrade:style/Animation.Design.BottomSheetDialog = 0x7f100005
com.seres.usb.upgrade:style/Animation.AppCompat.Tooltip = 0x7f100004
com.seres.usb.upgrade:style/Animation.AppCompat.DropDownUp = 0x7f100003
com.seres.usb.upgrade:style/AlertDialog.AppCompat.Light = 0x7f100001
com.seres.usb.upgrade:style/AlertDialog.AppCompat = 0x7f100000
com.seres.usb.upgrade:string/status_bar_notification_info_overflow = 0x7f0f00a6
com.seres.usb.upgrade:string/side_sheet_behavior = 0x7f0f00a5
com.seres.usb.upgrade:string/side_sheet_accessibility_pane_title = 0x7f0f00a4
com.seres.usb.upgrade:string/searchbar_scrolling_view_behavior = 0x7f0f00a1
com.seres.usb.upgrade:string/search_menu_title = 0x7f0f00a0
com.seres.usb.upgrade:string/path_password_eye_mask_strike_through = 0x7f0f009d
com.seres.usb.upgrade:string/mtrl_switch_track_decoration_path = 0x7f0f0097
com.seres.usb.upgrade:string/mtrl_switch_thumb_path_name = 0x7f0f0094
com.seres.usb.upgrade:string/mtrl_picker_toggle_to_year_selection = 0x7f0f0090
com.seres.usb.upgrade:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f0f008d
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog = 0x7f100257
com.seres.usb.upgrade:string/mtrl_picker_text_input_month_abbr = 0x7f0f008a
com.seres.usb.upgrade:string/mtrl_picker_text_input_day_abbr = 0x7f0f0089
com.seres.usb.upgrade:string/mtrl_picker_text_input_date_range_start_hint = 0x7f0f0088
com.seres.usb.upgrade:string/mtrl_picker_text_input_date_range_end_hint = 0x7f0f0087
com.seres.usb.upgrade:string/mtrl_picker_text_input_date_hint = 0x7f0f0086
com.seres.usb.upgrade:string/mtrl_picker_save = 0x7f0f0084
com.seres.usb.upgrade:string/mtrl_picker_range_header_unselected = 0x7f0f0083
com.seres.usb.upgrade:string/mtrl_picker_range_header_title = 0x7f0f0082
com.seres.usb.upgrade:string/mtrl_picker_range_header_only_start_selected = 0x7f0f0080
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1001b9
com.seres.usb.upgrade:string/mtrl_picker_out_of_range = 0x7f0f007e
com.seres.usb.upgrade:string/mtrl_picker_invalid_range = 0x7f0f007b
com.seres.usb.upgrade:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1000bf
com.seres.usb.upgrade:string/mtrl_picker_invalid_format_use = 0x7f0f007a
com.seres.usb.upgrade:string/mtrl_picker_invalid_format_example = 0x7f0f0079
com.seres.usb.upgrade:string/mtrl_picker_end_date_description = 0x7f0f0077
com.seres.usb.upgrade:string/mtrl_picker_date_header_unselected = 0x7f0f0075
com.seres.usb.upgrade:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f100111
com.seres.usb.upgrade:string/mtrl_picker_date_header_selected = 0x7f0f0073
com.seres.usb.upgrade:styleable/KeyAttribute = 0x7f11003f
com.seres.usb.upgrade:style/Widget.AppCompat.ProgressBar = 0x7f10031d
com.seres.usb.upgrade:string/mtrl_picker_announce_current_selection_none = 0x7f0f0070
com.seres.usb.upgrade:string/mtrl_picker_announce_current_range_selection = 0x7f0f006e
com.seres.usb.upgrade:string/mtrl_picker_a11y_prev_month = 0x7f0f006d
com.seres.usb.upgrade:string/mtrl_picker_a11y_next_month = 0x7f0f006c
com.seres.usb.upgrade:string/mtrl_exceed_max_badge_number_suffix = 0x7f0f006b
com.seres.usb.upgrade:string/mtrl_exceed_max_badge_number_content_description = 0x7f0f006a
com.seres.usb.upgrade:string/mtrl_chip_close_icon_content_description = 0x7f0f0069
com.seres.usb.upgrade:string/mtrl_checkbox_state_description_unchecked = 0x7f0f0068
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Body2 = 0x7f1001fa
com.seres.usb.upgrade:string/mtrl_checkbox_state_description_checked = 0x7f0f0066
com.seres.usb.upgrade:string/mtrl_checkbox_button_path_name = 0x7f0f0064
com.seres.usb.upgrade:string/mtrl_checkbox_button_icon_path_name = 0x7f0f0061
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Overline = 0x7f100204
com.seres.usb.upgrade:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f0f0060
com.seres.usb.upgrade:string/material_timepicker_text_input_mode_description = 0x7f0f005c
com.seres.usb.upgrade:string/material_timepicker_select_time = 0x7f0f005b
com.seres.usb.upgrade:string/material_timepicker_hour = 0x7f0f0058
com.seres.usb.upgrade:string/material_timepicker_clock_mode_description = 0x7f0f0057
com.seres.usb.upgrade:string/material_timepicker_am = 0x7f0f0056
com.seres.usb.upgrade:string/material_slider_value = 0x7f0f0055
com.seres.usb.upgrade:string/material_slider_range_end = 0x7f0f0053
com.seres.usb.upgrade:string/material_motion_easing_linear = 0x7f0f0051
com.seres.usb.upgrade:string/material_motion_easing_decelerated = 0x7f0f004f
com.seres.usb.upgrade:string/material_motion_easing_accelerated = 0x7f0f004e
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat = 0x7f100273
com.seres.usb.upgrade:style/Platform.V21.AppCompat = 0x7f100140
com.seres.usb.upgrade:string/material_minute_selection = 0x7f0f004c
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f100146
com.seres.usb.upgrade:string/material_hour_suffix = 0x7f0f004b
com.seres.usb.upgrade:string/material_hour_selection = 0x7f0f004a
com.seres.usb.upgrade:string/material_hour_24h_suffix = 0x7f0f0049
com.seres.usb.upgrade:styleable/Fragment = 0x7f110039
com.seres.usb.upgrade:string/material_clock_toggle_content_description = 0x7f0f0048
com.seres.usb.upgrade:string/material_clock_display_divider = 0x7f0f0047
com.seres.usb.upgrade:string/m3_sys_motion_easing_standard_accelerate = 0x7f0f0045
com.seres.usb.upgrade:string/m3_sys_motion_easing_standard = 0x7f0f0044
com.seres.usb.upgrade:string/m3_sys_motion_easing_legacy_accelerate = 0x7f0f0041
com.seres.usb.upgrade:string/m3_sys_motion_easing_legacy = 0x7f0f0040
com.seres.usb.upgrade:style/Widget.Material3.NavigationRailView.Badge = 0x7f1003b5
com.seres.usb.upgrade:string/m3_sys_motion_easing_emphasized_path_data = 0x7f0f003f
com.seres.usb.upgrade:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f0f003e
com.seres.usb.upgrade:string/m3_ref_typeface_plain_medium = 0x7f0f003a
com.seres.usb.upgrade:string/hide_bottom_view_on_scroll_behavior = 0x7f0f0034
com.seres.usb.upgrade:string/fab_transformation_scrim_behavior = 0x7f0f0032
com.seres.usb.upgrade:string/mtrl_picker_start_date_description = 0x7f0f0085
com.seres.usb.upgrade:string/exposed_dropdown_menu_content_description = 0x7f0f0031
com.seres.usb.upgrade:string/error_icon_content_description = 0x7f0f0030
com.seres.usb.upgrade:string/error_a11y_label = 0x7f0f002f
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Headline = 0x7f10019d
com.seres.usb.upgrade:string/character_counter_overflowed_content_description = 0x7f0f002c
com.seres.usb.upgrade:string/call_notification_incoming_text = 0x7f0f0028
com.seres.usb.upgrade:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1000f6
com.seres.usb.upgrade:string/call_notification_hang_up_action = 0x7f0f0027
com.seres.usb.upgrade:string/call_notification_answer_action = 0x7f0f0024
com.seres.usb.upgrade:string/bottomsheet_drag_handle_clicked = 0x7f0f0022
com.seres.usb.upgrade:string/bottomsheet_action_expand = 0x7f0f0020
com.seres.usb.upgrade:string/appbar_scrolling_view_behavior = 0x7f0f001d
com.seres.usb.upgrade:string/app_name = 0x7f0f001c
com.seres.usb.upgrade:string/androidx_startup = 0x7f0f001b
com.seres.usb.upgrade:string/abc_shareactionprovider_share_with_application = 0x7f0f0019
com.seres.usb.upgrade:string/abc_shareactionprovider_share_with = 0x7f0f0018
com.seres.usb.upgrade:string/abc_searchview_description_submit = 0x7f0f0016
com.seres.usb.upgrade:string/abc_searchview_description_query = 0x7f0f0014
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.Button = 0x7f100181
com.seres.usb.upgrade:string/abc_searchview_description_clear = 0x7f0f0013
com.seres.usb.upgrade:string/abc_search_hint = 0x7f0f0012
com.seres.usb.upgrade:string/abc_prepend_shortcut_label = 0x7f0f0011
com.seres.usb.upgrade:style/Widget.Material3.Button.IconButton = 0x7f100350
com.seres.usb.upgrade:string/abc_menu_sym_shortcut_label = 0x7f0f0010
com.seres.usb.upgrade:string/abc_menu_meta_shortcut_label = 0x7f0f000d
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Badge = 0x7f1001f8
com.seres.usb.upgrade:string/abc_menu_enter_shortcut_label = 0x7f0f000b
com.seres.usb.upgrade:string/abc_menu_ctrl_shortcut_label = 0x7f0f0009
com.seres.usb.upgrade:string/abc_menu_alt_shortcut_label = 0x7f0f0008
com.seres.usb.upgrade:string/abc_capital_on = 0x7f0f0007
com.seres.usb.upgrade:string/abc_activity_chooser_view_see_all = 0x7f0f0004
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f100263
com.seres.usb.upgrade:string/abc_action_mode_done = 0x7f0f0003
com.seres.usb.upgrade:string/abc_action_menu_overflow_description = 0x7f0f0002
com.seres.usb.upgrade:string/abc_action_bar_home_description = 0x7f0f0000
com.seres.usb.upgrade:plurals/mtrl_badge_content_description = 0x7f0e0000
com.seres.usb.upgrade:mipmap/logo = 0x7f0d0002
com.seres.usb.upgrade:mipmap/ic_launcher = 0x7f0d0000
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c0170
com.seres.usb.upgrade:string/abc_searchview_description_voice = 0x7f0f0017
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016d
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0169
com.seres.usb.upgrade:style/Base.V26.Theme.AppCompat.Light = 0x7f1000b7
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0167
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1002c3
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0163
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0162
com.seres.usb.upgrade:style/Widget.Material3.Button = 0x7f10034c
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015d
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015c
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c015a
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1002e2
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0158
com.seres.usb.upgrade:styleable/RecyclerView = 0x7f110075
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0156
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0155
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f10007b
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0154
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ListMenuView = 0x7f1000e6
com.seres.usb.upgrade:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014e
com.seres.usb.upgrade:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014d
com.seres.usb.upgrade:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0148
com.seres.usb.upgrade:string/material_motion_easing_standard = 0x7f0f0052
com.seres.usb.upgrade:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0147
com.seres.usb.upgrade:macro/m3_comp_text_button_label_text_type = 0x7f0c0146
com.seres.usb.upgrade:macro/m3_comp_text_button_label_text_color = 0x7f0c0145
com.seres.usb.upgrade:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0143
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013f
com.seres.usb.upgrade:styleable/State = 0x7f110081
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013e
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013d
com.seres.usb.upgrade:style/Widget.Material3.Slider = 0x7f1003c6
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013c
com.seres.usb.upgrade:style/Base.Widget.Design.TabLayout = 0x7f1000fd
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0139
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0136
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0135
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0134
com.seres.usb.upgrade:macro/m3_comp_switch_selected_track_color = 0x7f0c012f
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f100451
com.seres.usb.upgrade:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012d
com.seres.usb.upgrade:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0129
com.seres.usb.upgrade:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0126
com.seres.usb.upgrade:macro/m3_comp_switch_selected_handle_color = 0x7f0c0125
com.seres.usb.upgrade:style/Widget.Material3.Button.TextButton = 0x7f100356
com.seres.usb.upgrade:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0124
com.seres.usb.upgrade:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0122
com.seres.usb.upgrade:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1002f3
com.seres.usb.upgrade:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0121
com.seres.usb.upgrade:styleable/ViewBackgroundHelper = 0x7f110094
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1001a6
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011e
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011d
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011c
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c011a
com.seres.usb.upgrade:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0119
com.seres.usb.upgrade:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0117
com.seres.usb.upgrade:macro/m3_comp_slider_label_label_text_color = 0x7f0c0113
com.seres.usb.upgrade:macro/m3_comp_slider_label_container_color = 0x7f0c0112
com.seres.usb.upgrade:macro/m3_comp_slider_inactive_track_color = 0x7f0c0111
com.seres.usb.upgrade:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010e
com.seres.usb.upgrade:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010d
com.seres.usb.upgrade:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0109
com.seres.usb.upgrade:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0108
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f100422
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1003b0
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0105
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0104
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0102
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1001e0
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fe
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f100420
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fd
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fc
com.seres.usb.upgrade:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f8
com.seres.usb.upgrade:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f7
com.seres.usb.upgrade:macro/m3_comp_search_view_divider_color = 0x7f0c00f4
com.seres.usb.upgrade:macro/m3_comp_search_view_container_surface_tint_layer_color = 0x7f0c00f3
com.seres.usb.upgrade:macro/m3_comp_search_view_container_color = 0x7f0c00f2
com.seres.usb.upgrade:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ef
com.seres.usb.upgrade:style/ThemeOverlay.Material3 = 0x7f10027d
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1000d1
com.seres.usb.upgrade:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ed
com.seres.usb.upgrade:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00ec
com.seres.usb.upgrade:macro/m3_comp_search_bar_input_text_type = 0x7f0c00eb
com.seres.usb.upgrade:macro/m3_comp_search_bar_container_surface_tint_layer_color = 0x7f0c00e7
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
com.seres.usb.upgrade:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f100180
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
com.seres.usb.upgrade:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f100023
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d5
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d3
com.seres.usb.upgrade:style/Widget.MaterialComponents.Chip.Filter = 0x7f100404
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1001b2
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d2
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1003ac
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cf
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00ce
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00cc
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c9
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c7
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c6
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c2
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00c1
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00c0
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bf
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bd
com.seres.usb.upgrade:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f10022c
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00bc
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00bb
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00ba
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b9
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b8
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b7
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b5
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f100250
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b3
com.seres.usb.upgrade:string/path_password_eye_mask_visible = 0x7f0f009e
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b2
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f100297
com.seres.usb.upgrade:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00b1
com.seres.usb.upgrade:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00af
com.seres.usb.upgrade:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00ac
com.seres.usb.upgrade:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a7
com.seres.usb.upgrade:string/abc_capital_off = 0x7f0f0006
com.seres.usb.upgrade:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a5
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.Large = 0x7f100172
com.seres.usb.upgrade:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a3
com.seres.usb.upgrade:macro/m3_comp_outlined_autocomplete_menu_list_item_selected_container_color = 0x7f0c00a2
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f100423
com.seres.usb.upgrade:style/Widget.Material3.Button.UnelevatedButton = 0x7f10035e
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c00a1
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c00a0
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009d
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009c
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_container_color = 0x7f0c009b
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c009a
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0099
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0096
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0095
com.seres.usb.upgrade:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1003bc
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c0092
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008d
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c008c
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c008b
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0089
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0088
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0084
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0082
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c0081
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c0080
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007e
com.seres.usb.upgrade:styleable/ActivityChooserView = 0x7f110005
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0093
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c007a
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0079
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0078
com.seres.usb.upgrade:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1002eb
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0077
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1000c6
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0076
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0074
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0073
com.seres.usb.upgrade:styleable/ConstraintLayout_ReactiveGuide = 0x7f110028
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0072
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c0071
com.seres.usb.upgrade:styleable/AnimatedStateListDrawableTransition = 0x7f110009
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c0070
com.seres.usb.upgrade:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f10017f
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006f
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006e
com.seres.usb.upgrade:styleable/ListPopupWindow = 0x7f11004b
com.seres.usb.upgrade:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f10043b
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006c
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0069
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0068
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0067
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1001b3
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0066
com.seres.usb.upgrade:string/mtrl_switch_track_path = 0x7f0f0098
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0064
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0063
com.seres.usb.upgrade:style/Widget.MaterialComponents.NavigationView = 0x7f100432
com.seres.usb.upgrade:string/material_minute_suffix = 0x7f0f004d
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c0061
com.seres.usb.upgrade:macro/m3_comp_menu_container_color = 0x7f0c0060
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f10008f
com.seres.usb.upgrade:macro/m3_comp_linear_progress_indicator_track_color = 0x7f0c005f
com.seres.usb.upgrade:macro/m3_comp_linear_progress_indicator_active_indicator_color = 0x7f0c005e
com.seres.usb.upgrade:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f100283
com.seres.usb.upgrade:string/mtrl_badge_numberless_content_description = 0x7f0f005d
com.seres.usb.upgrade:macro/m3_comp_input_chip_container_shape = 0x7f0c005c
com.seres.usb.upgrade:styleable/CircularProgressIndicator = 0x7f11001f
com.seres.usb.upgrade:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c005a
com.seres.usb.upgrade:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0059
com.seres.usb.upgrade:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0057
com.seres.usb.upgrade:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0056
com.seres.usb.upgrade:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0053
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0052
com.seres.usb.upgrade:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f100088
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c0050
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f10044a
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004e
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_container_color = 0x7f0c004c
com.seres.usb.upgrade:macro/m3_comp_filled_card_container_shape = 0x7f0c0048
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1002d3
com.seres.usb.upgrade:macro/m3_comp_filled_button_label_text_color = 0x7f0c0045
com.seres.usb.upgrade:macro/m3_comp_filled_button_container_color = 0x7f0c0044
com.seres.usb.upgrade:macro/m3_comp_filled_autocomplete_menu_list_item_selected_container_color = 0x7f0c0042
com.seres.usb.upgrade:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0041
com.seres.usb.upgrade:macro/m3_comp_fab_surface_icon_color = 0x7f0c003f
com.seres.usb.upgrade:macro/m3_comp_fab_secondary_container_color = 0x7f0c003c
com.seres.usb.upgrade:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003b
com.seres.usb.upgrade:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f100104
com.seres.usb.upgrade:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c003a
com.seres.usb.upgrade:macro/m3_comp_fab_primary_icon_color = 0x7f0c0039
com.seres.usb.upgrade:macro/m3_comp_fab_primary_container_shape = 0x7f0c0038
com.seres.usb.upgrade:macro/m3_comp_fab_primary_container_color = 0x7f0c0037
com.seres.usb.upgrade:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0034
com.seres.usb.upgrade:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0033
com.seres.usb.upgrade:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0032
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1003b2
com.seres.usb.upgrade:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c0030
com.seres.usb.upgrade:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f100371
com.seres.usb.upgrade:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002f
com.seres.usb.upgrade:style/Widget.Material3.CardView.Outlined = 0x7f100361
com.seres.usb.upgrade:string/mtrl_switch_thumb_path_checked = 0x7f0f0092
com.seres.usb.upgrade:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002e
com.seres.usb.upgrade:string/mtrl_picker_invalid_format = 0x7f0f0078
com.seres.usb.upgrade:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002d
com.seres.usb.upgrade:style/Widget.Compat.NotificationActionContainer = 0x7f10032e
com.seres.usb.upgrade:macro/m3_comp_elevated_card_container_color = 0x7f0c002b
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f10044f
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f10015b
com.seres.usb.upgrade:macro/m3_comp_elevated_button_container_color = 0x7f0c002a
com.seres.usb.upgrade:macro/m3_comp_divider_color = 0x7f0c0029
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b6
com.seres.usb.upgrade:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0028
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f10039e
com.seres.usb.upgrade:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0027
com.seres.usb.upgrade:macro/m3_comp_dialog_headline_type = 0x7f0c0026
com.seres.usb.upgrade:macro/m3_comp_dialog_headline_color = 0x7f0c0025
com.seres.usb.upgrade:macro/m3_comp_dialog_container_shape = 0x7f0c0024
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0171
com.seres.usb.upgrade:macro/m3_comp_dialog_container_color = 0x7f0c0023
com.seres.usb.upgrade:style/TextAppearance.Material3.ActionBar.Title = 0x7f1001e4
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0022
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c0020
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001f
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001e
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001d
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1001e2
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001c
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001b
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c001a
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0018
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0016
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0013
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0011
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000f
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button = 0x7f1003f3
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000e
com.seres.usb.upgrade:string/character_counter_content_description = 0x7f0f002b
com.seres.usb.upgrade:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
com.seres.usb.upgrade:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
com.seres.usb.upgrade:layout/support_simple_spinner_dropdown_item = 0x7f0b006a
com.seres.usb.upgrade:layout/select_dialog_singlechoice_material = 0x7f0b0069
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f100381
com.seres.usb.upgrade:layout/select_dialog_multichoice_material = 0x7f0b0068
com.seres.usb.upgrade:layout/notification_template_part_time = 0x7f0b0066
com.seres.usb.upgrade:layout/notification_template_part_chronometer = 0x7f0b0065
com.seres.usb.upgrade:layout/notification_template_icon_group = 0x7f0b0064
com.seres.usb.upgrade:layout/mtrl_search_view = 0x7f0b0060
com.seres.usb.upgrade:layout/mtrl_picker_text_input_date_range = 0x7f0b005e
com.seres.usb.upgrade:layout/mtrl_picker_text_input_date = 0x7f0b005d
com.seres.usb.upgrade:layout/mtrl_picker_header_toggle = 0x7f0b005c
com.seres.usb.upgrade:style/Widget.AppCompat.SearchView.ActionBar = 0x7f100323
com.seres.usb.upgrade:layout/mtrl_picker_header_title_text = 0x7f0b005b
com.seres.usb.upgrade:layout/mtrl_picker_dialog = 0x7f0b0056
com.seres.usb.upgrade:layout/mtrl_picker_actions = 0x7f0b0055
com.seres.usb.upgrade:layout/mtrl_navigation_rail_item = 0x7f0b0054
com.seres.usb.upgrade:layout/mtrl_layout_snackbar_include = 0x7f0b0053
com.seres.usb.upgrade:layout/mtrl_layout_snackbar = 0x7f0b0052
com.seres.usb.upgrade:layout/mtrl_calendar_year = 0x7f0b0051
com.seres.usb.upgrade:layout/mtrl_calendar_vertical = 0x7f0b0050
com.seres.usb.upgrade:layout/mtrl_calendar_month_navigation = 0x7f0b004e
com.seres.usb.upgrade:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1002a1
com.seres.usb.upgrade:style/Theme.Material3.DynamicColors.Light = 0x7f10023a
com.seres.usb.upgrade:layout/mtrl_calendar_month_labeled = 0x7f0b004d
com.seres.usb.upgrade:layout/mtrl_calendar_month = 0x7f0b004c
com.seres.usb.upgrade:layout/mtrl_calendar_day = 0x7f0b0048
com.seres.usb.upgrade:layout/mtrl_auto_complete_simple_item = 0x7f0b0047
com.seres.usb.upgrade:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b0045
com.seres.usb.upgrade:layout/mtrl_alert_dialog_title = 0x7f0b0043
com.seres.usb.upgrade:layout/mtrl_alert_dialog = 0x7f0b0041
com.seres.usb.upgrade:layout/material_time_input = 0x7f0b003d
com.seres.usb.upgrade:style/ShapeAppearance.MaterialComponents.Badge = 0x7f10017c
com.seres.usb.upgrade:layout/material_time_chip = 0x7f0b003c
com.seres.usb.upgrade:layout/material_textinput_timepicker = 0x7f0b003b
com.seres.usb.upgrade:layout/material_clockface_textview = 0x7f0b0038
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Dialog = 0x7f100296
com.seres.usb.upgrade:layout/material_clock_display_divider = 0x7f0b0035
com.seres.usb.upgrade:style/Widget.Material3.CardView.Elevated = 0x7f10035f
com.seres.usb.upgrade:layout/material_clock_display = 0x7f0b0034
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c013a
com.seres.usb.upgrade:layout/material_chip_input_combo = 0x7f0b0033
com.seres.usb.upgrade:layout/m3_side_sheet_dialog = 0x7f0b0032
com.seres.usb.upgrade:layout/m3_auto_complete_simple_item = 0x7f0b0031
com.seres.usb.upgrade:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f100341
com.seres.usb.upgrade:layout/m3_alert_dialog_title = 0x7f0b0030
com.seres.usb.upgrade:layout/m3_alert_dialog_actions = 0x7f0b002f
com.seres.usb.upgrade:layout/material_timepicker_dialog = 0x7f0b003f
com.seres.usb.upgrade:layout/design_text_input_start_icon = 0x7f0b002c
com.seres.usb.upgrade:layout/design_text_input_end_icon = 0x7f0b002b
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0137
com.seres.usb.upgrade:layout/design_navigation_menu_item = 0x7f0b002a
com.seres.usb.upgrade:layout/design_navigation_item_subheader = 0x7f0b0028
com.seres.usb.upgrade:layout/design_navigation_item_separator = 0x7f0b0027
com.seres.usb.upgrade:layout/design_navigation_item = 0x7f0b0025
com.seres.usb.upgrade:layout/design_layout_snackbar_include = 0x7f0b0021
com.seres.usb.upgrade:layout/design_bottom_sheet_dialog = 0x7f0b001f
com.seres.usb.upgrade:layout/custom_dialog = 0x7f0b001d
com.seres.usb.upgrade:layout/activity_main = 0x7f0b001c
com.seres.usb.upgrade:layout/abc_search_view = 0x7f0b0019
com.seres.usb.upgrade:layout/abc_screen_toolbar = 0x7f0b0017
com.seres.usb.upgrade:layout/abc_screen_content_include = 0x7f0b0014
com.seres.usb.upgrade:layout/abc_list_menu_item_icon = 0x7f0b000f
com.seres.usb.upgrade:layout/abc_expanded_menu_layout = 0x7f0b000d
com.seres.usb.upgrade:layout/abc_dialog_title_material = 0x7f0b000c
com.seres.usb.upgrade:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.seres.usb.upgrade:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.seres.usb.upgrade:layout/abc_activity_chooser_view = 0x7f0b0006
com.seres.usb.upgrade:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.seres.usb.upgrade:layout/abc_action_menu_layout = 0x7f0b0003
com.seres.usb.upgrade:layout/abc_action_menu_item_layout = 0x7f0b0002
com.seres.usb.upgrade:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f1002f2
com.seres.usb.upgrade:layout/abc_action_bar_title_item = 0x7f0b0000
com.seres.usb.upgrade:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
com.seres.usb.upgrade:interpolator/mtrl_linear = 0x7f0a0010
com.seres.usb.upgrade:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
com.seres.usb.upgrade:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f1002f9
com.seres.usb.upgrade:interpolator/fast_out_slow_in = 0x7f0a0006
com.seres.usb.upgrade:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.seres.usb.upgrade:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.seres.usb.upgrade:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.seres.usb.upgrade:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.seres.usb.upgrade:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.seres.usb.upgrade:integer/status_bar_notification_info_maxnum = 0x7f090043
com.seres.usb.upgrade:integer/show_password_duration = 0x7f090042
com.seres.usb.upgrade:integer/mtrl_view_visible = 0x7f090041
com.seres.usb.upgrade:style/Base.V28.Theme.AppCompat = 0x7f1000b9
com.seres.usb.upgrade:integer/mtrl_switch_track_viewport_width = 0x7f09003d
com.seres.usb.upgrade:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f10040c
com.seres.usb.upgrade:integer/mtrl_switch_track_viewport_height = 0x7f09003c
com.seres.usb.upgrade:integer/mtrl_switch_thumb_viewport_size = 0x7f09003b
com.seres.usb.upgrade:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003a
com.seres.usb.upgrade:integer/mtrl_switch_thumb_pressed_duration = 0x7f090039
com.seres.usb.upgrade:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f090038
com.seres.usb.upgrade:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090037
com.seres.usb.upgrade:style/Widget.AppCompat.Light.PopupMenu = 0x7f100311
com.seres.usb.upgrade:integer/mtrl_switch_thumb_motion_duration = 0x7f090036
com.seres.usb.upgrade:integer/mtrl_calendar_selection_text_lines = 0x7f090031
com.seres.usb.upgrade:integer/mtrl_badge_max_character_count = 0x7f09002d
com.seres.usb.upgrade:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
com.seres.usb.upgrade:integer/material_motion_duration_short_1 = 0x7f09002a
com.seres.usb.upgrade:integer/material_motion_duration_medium_2 = 0x7f090029
com.seres.usb.upgrade:integer/mtrl_btn_anim_duration_ms = 0x7f09002f
com.seres.usb.upgrade:integer/material_motion_duration_medium_1 = 0x7f090028
com.seres.usb.upgrade:integer/material_motion_duration_long_2 = 0x7f090027
com.seres.usb.upgrade:styleable/Toolbar = 0x7f11008e
com.seres.usb.upgrade:integer/material_motion_duration_long_1 = 0x7f090026
com.seres.usb.upgrade:integer/m3_sys_shape_corner_small_corner_family = 0x7f090025
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Headline6 = 0x7f100203
com.seres.usb.upgrade:integer/m3_sys_shape_corner_full_corner_family = 0x7f090022
com.seres.usb.upgrade:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090021
com.seres.usb.upgrade:integer/m3_sys_motion_duration_short3 = 0x7f09001d
com.seres.usb.upgrade:integer/m3_sys_motion_duration_short2 = 0x7f09001c
com.seres.usb.upgrade:integer/m3_sys_motion_duration_medium4 = 0x7f09001a
com.seres.usb.upgrade:integer/m3_sys_motion_duration_medium3 = 0x7f090019
com.seres.usb.upgrade:integer/m3_sys_motion_duration_long1 = 0x7f090013
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1002d0
com.seres.usb.upgrade:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f10005d
com.seres.usb.upgrade:integer/m3_sys_motion_duration_extra_long2 = 0x7f090010
com.seres.usb.upgrade:integer/m3_sys_motion_duration_extra_long1 = 0x7f09000f
com.seres.usb.upgrade:integer/m3_card_anim_duration_ms = 0x7f09000d
com.seres.usb.upgrade:style/Widget.AppCompat.Spinner = 0x7f100326
com.seres.usb.upgrade:integer/m3_btn_anim_duration_ms = 0x7f09000b
com.seres.usb.upgrade:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionBar = 0x7f1000c3
com.seres.usb.upgrade:integer/design_snackbar_text_max_lines = 0x7f090006
com.seres.usb.upgrade:style/Widget.MaterialComponents.CheckedTextView = 0x7f100400
com.seres.usb.upgrade:integer/cancel_button_image_alpha = 0x7f090004
com.seres.usb.upgrade:integer/abc_config_activityShortDur = 0x7f090001
com.seres.usb.upgrade:integer/abc_config_activityDefaultDur = 0x7f090000
com.seres.usb.upgrade:id/x_left = 0x7f080200
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f10039a
com.seres.usb.upgrade:id/wrap_content_constrained = 0x7f0801ff
com.seres.usb.upgrade:style/Widget.AppCompat.TextView = 0x7f10032a
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1002db
com.seres.usb.upgrade:id/wrap_content = 0x7f0801fe
com.seres.usb.upgrade:id/view_tree_saved_state_registry_owner = 0x7f0801f5
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f100192
com.seres.usb.upgrade:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0801f4
com.seres.usb.upgrade:id/view_transition = 0x7f0801f2
com.seres.usb.upgrade:id/uniform = 0x7f0801ec
com.seres.usb.upgrade:integer/hide_password_duration = 0x7f090008
com.seres.usb.upgrade:id/unchecked = 0x7f0801eb
com.seres.usb.upgrade:id/tvUsbStatus = 0x7f0801ea
com.seres.usb.upgrade:layout/abc_select_dialog_material = 0x7f0b001a
com.seres.usb.upgrade:id/tvUsagePercentage = 0x7f0801e9
com.seres.usb.upgrade:id/tvTaskCount = 0x7f0801e8
com.seres.usb.upgrade:id/tvStorageStatus = 0x7f0801e7
com.seres.usb.upgrade:id/tvStorageInfo = 0x7f0801e6
com.seres.usb.upgrade:id/tvDeviceStatus = 0x7f0801e4
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f100132
com.seres.usb.upgrade:id/tvDevicePath = 0x7f0801e3
com.seres.usb.upgrade:id/tvDeviceName = 0x7f0801e2
com.seres.usb.upgrade:id/transition_scene_layoutid_cache = 0x7f0801df
com.seres.usb.upgrade:id/transition_layout_save = 0x7f0801dd
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f10024d
com.seres.usb.upgrade:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f10013f
com.seres.usb.upgrade:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090020
com.seres.usb.upgrade:id/transitionToEnd = 0x7f0801da
com.seres.usb.upgrade:id/topPanel = 0x7f0801d8
com.seres.usb.upgrade:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
com.seres.usb.upgrade:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.seres.usb.upgrade:id/top = 0x7f0801d7
com.seres.usb.upgrade:id/time = 0x7f0801d2
com.seres.usb.upgrade:id/textinput_suffix_text = 0x7f0801d1
com.seres.usb.upgrade:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f100436
com.seres.usb.upgrade:id/textinput_placeholder = 0x7f0801cf
com.seres.usb.upgrade:id/textinput_helper_text = 0x7f0801ce
com.seres.usb.upgrade:id/textinput_counter = 0x7f0801cc
com.seres.usb.upgrade:id/text_input_end_icon = 0x7f0801c9
com.seres.usb.upgrade:macro/m3_comp_slider_active_track_color = 0x7f0c010c
com.seres.usb.upgrade:id/textTop = 0x7f0801c8
com.seres.usb.upgrade:id/textStart = 0x7f0801c7
com.seres.usb.upgrade:id/textEnd = 0x7f0801c4
com.seres.usb.upgrade:id/text = 0x7f0801c2
com.seres.usb.upgrade:string/m3_ref_typeface_brand_regular = 0x7f0f0039
com.seres.usb.upgrade:id/tag_unhandled_key_listeners = 0x7f0801c0
com.seres.usb.upgrade:id/tag_unhandled_key_event_manager = 0x7f0801bf
com.seres.usb.upgrade:id/tag_screen_reader_focusable = 0x7f0801bc
com.seres.usb.upgrade:id/tag_on_receive_content_mime_types = 0x7f0801bb
com.seres.usb.upgrade:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f09003e
com.seres.usb.upgrade:id/tag_on_apply_window_listener = 0x7f0801b9
com.seres.usb.upgrade:string/bottom_sheet_behavior = 0x7f0f001e
com.seres.usb.upgrade:id/tag_accessibility_pane_title = 0x7f0801b8
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1001a2
com.seres.usb.upgrade:id/supportScrollUp = 0x7f0801b3
com.seres.usb.upgrade:id/stretch = 0x7f0801b0
com.seres.usb.upgrade:id/staticLayout = 0x7f0801ad
com.seres.usb.upgrade:id/startVertical = 0x7f0801ac
com.seres.usb.upgrade:id/standard = 0x7f0801a8
com.seres.usb.upgrade:id/src_over = 0x7f0801a7
com.seres.usb.upgrade:id/src_in = 0x7f0801a6
com.seres.usb.upgrade:id/src_atop = 0x7f0801a5
com.seres.usb.upgrade:id/square = 0x7f0801a4
com.seres.usb.upgrade:id/spread_inside = 0x7f0801a2
com.seres.usb.upgrade:id/spread = 0x7f0801a1
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
com.seres.usb.upgrade:id/spline = 0x7f08019f
com.seres.usb.upgrade:id/special_effects_controller_view_tag = 0x7f08019e
com.seres.usb.upgrade:id/spacer = 0x7f08019d
com.seres.usb.upgrade:id/south = 0x7f08019c
com.seres.usb.upgrade:id/snapMargins = 0x7f08019b
com.seres.usb.upgrade:id/snap = 0x7f08019a
com.seres.usb.upgrade:id/snackbar_action = 0x7f080198
com.seres.usb.upgrade:string/mtrl_timepicker_cancel = 0x7f0f0099
com.seres.usb.upgrade:id/slide = 0x7f080197
com.seres.usb.upgrade:id/skipped = 0x7f080196
com.seres.usb.upgrade:id/skipCollapsed = 0x7f080195
com.seres.usb.upgrade:macro/m3_comp_fab_tertiary_container_color = 0x7f0c0040
com.seres.usb.upgrade:id/showHome = 0x7f080192
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0161
com.seres.usb.upgrade:id/shortcut = 0x7f080190
com.seres.usb.upgrade:id/sharedValueSet = 0x7f08018e
com.seres.usb.upgrade:id/selection_type = 0x7f08018d
com.seres.usb.upgrade:id/select_dialog_listview = 0x7f08018b
com.seres.usb.upgrade:id/search_voice_btn = 0x7f08018a
com.seres.usb.upgrade:id/search_src_text = 0x7f080189
com.seres.usb.upgrade:id/search_plate = 0x7f080188
com.seres.usb.upgrade:id/search_button = 0x7f080183
com.seres.usb.upgrade:style/Base.CardView = 0x7f100010
com.seres.usb.upgrade:id/search_bar = 0x7f080182
com.seres.usb.upgrade:id/search_badge = 0x7f080181
com.seres.usb.upgrade:id/scrollView = 0x7f08017f
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Headline = 0x7f10001f
com.seres.usb.upgrade:id/scrollIndicatorUp = 0x7f08017e
com.seres.usb.upgrade:id/scrollIndicatorDown = 0x7f08017d
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1002c1
com.seres.usb.upgrade:macro/m3_comp_badge_color = 0x7f0c0002
com.seres.usb.upgrade:id/screen = 0x7f08017b
com.seres.usb.upgrade:id/sawtooth = 0x7f080179
com.seres.usb.upgrade:string/mtrl_picker_text_input_year_abbr = 0x7f0f008b
com.seres.usb.upgrade:id/save_non_transition_alpha = 0x7f080177
com.seres.usb.upgrade:id/rounded = 0x7f080175
com.seres.usb.upgrade:style/Widget.Material3.Badge.AdjustToBounds = 0x7f100343
com.seres.usb.upgrade:id/right_side = 0x7f080174
com.seres.usb.upgrade:id/report_drawn = 0x7f08016f
com.seres.usb.upgrade:id/recyclerViewStorageDevices = 0x7f08016e
com.seres.usb.upgrade:id/rectangles = 0x7f08016d
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Dark = 0x7f100292
com.seres.usb.upgrade:id/ratio = 0x7f08016c
com.seres.usb.upgrade:id/radio = 0x7f08016b
com.seres.usb.upgrade:style/Theme.MaterialComponents = 0x7f100243
com.seres.usb.upgrade:id/progress_circular = 0x7f080169
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f100152
com.seres.usb.upgrade:id/progressBarUsage = 0x7f080168
com.seres.usb.upgrade:id/pressed = 0x7f080167
com.seres.usb.upgrade:id/toggle = 0x7f0801d6
com.seres.usb.upgrade:id/postLayout = 0x7f080166
com.seres.usb.upgrade:id/position = 0x7f080165
com.seres.usb.upgrade:id/peekHeight = 0x7f080161
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c3
com.seres.usb.upgrade:id/password_toggle = 0x7f08015e
com.seres.usb.upgrade:id/parent_matrix = 0x7f08015d
com.seres.usb.upgrade:id/parentRelative = 0x7f08015c
com.seres.usb.upgrade:id/parent = 0x7f08015a
com.seres.usb.upgrade:id/packed = 0x7f080158
com.seres.usb.upgrade:id/overshoot = 0x7f080157
com.seres.usb.upgrade:id/outline = 0x7f080155
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f10024a
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f10014d
com.seres.usb.upgrade:id/open_search_view_toolbar_container = 0x7f080154
com.seres.usb.upgrade:id/tag_on_receive_content_listener = 0x7f0801ba
com.seres.usb.upgrade:id/open_search_view_toolbar = 0x7f080153
com.seres.usb.upgrade:id/open_search_view_search_prefix = 0x7f080151
com.seres.usb.upgrade:id/open_search_view_scrim = 0x7f080150
com.seres.usb.upgrade:style/ShapeAppearance.Material3.SmallComponent = 0x7f100179
com.seres.usb.upgrade:id/open_search_view_root = 0x7f08014f
com.seres.usb.upgrade:id/pin = 0x7f080163
com.seres.usb.upgrade:id/open_search_view_header_container = 0x7f08014e
com.seres.usb.upgrade:styleable/RecycleListView = 0x7f110074
com.seres.usb.upgrade:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0116
com.seres.usb.upgrade:id/open_search_view_dummy_toolbar = 0x7f08014c
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f100157
com.seres.usb.upgrade:id/open_search_view_divider = 0x7f08014b
com.seres.usb.upgrade:id/open_search_view_content_container = 0x7f08014a
com.seres.usb.upgrade:id/open_search_view_background = 0x7f080148
com.seres.usb.upgrade:id/rightToLeft = 0x7f080172
com.seres.usb.upgrade:id/off = 0x7f080144
com.seres.usb.upgrade:id/notification_main_column_container = 0x7f080143
com.seres.usb.upgrade:id/notification_main_column = 0x7f080142
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f10028f
com.seres.usb.upgrade:id/notification_background = 0x7f080141
com.seres.usb.upgrade:id/north = 0x7f080140
com.seres.usb.upgrade:id/normal = 0x7f08013f
com.seres.usb.upgrade:id/noState = 0x7f08013d
com.seres.usb.upgrade:style/Widget.AppCompat.RatingBar.Small = 0x7f100321
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1002ca
com.seres.usb.upgrade:id/neverCompleteToEnd = 0x7f08013a
com.seres.usb.upgrade:id/never = 0x7f080139
com.seres.usb.upgrade:id/navigation_bar_item_large_label_view = 0x7f080136
com.seres.usb.upgrade:id/navigation_bar_item_labels_group = 0x7f080135
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f100398
com.seres.usb.upgrade:id/navigation_bar_item_icon_container = 0x7f080133
com.seres.usb.upgrade:id/tag_window_insets_animation_callback = 0x7f0801c1
com.seres.usb.upgrade:id/mtrl_picker_text_input_range_start = 0x7f08012e
com.seres.usb.upgrade:id/mtrl_picker_text_input_range_end = 0x7f08012d
com.seres.usb.upgrade:id/mtrl_picker_header_toggle = 0x7f08012b
com.seres.usb.upgrade:id/mtrl_picker_header_selection_text = 0x7f080129
com.seres.usb.upgrade:id/mtrl_picker_header = 0x7f080128
com.seres.usb.upgrade:id/mtrl_motion_snapshot_view = 0x7f080126
com.seres.usb.upgrade:string/bottomsheet_action_collapse = 0x7f0f001f
com.seres.usb.upgrade:id/mtrl_card_checked_layer_id = 0x7f080123
com.seres.usb.upgrade:id/mtrl_calendar_year_selector_frame = 0x7f080122
com.seres.usb.upgrade:id/mtrl_calendar_frame = 0x7f08011d
com.seres.usb.upgrade:id/mtrl_calendar_days_of_week = 0x7f08011c
com.seres.usb.upgrade:id/mtrl_anchor_parent = 0x7f08011a
com.seres.usb.upgrade:style/TextAppearance.AppCompat = 0x7f100194
com.seres.usb.upgrade:id/motion_base = 0x7f080119
com.seres.usb.upgrade:id/month_title = 0x7f080118
com.seres.usb.upgrade:id/month_navigation_fragment_toggle = 0x7f080115
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f100134
com.seres.usb.upgrade:id/month_navigation_bar = 0x7f080114
com.seres.usb.upgrade:id/month_grid = 0x7f080113
com.seres.usb.upgrade:id/mini = 0x7f080112
com.seres.usb.upgrade:id/material_value_index = 0x7f08010e
com.seres.usb.upgrade:integer/m3_sys_motion_path = 0x7f09001f
com.seres.usb.upgrade:id/material_timepicker_view = 0x7f08010d
com.seres.usb.upgrade:styleable/KeyPosition = 0x7f110044
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1001a3
com.seres.usb.upgrade:id/material_timepicker_mode_button = 0x7f08010b
com.seres.usb.upgrade:string/mtrl_picker_range_header_only_end_selected = 0x7f0f007f
com.seres.usb.upgrade:id/material_minute_tv = 0x7f080107
com.seres.usb.upgrade:id/material_hour_text_input = 0x7f080103
com.seres.usb.upgrade:id/material_clock_period_toggle = 0x7f080102
com.seres.usb.upgrade:id/material_clock_period_am_button = 0x7f080100
com.seres.usb.upgrade:id/material_clock_level = 0x7f0800ff
com.seres.usb.upgrade:id/material_clock_face = 0x7f0800fd
com.seres.usb.upgrade:id/material_clock_display_and_toggle = 0x7f0800fc
com.seres.usb.upgrade:id/material_clock_display = 0x7f0800fb
com.seres.usb.upgrade:id/match_parent = 0x7f0800fa
com.seres.usb.upgrade:id/match_constraint = 0x7f0800f9
com.seres.usb.upgrade:id/masked = 0x7f0800f8
com.seres.usb.upgrade:id/m3_side_sheet = 0x7f0800f6
com.seres.usb.upgrade:style/Base.Widget.Material3.ActionMode = 0x7f1000ff
com.seres.usb.upgrade:id/list_item = 0x7f0800f5
com.seres.usb.upgrade:style/Platform.V21.AppCompat.Light = 0x7f100141
com.seres.usb.upgrade:id/listMode = 0x7f0800f4
com.seres.usb.upgrade:id/linear = 0x7f0800f3
com.seres.usb.upgrade:id/line3 = 0x7f0800f2
com.seres.usb.upgrade:style/Base.V7.Theme.AppCompat = 0x7f1000bb
com.seres.usb.upgrade:id/line1 = 0x7f0800f1
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f10007d
com.seres.usb.upgrade:layout/design_bottom_navigation_item = 0x7f0b001e
com.seres.usb.upgrade:id/leftToRight = 0x7f0800ef
com.seres.usb.upgrade:id/layout = 0x7f0800ed
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight.Dialog = 0x7f100210
com.seres.usb.upgrade:id/jumpToStart = 0x7f0800eb
com.seres.usb.upgrade:id/jumpToEnd = 0x7f0800ea
com.seres.usb.upgrade:id/is_pooling_container_tag = 0x7f0800e7
com.seres.usb.upgrade:id/inward = 0x7f0800e6
com.seres.usb.upgrade:id/info = 0x7f0800e4
com.seres.usb.upgrade:id/indeterminate = 0x7f0800e3
com.seres.usb.upgrade:id/included = 0x7f0800e2
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f100418
com.seres.usb.upgrade:id/immediateStop = 0x7f0800e1
com.seres.usb.upgrade:id/ignoreRequest = 0x7f0800df
com.seres.usb.upgrade:id/icon_group = 0x7f0800dc
com.seres.usb.upgrade:id/snackbar_text = 0x7f080199
com.seres.usb.upgrade:id/icon = 0x7f0800db
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1001bf
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007c
com.seres.usb.upgrade:layout/m3_alert_dialog = 0x7f0b002e
com.seres.usb.upgrade:id/horizontal_only = 0x7f0800da
com.seres.usb.upgrade:id/honorRequest = 0x7f0800d9
com.seres.usb.upgrade:id/homeAsUp = 0x7f0800d8
com.seres.usb.upgrade:id/header_title = 0x7f0800d5
com.seres.usb.upgrade:id/groups = 0x7f0800d4
com.seres.usb.upgrade:id/graph_wrap = 0x7f0800d1
com.seres.usb.upgrade:id/graph = 0x7f0800d0
com.seres.usb.upgrade:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
com.seres.usb.upgrade:id/gone = 0x7f0800cf
com.seres.usb.upgrade:id/frost = 0x7f0800cb
com.seres.usb.upgrade:id/fragment_container_view_tag = 0x7f0800ca
com.seres.usb.upgrade:id/floating = 0x7f0800c8
com.seres.usb.upgrade:id/flip = 0x7f0800c7
com.seres.usb.upgrade:id/wrap = 0x7f0801fd
com.seres.usb.upgrade:id/fixed = 0x7f0800c6
com.seres.usb.upgrade:id/search_edit_frame = 0x7f080185
com.seres.usb.upgrade:id/filled = 0x7f0800c0
com.seres.usb.upgrade:id/fill_vertical = 0x7f0800bf
com.seres.usb.upgrade:id/fill_horizontal = 0x7f0800be
com.seres.usb.upgrade:id/fill = 0x7f0800bd
com.seres.usb.upgrade:id/fade = 0x7f0800bc
com.seres.usb.upgrade:style/TextAppearance.Design.HelperText = 0x7f1001cd
com.seres.usb.upgrade:id/expanded_menu = 0x7f0800bb
com.seres.usb.upgrade:id/exitUntilCollapsed = 0x7f0800b9
com.seres.usb.upgrade:id/enterAlwaysCollapsed = 0x7f0800b8
com.seres.usb.upgrade:id/endToStart = 0x7f0800b6
com.seres.usb.upgrade:id/end = 0x7f0800b5
com.seres.usb.upgrade:id/embed = 0x7f0800b4
com.seres.usb.upgrade:id/east = 0x7f0800b0
com.seres.usb.upgrade:id/easeOut = 0x7f0800af
com.seres.usb.upgrade:id/easeInOut = 0x7f0800ae
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015e
com.seres.usb.upgrade:id/easeIn = 0x7f0800ad
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f100057
com.seres.usb.upgrade:id/dropdown_menu = 0x7f0800ac
com.seres.usb.upgrade:id/dragStart = 0x7f0800aa
com.seres.usb.upgrade:id/dragRight = 0x7f0800a9
com.seres.usb.upgrade:id/dragEnd = 0x7f0800a7
com.seres.usb.upgrade:id/dragDown = 0x7f0800a6
com.seres.usb.upgrade:id/enterAlways = 0x7f0800b7
com.seres.usb.upgrade:id/disableHome = 0x7f08009f
com.seres.usb.upgrade:id/direct = 0x7f08009e
com.seres.usb.upgrade:id/dimensions = 0x7f08009d
com.seres.usb.upgrade:id/design_menu_item_text = 0x7f08009a
com.seres.usb.upgrade:layout/design_menu_item_action_area = 0x7f0b0024
com.seres.usb.upgrade:id/design_menu_item_action_area_stub = 0x7f080099
com.seres.usb.upgrade:id/dependency_ordering = 0x7f080096
com.seres.usb.upgrade:id/deltaRelative = 0x7f080095
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016c
com.seres.usb.upgrade:id/decor_content_parent = 0x7f080093
com.seres.usb.upgrade:id/tag_transition_group = 0x7f0801be
com.seres.usb.upgrade:id/decelerateAndComplete = 0x7f080092
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f100153
com.seres.usb.upgrade:id/decelerate = 0x7f080091
com.seres.usb.upgrade:id/date_picker_actions = 0x7f080090
com.seres.usb.upgrade:style/Widget.AppCompat.Light.SearchView = 0x7f100313
com.seres.usb.upgrade:id/cut = 0x7f08008f
com.seres.usb.upgrade:id/customPanel = 0x7f08008e
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f100187
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0097
com.seres.usb.upgrade:macro/m3_comp_filter_chip_container_shape = 0x7f0c0058
com.seres.usb.upgrade:id/cradle = 0x7f08008b
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c0090
com.seres.usb.upgrade:id/counterclockwise = 0x7f08008a
com.seres.usb.upgrade:id/cos = 0x7f080089
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1002ce
com.seres.usb.upgrade:id/coordinator = 0x7f080088
com.seres.usb.upgrade:id/contiguous = 0x7f080086
com.seres.usb.upgrade:id/content = 0x7f080084
com.seres.usb.upgrade:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f10021f
com.seres.usb.upgrade:macro/m3_comp_filled_card_container_color = 0x7f0c0047
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
com.seres.usb.upgrade:id/container = 0x7f080083
com.seres.usb.upgrade:id/constraint = 0x7f080082
com.seres.usb.upgrade:id/confirm_button = 0x7f080081
com.seres.usb.upgrade:id/compress = 0x7f080080
com.seres.usb.upgrade:layout/notification_action_tombstone = 0x7f0b0062
com.seres.usb.upgrade:id/west = 0x7f0801f9
com.seres.usb.upgrade:id/collapseActionView = 0x7f08007f
com.seres.usb.upgrade:id/clockwise = 0x7f08007d
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f10014b
com.seres.usb.upgrade:id/clip_vertical = 0x7f08007c
com.seres.usb.upgrade:id/clip_horizontal = 0x7f08007b
com.seres.usb.upgrade:id/checked = 0x7f080077
com.seres.usb.upgrade:integer/m3_sys_motion_duration_long4 = 0x7f090016
com.seres.usb.upgrade:id/checkbox = 0x7f080076
com.seres.usb.upgrade:id/chains = 0x7f080075
com.seres.usb.upgrade:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1002b1
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1000e5
com.seres.usb.upgrade:id/chain2 = 0x7f080074
com.seres.usb.upgrade:id/chain = 0x7f080073
com.seres.usb.upgrade:id/center_vertical = 0x7f080072
com.seres.usb.upgrade:id/center_horizontal = 0x7f080071
com.seres.usb.upgrade:id/centerInside = 0x7f080070
com.seres.usb.upgrade:id/centerCrop = 0x7f08006f
com.seres.usb.upgrade:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
com.seres.usb.upgrade:id/btnStopService = 0x7f080068
com.seres.usb.upgrade:id/btnStartService = 0x7f080067
com.seres.usb.upgrade:dimen/design_bottom_sheet_modal_elevation = 0x7f06006c
com.seres.usb.upgrade:attr/contentPadding = 0x7f03013f
com.seres.usb.upgrade:id/btnShowActiveTasks = 0x7f080066
com.seres.usb.upgrade:id/btnRefreshStorage = 0x7f080065
com.seres.usb.upgrade:id/btnClearLogs = 0x7f080062
com.seres.usb.upgrade:id/bounceStart = 0x7f080061
com.seres.usb.upgrade:id/bounceBoth = 0x7f08005f
com.seres.usb.upgrade:attr/switchPadding = 0x7f030405
com.seres.usb.upgrade:dimen/mtrl_extended_fab_top_padding = 0x7f0602ab
com.seres.usb.upgrade:id/blocking = 0x7f08005c
com.seres.usb.upgrade:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a0
com.seres.usb.upgrade:id/baseline = 0x7f080058
com.seres.usb.upgrade:id/asConfigured = 0x7f080051
com.seres.usb.upgrade:style/Widget.Material3.Toolbar.Surface = 0x7f1003dc
com.seres.usb.upgrade:id/mtrl_calendar_months = 0x7f08011f
com.seres.usb.upgrade:id/dragLeft = 0x7f0800a8
com.seres.usb.upgrade:id/arc = 0x7f080050
com.seres.usb.upgrade:id/all = 0x7f080049
com.seres.usb.upgrade:id/alertTitle = 0x7f080047
com.seres.usb.upgrade:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1000ab
com.seres.usb.upgrade:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
com.seres.usb.upgrade:id/dragClockwise = 0x7f0800a5
com.seres.usb.upgrade:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600de
com.seres.usb.upgrade:id/actions = 0x7f080044
com.seres.usb.upgrade:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.seres.usb.upgrade:id/action_text = 0x7f080043
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0153
com.seres.usb.upgrade:attr/endIconTintMode = 0x7f0301a8
com.seres.usb.upgrade:drawable/abc_ic_ab_back_material = 0x7f07003e
com.seres.usb.upgrade:id/action_mode_bar = 0x7f080040
com.seres.usb.upgrade:id/action_divider = 0x7f08003c
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060115
com.seres.usb.upgrade:color/material_personalized_color_background = 0x7f05025c
com.seres.usb.upgrade:id/action_container = 0x7f08003a
com.seres.usb.upgrade:id/action_bar_spinner = 0x7f080037
com.seres.usb.upgrade:id/actionUp = 0x7f080032
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b4
com.seres.usb.upgrade:drawable/$avd_hide_password__0 = 0x7f070000
com.seres.usb.upgrade:id/actionDownUp = 0x7f080031
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f100031
com.seres.usb.upgrade:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06012f
com.seres.usb.upgrade:dimen/mtrl_textinput_counter_margin_start = 0x7f0602f9
com.seres.usb.upgrade:id/accessibility_custom_action_8 = 0x7f08002e
com.seres.usb.upgrade:id/accessibility_custom_action_31 = 0x7f080029
com.seres.usb.upgrade:id/accessibility_custom_action_3 = 0x7f080027
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_surface = 0x7f050177
com.seres.usb.upgrade:id/accessibility_custom_action_27 = 0x7f080024
com.seres.usb.upgrade:id/accessibility_custom_action_26 = 0x7f080023
com.seres.usb.upgrade:color/m3_assist_chip_icon_tint_color = 0x7f050061
com.seres.usb.upgrade:id/accessibility_custom_action_24 = 0x7f080021
com.seres.usb.upgrade:layout/material_radial_view_group = 0x7f0b003a
com.seres.usb.upgrade:attr/actionModeBackground = 0x7f030012
com.seres.usb.upgrade:id/accessibility_custom_action_23 = 0x7f080020
com.seres.usb.upgrade:id/accessibility_custom_action_19 = 0x7f08001b
com.seres.usb.upgrade:attr/colorPrimaryInverse = 0x7f030117
com.seres.usb.upgrade:id/accessibility_custom_action_16 = 0x7f080018
com.seres.usb.upgrade:id/accessibility_custom_action_12 = 0x7f080014
com.seres.usb.upgrade:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a9
com.seres.usb.upgrade:id/accessibility_custom_action_1 = 0x7f080011
com.seres.usb.upgrade:id/accelerate = 0x7f08000e
com.seres.usb.upgrade:id/TOP_END = 0x7f08000c
com.seres.usb.upgrade:id/META = 0x7f080005
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f100307
com.seres.usb.upgrade:attr/cardUseCompatPadding = 0x7f0300a0
com.seres.usb.upgrade:id/CTRL = 0x7f080003
com.seres.usb.upgrade:style/Widget.MaterialComponents.Snackbar = 0x7f10043a
com.seres.usb.upgrade:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f100155
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060138
com.seres.usb.upgrade:id/BOTTOM_START = 0x7f080002
com.seres.usb.upgrade:drawable/notification_template_icon_bg = 0x7f0700e1
com.seres.usb.upgrade:drawable/notification_bg_normal_pressed = 0x7f0700df
com.seres.usb.upgrade:string/mtrl_checkbox_button_path_group_name = 0x7f0f0063
com.seres.usb.upgrade:color/m3_sys_color_secondary_fixed = 0x7f0501e0
com.seres.usb.upgrade:drawable/notification_bg_normal = 0x7f0700de
com.seres.usb.upgrade:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
com.seres.usb.upgrade:drawable/notification_action_background = 0x7f0700d9
com.seres.usb.upgrade:dimen/m3_chip_hovered_translation_z = 0x7f0600f8
com.seres.usb.upgrade:drawable/navigation_empty_icon = 0x7f0700d8
com.seres.usb.upgrade:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1003d1
com.seres.usb.upgrade:drawable/mtrl_switch_track = 0x7f0700d5
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1002d6
com.seres.usb.upgrade:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700d4
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_checked = 0x7f0700cc
com.seres.usb.upgrade:xml/backup_rules = 0x7f120000
com.seres.usb.upgrade:drawable/mtrl_switch_thumb = 0x7f0700cb
com.seres.usb.upgrade:drawable/mtrl_ic_indeterminate = 0x7f0700c7
com.seres.usb.upgrade:dimen/m3_fab_translation_z_pressed = 0x7f0601b2
com.seres.usb.upgrade:drawable/mtrl_ic_error = 0x7f0700c6
com.seres.usb.upgrade:drawable/mtrl_ic_checkbox_checked = 0x7f0700c4
com.seres.usb.upgrade:drawable/mtrl_ic_arrow_drop_down = 0x7f0700c0
com.seres.usb.upgrade:drawable/mtrl_dialog_background = 0x7f0700be
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700bb
com.seres.usb.upgrade:attr/checkboxStyle = 0x7f0300b1
com.seres.usb.upgrade:attr/cornerSizeBottomRight = 0x7f030153
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700b9
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon = 0x7f0700b6
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700b5
com.seres.usb.upgrade:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700b2
com.seres.usb.upgrade:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.seres.usb.upgrade:color/m3_ref_palette_neutral96 = 0x7f05010c
com.seres.usb.upgrade:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700b1
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0173
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
com.seres.usb.upgrade:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700ad
com.seres.usb.upgrade:style/Theme.MaterialComponents.CompactMenu = 0x7f100246
com.seres.usb.upgrade:drawable/material_ic_clear_black_24dp = 0x7f0700ab
com.seres.usb.upgrade:macro/m3_comp_elevated_card_container_shape = 0x7f0c002c
com.seres.usb.upgrade:drawable/material_ic_calendar_black_24dp = 0x7f0700aa
com.seres.usb.upgrade:drawable/material_cursor_drawable = 0x7f0700a9
com.seres.usb.upgrade:drawable/m3_tabs_transparent_background = 0x7f0700a8
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c0120
com.seres.usb.upgrade:id/accessibility_custom_action_13 = 0x7f080015
com.seres.usb.upgrade:dimen/m3_navigation_item_shape_inset_start = 0x7f0601bd
com.seres.usb.upgrade:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602aa
com.seres.usb.upgrade:drawable/m3_tabs_rounded_line_indicator = 0x7f0700a7
com.seres.usb.upgrade:drawable/m3_tabs_background = 0x7f0700a5
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f10018f
com.seres.usb.upgrade:attr/dividerVertical = 0x7f030182
com.seres.usb.upgrade:drawable/m3_password_eye = 0x7f0700a1
com.seres.usb.upgrade:drawable/ic_search_black_24 = 0x7f07009b
com.seres.usb.upgrade:string/mtrl_picker_navigate_to_current_year_description = 0x7f0f007c
com.seres.usb.upgrade:drawable/ic_mtrl_chip_close_circle = 0x7f07009a
com.seres.usb.upgrade:color/m3_ref_palette_error30 = 0x7f0500ee
com.seres.usb.upgrade:drawable/ic_mtrl_chip_checked_black = 0x7f070098
com.seres.usb.upgrade:string/mtrl_timepicker_confirm = 0x7f0f009a
com.seres.usb.upgrade:attr/materialSearchBarStyle = 0x7f0302f5
com.seres.usb.upgrade:drawable/ic_mtrl_checked_circle = 0x7f070097
com.seres.usb.upgrade:style/Theme.AppCompat.Dialog = 0x7f100215
com.seres.usb.upgrade:macro/m3_comp_outlined_card_outline_color = 0x7f0c00b0
com.seres.usb.upgrade:drawable/ic_m3_chip_close = 0x7f070096
com.seres.usb.upgrade:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1000b3
com.seres.usb.upgrade:drawable/ic_m3_chip_check = 0x7f070094
com.seres.usb.upgrade:attr/goIcon = 0x7f03020c
com.seres.usb.upgrade:drawable/ic_launcher_foreground = 0x7f070093
com.seres.usb.upgrade:drawable/ic_keyboard_black_24dp = 0x7f070091
com.seres.usb.upgrade:drawable/ic_call_decline_low = 0x7f07008e
com.seres.usb.upgrade:drawable/ic_call_answer_video = 0x7f07008b
com.seres.usb.upgrade:drawable/ic_arrow_back_black_24 = 0x7f070088
com.seres.usb.upgrade:drawable/design_ic_visibility = 0x7f070084
com.seres.usb.upgrade:attr/quantizeMotionInterpolator = 0x7f030386
com.seres.usb.upgrade:drawable/mtrl_navigation_bar_item_background = 0x7f0700c8
com.seres.usb.upgrade:drawable/button_background = 0x7f070082
com.seres.usb.upgrade:id/transitionToStart = 0x7f0801db
com.seres.usb.upgrade:attr/useMaterialThemeColors = 0x7f0304b9
com.seres.usb.upgrade:id/accessibility_custom_action_9 = 0x7f08002f
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Display4 = 0x7f10019c
com.seres.usb.upgrade:drawable/btn_radio_on_mtrl = 0x7f070080
com.seres.usb.upgrade:attr/touchRegionId = 0x7f03049f
com.seres.usb.upgrade:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007b
com.seres.usb.upgrade:drawable/m3_tabs_line_indicator = 0x7f0700a6
com.seres.usb.upgrade:layout/material_clock_period_toggle_land = 0x7f0b0037
com.seres.usb.upgrade:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.seres.usb.upgrade:color/m3_highlighted_text = 0x7f05008c
com.seres.usb.upgrade:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070075
com.seres.usb.upgrade:style/Widget.MaterialComponents.ShapeableImageView = 0x7f100438
com.seres.usb.upgrade:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070074
com.seres.usb.upgrade:drawable/abc_textfield_default_mtrl_alpha = 0x7f070073
com.seres.usb.upgrade:attr/titleTextAppearance = 0x7f03048f
com.seres.usb.upgrade:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070072
com.seres.usb.upgrade:drawable/abc_text_select_handle_middle_mtrl = 0x7f070070
com.seres.usb.upgrade:drawable/abc_text_cursor_material = 0x7f07006e
com.seres.usb.upgrade:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f10020b
com.seres.usb.upgrade:attr/arrowShaftLength = 0x7f03003a
com.seres.usb.upgrade:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070061
com.seres.usb.upgrade:drawable/abc_switch_track_mtrl_alpha = 0x7f07006b
com.seres.usb.upgrade:drawable/abc_star_half_black_48dp = 0x7f070069
com.seres.usb.upgrade:drawable/abc_spinner_textfield_background_material = 0x7f070067
com.seres.usb.upgrade:id/selected = 0x7f08018c
com.seres.usb.upgrade:drawable/abc_seekbar_track_material = 0x7f070065
com.seres.usb.upgrade:drawable/abc_seekbar_tick_mark_material = 0x7f070064
com.seres.usb.upgrade:layout/design_layout_tab_text = 0x7f0b0023
com.seres.usb.upgrade:id/currentState = 0x7f08008c
com.seres.usb.upgrade:id/accessibility_custom_action_20 = 0x7f08001d
com.seres.usb.upgrade:color/m3_sys_color_dark_on_background = 0x7f050152
com.seres.usb.upgrade:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005f
com.seres.usb.upgrade:drawable/abc_list_selector_holo_light = 0x7f070058
com.seres.usb.upgrade:dimen/mtrl_progress_circular_inset = 0x7f0602cd
com.seres.usb.upgrade:drawable/abc_list_selector_disabled_holo_light = 0x7f070056
com.seres.usb.upgrade:drawable/abc_list_selector_background_transition_holo_light = 0x7f070054
com.seres.usb.upgrade:drawable/abc_list_divider_mtrl_alpha = 0x7f07004e
com.seres.usb.upgrade:layout/material_clock_period_toggle = 0x7f0b0036
com.seres.usb.upgrade:attr/iconTintMode = 0x7f03022b
com.seres.usb.upgrade:drawable/abc_item_background_holo_light = 0x7f07004c
com.seres.usb.upgrade:styleable/KeyCycle = 0x7f110040
com.seres.usb.upgrade:attr/checkMarkTintMode = 0x7f0300b0
com.seres.usb.upgrade:drawable/abc_ic_voice_search_api_material = 0x7f07004a
com.seres.usb.upgrade:drawable/abc_ic_search_api_material = 0x7f070049
com.seres.usb.upgrade:attr/arcMode = 0x7f030038
com.seres.usb.upgrade:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070048
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ActionButton = 0x7f1000c8
com.seres.usb.upgrade:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070047
com.seres.usb.upgrade:id/multiply = 0x7f080131
com.seres.usb.upgrade:dimen/mtrl_btn_padding_bottom = 0x7f06025e
com.seres.usb.upgrade:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070046
com.seres.usb.upgrade:dimen/fastscroll_margin = 0x7f060091
com.seres.usb.upgrade:drawable/abc_ic_go_search_api_material = 0x7f070042
com.seres.usb.upgrade:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f100154
com.seres.usb.upgrade:id/textSpacerNoTitle = 0x7f0801c6
com.seres.usb.upgrade:attr/colorOnPrimary = 0x7f030100
com.seres.usb.upgrade:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070041
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f100261
com.seres.usb.upgrade:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0127
com.seres.usb.upgrade:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003f
com.seres.usb.upgrade:id/material_textinput_timepicker = 0x7f080108
com.seres.usb.upgrade:drawable/abc_control_background_material = 0x7f07003b
com.seres.usb.upgrade:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.seres.usb.upgrade:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602ad
com.seres.usb.upgrade:drawable/abc_cab_background_top_mtrl_alpha = 0x7f07003a
com.seres.usb.upgrade:attr/hintEnabled = 0x7f03021c
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_unchecked = 0x7f0700d2
com.seres.usb.upgrade:attr/removeEmbeddedFabElevation = 0x7f03039a
com.seres.usb.upgrade:drawable/abc_cab_background_internal_bg = 0x7f070038
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f100190
com.seres.usb.upgrade:layout/notification_action = 0x7f0b0061
com.seres.usb.upgrade:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602ac
com.seres.usb.upgrade:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070037
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.TextButton = 0x7f1003f7
com.seres.usb.upgrade:string/character_counter_pattern = 0x7f0f002d
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013c
com.seres.usb.upgrade:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070034
com.seres.usb.upgrade:drawable/abc_btn_radio_material = 0x7f070032
com.seres.usb.upgrade:id/ifRoom = 0x7f0800dd
com.seres.usb.upgrade:drawable/abc_btn_check_material_anim = 0x7f07002d
com.seres.usb.upgrade:drawable/abc_btn_check_material = 0x7f07002c
com.seres.usb.upgrade:styleable/Slider = 0x7f11007d
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f1003f5
com.seres.usb.upgrade:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070029
com.seres.usb.upgrade:styleable/DrawerLayout = 0x7f110030
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1002d5
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070027
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070025
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f100205
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Button.Colored = 0x7f1000d2
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070023
com.seres.usb.upgrade:attr/motionEffect_end = 0x7f030337
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070022
com.seres.usb.upgrade:attr/centerIfNoTextEnabled = 0x7f0300ac
com.seres.usb.upgrade:color/m3_sys_color_dark_outline = 0x7f05015d
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700d3
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f100293
com.seres.usb.upgrade:id/action_menu_divider = 0x7f08003e
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070021
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f050197
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001d
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001c
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f07001a
com.seres.usb.upgrade:dimen/abc_control_inset_material = 0x7f060019
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070018
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070011
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f10002a
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000d
com.seres.usb.upgrade:layout/mtrl_picker_header_selection_text = 0x7f0b005a
com.seres.usb.upgrade:drawable/$m3_avd_show_password__2 = 0x7f07000c
com.seres.usb.upgrade:drawable/$m3_avd_show_password__0 = 0x7f07000a
com.seres.usb.upgrade:drawable/$m3_avd_hide_password__1 = 0x7f070008
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f100454
com.seres.usb.upgrade:drawable/$m3_avd_hide_password__0 = 0x7f070007
com.seres.usb.upgrade:drawable/$ic_launcher_foreground__0 = 0x7f070006
com.seres.usb.upgrade:drawable/$avd_show_password__2 = 0x7f070005
com.seres.usb.upgrade:style/Base.Widget.Material3.CollapsingToolbar = 0x7f100103
com.seres.usb.upgrade:drawable/$avd_show_password__1 = 0x7f070004
com.seres.usb.upgrade:id/right = 0x7f080171
com.seres.usb.upgrade:dimen/tooltip_y_offset_touch = 0x7f06031a
com.seres.usb.upgrade:drawable/abc_list_selector_holo_dark = 0x7f070057
com.seres.usb.upgrade:attr/actionModeSelectAllDrawable = 0x7f03001b
com.seres.usb.upgrade:dimen/tooltip_precise_anchor_threshold = 0x7f060317
com.seres.usb.upgrade:attr/thumbTint = 0x7f030476
com.seres.usb.upgrade:attr/actionModeShareDrawable = 0x7f03001c
com.seres.usb.upgrade:attr/homeAsUpIndicator = 0x7f03021f
com.seres.usb.upgrade:dimen/tooltip_precise_anchor_extra_offset = 0x7f060316
com.seres.usb.upgrade:drawable/m3_popupmenu_background_overlay = 0x7f0700a2
com.seres.usb.upgrade:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700b3
com.seres.usb.upgrade:dimen/tooltip_horizontal_padding = 0x7f060314
com.seres.usb.upgrade:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0118
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070015
com.seres.usb.upgrade:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060127
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0601f7
com.seres.usb.upgrade:dimen/notification_top_pad_large_text = 0x7f060312
com.seres.usb.upgrade:macro/m3_comp_filled_button_label_text_type = 0x7f0c0046
com.seres.usb.upgrade:attr/colorSurfaceContainerLow = 0x7f030124
com.seres.usb.upgrade:id/buttonPanel = 0x7f080069
com.seres.usb.upgrade:attr/actionBarWidgetTheme = 0x7f03000c
com.seres.usb.upgrade:dimen/m3_comp_badge_large_size = 0x7f0600ff
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f050189
com.seres.usb.upgrade:dimen/notification_subtext_size = 0x7f060310
com.seres.usb.upgrade:dimen/notification_small_icon_size_as_large = 0x7f06030f
com.seres.usb.upgrade:attr/clickAction = 0x7f0300d9
com.seres.usb.upgrade:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f06018a
com.seres.usb.upgrade:dimen/notification_small_icon_background_padding = 0x7f06030e
com.seres.usb.upgrade:dimen/notification_large_icon_height = 0x7f060308
com.seres.usb.upgrade:dimen/notification_action_icon_size = 0x7f060304
com.seres.usb.upgrade:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060101
com.seres.usb.upgrade:id/btnOpenUsb = 0x7f080064
com.seres.usb.upgrade:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e9
com.seres.usb.upgrade:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f060303
com.seres.usb.upgrade:dimen/mtrl_tooltip_minWidth = 0x7f060301
com.seres.usb.upgrade:attr/mock_labelColor = 0x7f030316
com.seres.usb.upgrade:attr/haloRadius = 0x7f03020f
com.seres.usb.upgrade:dimen/m3_ripple_hovered_alpha = 0x7f0601cf
com.seres.usb.upgrade:dimen/mtrl_tooltip_minHeight = 0x7f060300
com.seres.usb.upgrade:styleable/MaterialButtonToggleGroup = 0x7f110050
com.seres.usb.upgrade:dimen/mtrl_toolbar_default_height = 0x7f0602fd
com.seres.usb.upgrade:attr/sideSheetModalStyle = 0x7f0303c6
com.seres.usb.upgrade:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f0602f8
com.seres.usb.upgrade:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0602f7
com.seres.usb.upgrade:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0602f6
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f100254
com.seres.usb.upgrade:dimen/mtrl_switch_track_width = 0x7f0602f3
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c0130
com.seres.usb.upgrade:dimen/mtrl_switch_text_padding = 0x7f0602ee
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f100069
com.seres.usb.upgrade:string/mtrl_switch_thumb_group_name = 0x7f0f0091
com.seres.usb.upgrade:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602ed
com.seres.usb.upgrade:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1000a7
com.seres.usb.upgrade:attr/materialCalendarDay = 0x7f0302d9
com.seres.usb.upgrade:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602e9
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501a6
com.seres.usb.upgrade:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600be
com.seres.usb.upgrade:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602e8
com.seres.usb.upgrade:id/reverseSawtooth = 0x7f080170
com.seres.usb.upgrade:dimen/mtrl_slider_track_side_padding = 0x7f0602e6
com.seres.usb.upgrade:id/scrollable = 0x7f080180
com.seres.usb.upgrade:id/accessibility_custom_action_18 = 0x7f08001a
com.seres.usb.upgrade:dimen/mtrl_shape_corner_size_small_component = 0x7f0602dd
com.seres.usb.upgrade:id/tag_state_description = 0x7f0801bd
com.seres.usb.upgrade:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602d8
com.seres.usb.upgrade:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602d7
com.seres.usb.upgrade:color/dim_foreground_material_dark = 0x7f050058
com.seres.usb.upgrade:dimen/mtrl_progress_circular_size_medium = 0x7f0602d4
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011d
com.seres.usb.upgrade:dimen/mtrl_progress_circular_size = 0x7f0602d2
com.seres.usb.upgrade:id/accessibility_custom_action_22 = 0x7f08001f
com.seres.usb.upgrade:id/TOP_START = 0x7f08000d
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f060207
com.seres.usb.upgrade:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602ce
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602cb
com.seres.usb.upgrade:attr/hideOnScroll = 0x7f03021a
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_margin = 0x7f0602ca
com.seres.usb.upgrade:id/mtrl_child_content_container = 0x7f080124
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_icon_size = 0x7f0602c9
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_elevation = 0x7f0602c7
com.seres.usb.upgrade:attr/windowNoTitle = 0x7f0304d4
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_default_width = 0x7f0602c6
com.seres.usb.upgrade:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602c3
com.seres.usb.upgrade:dimen/abc_progress_bar_height_material = 0x7f060035
com.seres.usb.upgrade:dimen/mtrl_navigation_item_icon_size = 0x7f0602c1
com.seres.usb.upgrade:id/edge = 0x7f0800b1
com.seres.usb.upgrade:attr/fontProviderAuthority = 0x7f0301fc
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602c4
com.seres.usb.upgrade:integer/material_motion_duration_short_2 = 0x7f09002b
com.seres.usb.upgrade:dimen/mtrl_navigation_item_icon_padding = 0x7f0602c0
com.seres.usb.upgrade:attr/maxAcceleration = 0x7f0302ff
com.seres.usb.upgrade:dimen/mtrl_navigation_elevation = 0x7f0602be
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Menu = 0x7f100027
com.seres.usb.upgrade:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602bd
com.seres.usb.upgrade:color/m3_ref_palette_primary0 = 0x7f05011c
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050182
com.seres.usb.upgrade:dimen/m3_btn_padding_right = 0x7f0600db
com.seres.usb.upgrade:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602bc
com.seres.usb.upgrade:dimen/mtrl_tooltip_arrowSize = 0x7f0602fe
com.seres.usb.upgrade:id/navigation_bar_item_small_label_view = 0x7f080137
com.seres.usb.upgrade:id/accessibility_custom_action_7 = 0x7f08002d
com.seres.usb.upgrade:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602ba
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant70 = 0x7f050117
com.seres.usb.upgrade:dimen/mtrl_low_ripple_default_alpha = 0x7f0602b7
com.seres.usb.upgrade:color/material_dynamic_secondary99 = 0x7f050237
com.seres.usb.upgrade:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602b5
com.seres.usb.upgrade:dimen/mtrl_high_ripple_default_alpha = 0x7f0602b3
com.seres.usb.upgrade:color/material_grey_800 = 0x7f050249
com.seres.usb.upgrade:dimen/design_snackbar_background_corner_radius = 0x7f060080
com.seres.usb.upgrade:dimen/mtrl_fab_translation_z_pressed = 0x7f0602b2
com.seres.usb.upgrade:dimen/mtrl_fab_min_touch_target = 0x7f0602b0
com.seres.usb.upgrade:drawable/notification_template_icon_low_bg = 0x7f0700e2
com.seres.usb.upgrade:dimen/mtrl_fab_elevation = 0x7f0602af
com.seres.usb.upgrade:drawable/$avd_hide_password__1 = 0x7f070001
com.seres.usb.upgrade:id/month_navigation_previous = 0x7f080117
com.seres.usb.upgrade:dimen/mtrl_extended_fab_start_padding = 0x7f0602a9
com.seres.usb.upgrade:dimen/mtrl_extended_fab_min_width = 0x7f0602a8
com.seres.usb.upgrade:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bc
com.seres.usb.upgrade:dimen/mtrl_extended_fab_min_height = 0x7f0602a7
com.seres.usb.upgrade:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602a6
com.seres.usb.upgrade:dimen/mtrl_extended_fab_end_padding = 0x7f0602a3
com.seres.usb.upgrade:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00fb
com.seres.usb.upgrade:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602a1
com.seres.usb.upgrade:style/Platform.V25.AppCompat.Light = 0x7f100143
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060121
com.seres.usb.upgrade:attr/customReference = 0x7f030169
com.seres.usb.upgrade:dimen/mtrl_extended_fab_bottom_padding = 0x7f06029f
com.seres.usb.upgrade:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002f
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f100445
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1001c2
com.seres.usb.upgrade:dimen/mtrl_chip_text_size = 0x7f06029b
com.seres.usb.upgrade:dimen/mtrl_chip_pressed_translation_z = 0x7f06029a
com.seres.usb.upgrade:dimen/mtrl_card_spacing = 0x7f060299
com.seres.usb.upgrade:dimen/mtrl_card_dragged_z = 0x7f060297
com.seres.usb.upgrade:dimen/mtrl_card_corner_radius = 0x7f060296
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f05017c
com.seres.usb.upgrade:dimen/mtrl_card_checked_icon_margin = 0x7f060294
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f100039
com.seres.usb.upgrade:dimen/mtrl_calendar_year_width = 0x7f060293
com.seres.usb.upgrade:dimen/mtrl_calendar_year_height = 0x7f060290
com.seres.usb.upgrade:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f100431
com.seres.usb.upgrade:dimen/mtrl_calendar_title_baseline_to_top = 0x7f06028d
com.seres.usb.upgrade:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f06028b
com.seres.usb.upgrade:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.seres.usb.upgrade:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060175
com.seres.usb.upgrade:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060289
com.seres.usb.upgrade:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f06027f
com.seres.usb.upgrade:dimen/mtrl_calendar_header_height_fullscreen = 0x7f06027b
com.seres.usb.upgrade:dimen/mtrl_calendar_dialog_background_inset = 0x7f060276
com.seres.usb.upgrade:dimen/mtrl_calendar_day_width = 0x7f060274
com.seres.usb.upgrade:dimen/mtrl_calendar_day_vertical_padding = 0x7f060273
com.seres.usb.upgrade:drawable/abc_list_selector_disabled_holo_dark = 0x7f070055
com.seres.usb.upgrade:dimen/mtrl_calendar_day_today_stroke = 0x7f060272
com.seres.usb.upgrade:dimen/m3_card_dragged_z = 0x7f0600e5
com.seres.usb.upgrade:drawable/ic_clear_black_24 = 0x7f07008f
com.seres.usb.upgrade:dimen/mtrl_calendar_content_padding = 0x7f06026e
com.seres.usb.upgrade:dimen/mtrl_calendar_bottom_padding = 0x7f06026d
com.seres.usb.upgrade:attr/flow_verticalStyle = 0x7f0301f8
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060163
com.seres.usb.upgrade:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f06026a
com.seres.usb.upgrade:dimen/mtrl_btn_text_size = 0x7f060268
com.seres.usb.upgrade:dimen/mtrl_btn_text_btn_padding_left = 0x7f060266
com.seres.usb.upgrade:dimen/mtrl_btn_text_btn_icon_padding = 0x7f060265
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Light = 0x7f10008e
com.seres.usb.upgrade:dimen/mtrl_btn_padding_right = 0x7f060260
com.seres.usb.upgrade:dimen/mtrl_btn_max_width = 0x7f06025d
com.seres.usb.upgrade:color/m3_sys_color_on_secondary_fixed = 0x7f0501da
com.seres.usb.upgrade:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060259
com.seres.usb.upgrade:attr/textAppearanceLargePopupMenu = 0x7f030440
com.seres.usb.upgrade:dimen/mtrl_btn_focused_z = 0x7f060257
com.seres.usb.upgrade:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.seres.usb.upgrade:dimen/mtrl_btn_elevation = 0x7f060256
com.seres.usb.upgrade:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f06024d
com.seres.usb.upgrade:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f06024c
com.seres.usb.upgrade:color/mtrl_btn_text_btn_ripple_color = 0x7f05029d
com.seres.usb.upgrade:drawable/ic_launcher_background = 0x7f070092
com.seres.usb.upgrade:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f060245
com.seres.usb.upgrade:id/autoCompleteToEnd = 0x7f080055
com.seres.usb.upgrade:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f100110
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070013
com.seres.usb.upgrade:attr/clockIcon = 0x7f0300dc
com.seres.usb.upgrade:attr/state_collapsible = 0x7f0303e9
com.seres.usb.upgrade:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060240
com.seres.usb.upgrade:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f06023f
com.seres.usb.upgrade:dimen/material_time_picker_minimum_screen_height = 0x7f06023c
com.seres.usb.upgrade:dimen/material_textinput_default_width = 0x7f060239
com.seres.usb.upgrade:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f060236
com.seres.usb.upgrade:dimen/material_emphasis_medium = 0x7f06022e
com.seres.usb.upgrade:dimen/material_emphasis_high_type = 0x7f06022d
com.seres.usb.upgrade:dimen/material_emphasis_disabled = 0x7f06022b
com.seres.usb.upgrade:dimen/material_divider_thickness = 0x7f06022a
com.seres.usb.upgrade:styleable/CheckedTextView = 0x7f11001c
com.seres.usb.upgrade:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f10029e
com.seres.usb.upgrade:id/up = 0x7f0801ee
com.seres.usb.upgrade:id/auto = 0x7f080053
com.seres.usb.upgrade:styleable/MotionLabel = 0x7f110065
com.seres.usb.upgrade:dimen/m3_card_elevated_hovered_z = 0x7f0600e9
com.seres.usb.upgrade:dimen/m3_comp_filled_card_container_elevation = 0x7f060125
com.seres.usb.upgrade:dimen/material_cursor_width = 0x7f060229
com.seres.usb.upgrade:attr/reactiveGuide_animateChange = 0x7f030391
com.seres.usb.upgrade:dimen/material_clock_size = 0x7f060227
com.seres.usb.upgrade:layout/mtrl_picker_fullscreen = 0x7f0b0057
com.seres.usb.upgrade:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060242
com.seres.usb.upgrade:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c8
com.seres.usb.upgrade:dimen/material_clock_hand_stroke_width = 0x7f060221
com.seres.usb.upgrade:dimen/material_clock_face_margin_top = 0x7f06021e
com.seres.usb.upgrade:id/text_input_error_icon = 0x7f0801ca
com.seres.usb.upgrade:dimen/material_clock_display_width = 0x7f06021d
com.seres.usb.upgrade:id/NO_DEBUG = 0x7f080006
com.seres.usb.upgrade:dimen/m3_timepicker_window_elevation = 0x7f060218
com.seres.usb.upgrade:dimen/m3_timepicker_display_stroke_width = 0x7f060217
com.seres.usb.upgrade:style/TextAppearance.Material3.SearchView = 0x7f1001f3
com.seres.usb.upgrade:attr/behavior_autoShrink = 0x7f030068
com.seres.usb.upgrade:attr/colorPrimaryFixedDim = 0x7f030116
com.seres.usb.upgrade:attr/waveDecay = 0x7f0304c5
com.seres.usb.upgrade:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f060216
com.seres.usb.upgrade:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f060214
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060210
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f06020f
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f06020d
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f06020b
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f10011d
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0019
com.seres.usb.upgrade:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f060243
com.seres.usb.upgrade:id/matrix = 0x7f08010f
com.seres.usb.upgrade:dimen/material_clock_period_toggle_horizontal_gap = 0x7f060224
com.seres.usb.upgrade:attr/backgroundOverlayColorAlpha = 0x7f03004d
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060209
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f060208
com.seres.usb.upgrade:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.seres.usb.upgrade:color/mtrl_error = 0x7f0502ac
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060200
com.seres.usb.upgrade:styleable/ShapeAppearance = 0x7f11007a
com.seres.usb.upgrade:id/ALT = 0x7f080000
com.seres.usb.upgrade:id/transition_transform = 0x7f0801e0
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f0601fb
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c0091
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f0601fa
com.seres.usb.upgrade:string/clear_text_end_icon_content_description = 0x7f0f002e
com.seres.usb.upgrade:drawable/mtrl_ic_check_mark = 0x7f0700c3
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0601f8
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500ae
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601f6
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601f4
com.seres.usb.upgrade:style/Base.Theme.AppCompat = 0x7f10004b
com.seres.usb.upgrade:dimen/material_clock_display_padding = 0x7f06021c
com.seres.usb.upgrade:dimen/m3_sys_elevation_level1 = 0x7f0601ea
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016f
com.seres.usb.upgrade:drawable/abc_list_pressed_holo_dark = 0x7f070051
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_outline = 0x7f050199
com.seres.usb.upgrade:dimen/mtrl_slider_track_height = 0x7f0602e5
com.seres.usb.upgrade:dimen/m3_snackbar_margin = 0x7f0601e8
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016b
com.seres.usb.upgrade:dimen/m3_small_fab_size = 0x7f0601e6
com.seres.usb.upgrade:dimen/material_textinput_min_width = 0x7f06023b
com.seres.usb.upgrade:color/material_dynamic_primary20 = 0x7f050221
com.seres.usb.upgrade:dimen/m3_slider_thumb_elevation = 0x7f0601e4
com.seres.usb.upgrade:dimen/m3_simple_item_color_selected_alpha = 0x7f0601e2
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00cb
com.seres.usb.upgrade:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601e1
com.seres.usb.upgrade:dimen/m3_side_sheet_width = 0x7f0601e0
com.seres.usb.upgrade:dimen/m3_side_sheet_standard_elevation = 0x7f0601df
com.seres.usb.upgrade:dimen/m3_side_sheet_margin_detached = 0x7f0601dd
com.seres.usb.upgrade:attr/helperText = 0x7f030212
com.seres.usb.upgrade:dimen/m3_searchview_elevation = 0x7f0601db
com.seres.usb.upgrade:dimen/m3_searchbar_text_size = 0x7f0601d9
com.seres.usb.upgrade:color/material_dynamic_primary0 = 0x7f05021e
com.seres.usb.upgrade:dimen/m3_searchbar_padding_start = 0x7f0601d7
com.seres.usb.upgrade:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1000a0
com.seres.usb.upgrade:id/action_bar_title = 0x7f080039
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f100135
com.seres.usb.upgrade:attr/showPaths = 0x7f0303c1
com.seres.usb.upgrade:dimen/design_bottom_navigation_elevation = 0x7f060062
com.seres.usb.upgrade:dimen/m3_searchbar_height = 0x7f0601d3
com.seres.usb.upgrade:dimen/m3_searchbar_elevation = 0x7f0601d2
com.seres.usb.upgrade:style/Base.V24.Theme.Material3.Light = 0x7f1000b4
com.seres.usb.upgrade:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601d1
com.seres.usb.upgrade:layout/mtrl_picker_header_dialog = 0x7f0b0058
com.seres.usb.upgrade:dimen/m3_ripple_pressed_alpha = 0x7f0601d0
com.seres.usb.upgrade:dimen/m3_sys_elevation_level2 = 0x7f0601eb
com.seres.usb.upgrade:dimen/m3_ripple_focused_alpha = 0x7f0601ce
com.seres.usb.upgrade:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f9
com.seres.usb.upgrade:id/custom = 0x7f08008d
com.seres.usb.upgrade:dimen/m3_ripple_default_alpha = 0x7f0601cd
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601c9
com.seres.usb.upgrade:dimen/m3_navigation_rail_elevation = 0x7f0601c3
com.seres.usb.upgrade:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601c1
com.seres.usb.upgrade:id/design_menu_item_action_area = 0x7f080098
com.seres.usb.upgrade:attr/color = 0x7f0300f1
com.seres.usb.upgrade:dimen/notification_big_circle_margin = 0x7f060306
com.seres.usb.upgrade:dimen/m3_navigation_item_vertical_padding = 0x7f0601bf
com.seres.usb.upgrade:dimen/m3_navigation_item_shape_inset_top = 0x7f0601be
com.seres.usb.upgrade:dimen/m3_navigation_item_horizontal_padding = 0x7f0601b9
com.seres.usb.upgrade:style/Widget.AppCompat.CompoundButton.Switch = 0x7f1002fa
com.seres.usb.upgrade:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f100081
com.seres.usb.upgrade:integer/mtrl_view_gone = 0x7f09003f
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700bd
com.seres.usb.upgrade:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1002a0
com.seres.usb.upgrade:attr/layout_goneMarginEnd = 0x7f0302a5
com.seres.usb.upgrade:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601b7
com.seres.usb.upgrade:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f100358
com.seres.usb.upgrade:attr/backgroundInsetStart = 0x7f03004b
com.seres.usb.upgrade:dimen/m3_large_fab_size = 0x7f0601b4
com.seres.usb.upgrade:dimen/material_time_picker_minimum_screen_width = 0x7f06023d
com.seres.usb.upgrade:dimen/m3_large_fab_max_image_size = 0x7f0601b3
com.seres.usb.upgrade:dimen/m3_fab_corner_size = 0x7f0601b0
com.seres.usb.upgrade:attr/layout_insetEdge = 0x7f0302aa
com.seres.usb.upgrade:dimen/m3_extended_fab_start_padding = 0x7f0601ad
com.seres.usb.upgrade:styleable/BottomAppBar = 0x7f110015
com.seres.usb.upgrade:dimen/m3_extended_fab_icon_padding = 0x7f0601ab
com.seres.usb.upgrade:color/abc_hint_foreground_material_light = 0x7f050008
com.seres.usb.upgrade:dimen/m3_extended_fab_bottom_padding = 0x7f0601a9
com.seres.usb.upgrade:dimen/m3_datepicker_elevation = 0x7f0601a7
com.seres.usb.upgrade:attr/closeItemLayout = 0x7f0300e5
com.seres.usb.upgrade:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601a3
com.seres.usb.upgrade:string/mtrl_picker_toggle_to_text_input_mode = 0x7f0f008f
com.seres.usb.upgrade:dimen/m3_searchbar_margin_vertical = 0x7f0601d5
com.seres.usb.upgrade:attr/subtitle = 0x7f0303fb
com.seres.usb.upgrade:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f06019d
com.seres.usb.upgrade:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f06019b
com.seres.usb.upgrade:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f060198
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f100030
com.seres.usb.upgrade:attr/motionDurationLong1 = 0x7f03031e
com.seres.usb.upgrade:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f060284
com.seres.usb.upgrade:dimen/highlight_alpha_material_colored = 0x7f060093
com.seres.usb.upgrade:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f060194
com.seres.usb.upgrade:drawable/abc_dialog_material_background = 0x7f07003c
com.seres.usb.upgrade:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f060193
com.seres.usb.upgrade:attr/materialThemeOverlay = 0x7f0302fb
com.seres.usb.upgrade:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060190
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar = 0x7f100414
com.seres.usb.upgrade:dimen/m3_comp_circular_progress_indicator_active_indicator_width = 0x7f060104
com.seres.usb.upgrade:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601d6
com.seres.usb.upgrade:color/material_slider_halo_color = 0x7f050290
com.seres.usb.upgrade:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f06018d
com.seres.usb.upgrade:style/Widget.AppCompat.PopupMenu = 0x7f10031a
com.seres.usb.upgrade:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f06018c
com.seres.usb.upgrade:dimen/design_bottom_navigation_active_item_min_width = 0x7f060060
com.seres.usb.upgrade:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f050097
com.seres.usb.upgrade:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f060189
com.seres.usb.upgrade:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060181
com.seres.usb.upgrade:id/triangle = 0x7f0801e1
com.seres.usb.upgrade:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f06017f
com.seres.usb.upgrade:style/Widget.MaterialComponents.TimePicker.Button = 0x7f10044e
com.seres.usb.upgrade:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601e7
com.seres.usb.upgrade:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06017c
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500e7
com.seres.usb.upgrade:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602d6
com.seres.usb.upgrade:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017b
com.seres.usb.upgrade:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700af
com.seres.usb.upgrade:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f060178
com.seres.usb.upgrade:color/material_personalized_color_text_primary_inverse = 0x7f050286
com.seres.usb.upgrade:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060174
com.seres.usb.upgrade:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f06016f
com.seres.usb.upgrade:attr/layout_constraintLeft_creator = 0x7f03028c
com.seres.usb.upgrade:dimen/m3_comp_search_bar_container_height = 0x7f06016e
com.seres.usb.upgrade:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016b
com.seres.usb.upgrade:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016a
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011f
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f050178
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700b8
com.seres.usb.upgrade:style/Widget.AppCompat.Button.Borderless = 0x7f1002f1
com.seres.usb.upgrade:style/Widget.AppCompat.ActionBar.TabText = 0x7f1002e8
com.seres.usb.upgrade:id/antiClockwise = 0x7f08004e
com.seres.usb.upgrade:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070062
com.seres.usb.upgrade:string/call_notification_answer_video_action = 0x7f0f0025
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0075
com.seres.usb.upgrade:dimen/m3_comp_search_view_container_elevation = 0x7f060171
com.seres.usb.upgrade:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f060169
com.seres.usb.upgrade:string/call_notification_decline_action = 0x7f0f0026
com.seres.usb.upgrade:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f060168
com.seres.usb.upgrade:color/m3_dynamic_highlighted_text = 0x7f050083
com.seres.usb.upgrade:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060165
com.seres.usb.upgrade:color/material_personalized_color_on_tertiary = 0x7f05026c
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f060161
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f060160
com.seres.usb.upgrade:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f10034e
com.seres.usb.upgrade:drawable/abc_btn_default_mtrl_shape = 0x7f070031
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015f
com.seres.usb.upgrade:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f06028e
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015e
com.seres.usb.upgrade:macro/m3_comp_snackbar_container_color = 0x7f0c0114
com.seres.usb.upgrade:attr/startIconScaleType = 0x7f0303e4
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015c
com.seres.usb.upgrade:dimen/m3_comp_outlined_text_field_outline_width = 0x7f06015b
com.seres.usb.upgrade:color/design_dark_default_color_on_primary = 0x7f050036
com.seres.usb.upgrade:drawable/$avd_hide_password__2 = 0x7f070002
com.seres.usb.upgrade:attr/flow_horizontalStyle = 0x7f0301ee
com.seres.usb.upgrade:attr/materialTimePickerTitleStyle = 0x7f0302fe
com.seres.usb.upgrade:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f06015a
com.seres.usb.upgrade:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060156
com.seres.usb.upgrade:attr/autoSizeMaxTextSize = 0x7f030040
com.seres.usb.upgrade:dimen/m3_comp_outlined_card_icon_size = 0x7f060154
com.seres.usb.upgrade:style/Animation.AppCompat.Dialog = 0x7f100002
com.seres.usb.upgrade:drawable/ic_call_answer = 0x7f070089
com.seres.usb.upgrade:dimen/m3_comp_outlined_card_container_elevation = 0x7f060152
com.seres.usb.upgrade:attr/collapsingToolbarLayoutLargeSize = 0x7f0300ec
com.seres.usb.upgrade:dimen/m3_comp_outlined_button_outline_width = 0x7f060151
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014d
com.seres.usb.upgrade:color/m3_sys_color_dark_surface = 0x7f050163
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014c
com.seres.usb.upgrade:attr/textInputOutlinedStyle = 0x7f03045e
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f06014b
com.seres.usb.upgrade:string/mtrl_picker_announce_current_selection = 0x7f0f006f
com.seres.usb.upgrade:anim/abc_popup_enter = 0x7f010003
com.seres.usb.upgrade:attr/state_above_anchor = 0x7f0303e7
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060149
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060148
com.seres.usb.upgrade:style/Widget.MaterialComponents.NavigationRailView = 0x7f10042d
com.seres.usb.upgrade:layout/material_timepicker = 0x7f0b003e
com.seres.usb.upgrade:attr/triggerId = 0x7f0304b2
com.seres.usb.upgrade:attr/dividerInsetEnd = 0x7f03017e
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060145
com.seres.usb.upgrade:attr/expandActivityOverflowButtonDrawable = 0x7f0301b6
com.seres.usb.upgrade:id/SHOW_PROGRESS = 0x7f08000a
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601f5
com.seres.usb.upgrade:id/parallax = 0x7f080159
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060143
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_container_width = 0x7f060140
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013e
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013d
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060139
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0017
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015d
com.seres.usb.upgrade:color/material_timepicker_button_stroke = 0x7f050295
com.seres.usb.upgrade:attr/materialClockStyle = 0x7f0302ed
com.seres.usb.upgrade:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060134
com.seres.usb.upgrade:id/tag_accessibility_heading = 0x7f0801b7
com.seres.usb.upgrade:color/abc_tint_spinner = 0x7f050017
com.seres.usb.upgrade:drawable/notification_tile_bg = 0x7f0700e3
com.seres.usb.upgrade:dimen/m3_comp_input_chip_container_elevation = 0x7f060131
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1002c8
com.seres.usb.upgrade:id/material_clock_period_pm_button = 0x7f080101
com.seres.usb.upgrade:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f060130
com.seres.usb.upgrade:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06012e
com.seres.usb.upgrade:layout/material_timepicker_textinput_display = 0x7f0b0040
com.seres.usb.upgrade:drawable/ic_usb = 0x7f07009c
com.seres.usb.upgrade:id/row_index_key = 0x7f080176
com.seres.usb.upgrade:dimen/m3_comp_filter_chip_container_height = 0x7f06012c
com.seres.usb.upgrade:attr/colorPrimaryContainer = 0x7f030113
com.seres.usb.upgrade:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f060233
com.seres.usb.upgrade:color/m3_textfield_indicator_text_color = 0x7f0501ee
com.seres.usb.upgrade:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012b
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c4
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060113
com.seres.usb.upgrade:dimen/m3_comp_filled_card_icon_size = 0x7f060129
com.seres.usb.upgrade:attr/navigationViewStyle = 0x7f03034d
com.seres.usb.upgrade:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060128
com.seres.usb.upgrade:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060126
com.seres.usb.upgrade:dimen/m3_comp_filled_button_container_elevation = 0x7f060123
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_small_container_height = 0x7f060120
com.seres.usb.upgrade:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060249
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06011f
com.seres.usb.upgrade:dimen/mtrl_progress_track_thickness = 0x7f0602da
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_icon_size = 0x7f06011b
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060119
com.seres.usb.upgrade:dimen/m3_side_sheet_modal_elevation = 0x7f0601de
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060114
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060112
com.seres.usb.upgrade:id/action_bar_container = 0x7f080035
com.seres.usb.upgrade:dimen/mtrl_textinput_end_icon_margin_start = 0x7f0602fa
com.seres.usb.upgrade:dimen/hint_alpha_material_dark = 0x7f060096
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f060110
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001f
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06010e
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010d
com.seres.usb.upgrade:style/TextAppearance.Material3.BodySmall = 0x7f1001e7
com.seres.usb.upgrade:dimen/m3_comp_elevated_card_icon_size = 0x7f06010c
com.seres.usb.upgrade:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f06010a
com.seres.usb.upgrade:attr/flow_verticalAlign = 0x7f0301f5
com.seres.usb.upgrade:dimen/m3_comp_elevated_button_container_elevation = 0x7f060109
com.seres.usb.upgrade:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005b
com.seres.usb.upgrade:dimen/m3_comp_divider_thickness = 0x7f060108
com.seres.usb.upgrade:attr/materialCalendarHeaderTitle = 0x7f0302e1
com.seres.usb.upgrade:drawable/abc_edit_text_material = 0x7f07003d
com.seres.usb.upgrade:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060106
com.seres.usb.upgrade:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0149
com.seres.usb.upgrade:dimen/mtrl_calendar_header_text_padding = 0x7f06027d
com.seres.usb.upgrade:id/add = 0x7f080046
com.seres.usb.upgrade:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060105
com.seres.usb.upgrade:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060102
com.seres.usb.upgrade:color/material_on_primary_disabled = 0x7f050253
com.seres.usb.upgrade:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0600fe
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f100161
com.seres.usb.upgrade:color/design_dark_default_color_error = 0x7f050033
com.seres.usb.upgrade:attr/colorControlActivated = 0x7f0300f6
com.seres.usb.upgrade:drawable/abc_action_bar_item_background_material = 0x7f07002a
com.seres.usb.upgrade:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600fc
com.seres.usb.upgrade:dimen/m3_comp_assist_chip_container_height = 0x7f0600fa
com.seres.usb.upgrade:id/title = 0x7f0801d3
com.seres.usb.upgrade:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602ea
com.seres.usb.upgrade:color/m3_sys_color_light_surface_container_highest = 0x7f0501d1
com.seres.usb.upgrade:dimen/mtrl_btn_letter_spacing = 0x7f06025c
com.seres.usb.upgrade:dimen/m3_btn_text_btn_padding_right = 0x7f0600e1
com.seres.usb.upgrade:attr/lastBaselineToBottomHeight = 0x7f030267
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060144
com.seres.usb.upgrade:attr/textBackground = 0x7f03044f
com.seres.usb.upgrade:dimen/m3_chip_dragged_translation_z = 0x7f0600f6
com.seres.usb.upgrade:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060158
com.seres.usb.upgrade:integer/mtrl_view_invisible = 0x7f090040
com.seres.usb.upgrade:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f3
com.seres.usb.upgrade:dimen/m3_card_elevated_elevation = 0x7f0600e8
com.seres.usb.upgrade:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600df
com.seres.usb.upgrade:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f110032
com.seres.usb.upgrade:color/mtrl_calendar_selected_range = 0x7f0502a2
com.seres.usb.upgrade:id/accessibility_custom_action_29 = 0x7f080026
com.seres.usb.upgrade:drawable/abc_switch_thumb_material = 0x7f07006a
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f100426
com.seres.usb.upgrade:dimen/m3_btn_stroke_size = 0x7f0600dd
com.seres.usb.upgrade:attr/collapseContentDescription = 0x7f0300e6
com.seres.usb.upgrade:attr/rangeFillColor = 0x7f03038d
com.seres.usb.upgrade:id/action_image = 0x7f08003d
com.seres.usb.upgrade:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.seres.usb.upgrade:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070060
com.seres.usb.upgrade:layout/design_navigation_menu = 0x7f0b0029
com.seres.usb.upgrade:dimen/m3_btn_padding_left = 0x7f0600da
com.seres.usb.upgrade:dimen/m3_btn_icon_only_default_size = 0x7f0600d4
com.seres.usb.upgrade:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1002b6
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.Dialog = 0x7f100279
com.seres.usb.upgrade:id/pooling_container_listener_holder_tag = 0x7f080164
com.seres.usb.upgrade:drawable/abc_spinner_mtrl_am_alpha = 0x7f070066
com.seres.usb.upgrade:dimen/mtrl_calendar_text_input_padding_top = 0x7f06028c
com.seres.usb.upgrade:dimen/m3_comp_badge_size = 0x7f060100
com.seres.usb.upgrade:dimen/m3_btn_icon_only_default_padding = 0x7f0600d3
com.seres.usb.upgrade:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d2
com.seres.usb.upgrade:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600c8
com.seres.usb.upgrade:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d1
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500ad
com.seres.usb.upgrade:dimen/m3_btn_elevation = 0x7f0600d0
com.seres.usb.upgrade:dimen/m3_btn_elevated_btn_elevation = 0x7f0600cf
com.seres.usb.upgrade:dimen/m3_btn_disabled_translation_z = 0x7f0600ce
com.seres.usb.upgrade:dimen/m3_btn_disabled_elevation = 0x7f0600cd
com.seres.usb.upgrade:styleable/ViewPager2 = 0x7f110095
com.seres.usb.upgrade:drawable/ic_call_answer_low = 0x7f07008a
com.seres.usb.upgrade:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cc
com.seres.usb.upgrade:dimen/m3_bottomappbar_height = 0x7f0600c9
com.seres.usb.upgrade:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c7
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1000ea
com.seres.usb.upgrade:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c6
com.seres.usb.upgrade:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c4
com.seres.usb.upgrade:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f6
com.seres.usb.upgrade:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600bf
com.seres.usb.upgrade:attr/itemShapeFillColor = 0x7f030251
com.seres.usb.upgrade:attr/itemStrokeColor = 0x7f030257
com.seres.usb.upgrade:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f060150
com.seres.usb.upgrade:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bb
com.seres.usb.upgrade:dimen/m3_badge_with_text_size = 0x7f0600b9
com.seres.usb.upgrade:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b7
com.seres.usb.upgrade:attr/dividerHorizontal = 0x7f03017d
com.seres.usb.upgrade:attr/layout_anchorGravity = 0x7f03026e
com.seres.usb.upgrade:color/m3_ref_palette_tertiary60 = 0x7f05013d
com.seres.usb.upgrade:dimen/m3_badge_vertical_offset = 0x7f0600b6
com.seres.usb.upgrade:dimen/m3_badge_size = 0x7f0600b5
com.seres.usb.upgrade:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f10005c
com.seres.usb.upgrade:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b1
com.seres.usb.upgrade:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b0
com.seres.usb.upgrade:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600ad
com.seres.usb.upgrade:attr/chipStandaloneStyle = 0x7f0300cb
com.seres.usb.upgrade:dimen/m3_searchview_divider_size = 0x7f0601da
com.seres.usb.upgrade:dimen/m3_appbar_size_medium = 0x7f0600ab
com.seres.usb.upgrade:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f060197
com.seres.usb.upgrade:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600a8
com.seres.usb.upgrade:id/grouping = 0x7f0800d3
com.seres.usb.upgrade:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a6
com.seres.usb.upgrade:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a5
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1002c2
com.seres.usb.upgrade:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a4
com.seres.usb.upgrade:dimen/mtrl_btn_disabled_elevation = 0x7f060254
com.seres.usb.upgrade:attr/buttonCompat = 0x7f03008f
com.seres.usb.upgrade:dimen/m3_alert_dialog_icon_size = 0x7f0600a2
com.seres.usb.upgrade:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f100231
com.seres.usb.upgrade:dimen/m3_alert_dialog_elevation = 0x7f0600a0
com.seres.usb.upgrade:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
com.seres.usb.upgrade:dimen/m3_alert_dialog_action_top_padding = 0x7f06009e
com.seres.usb.upgrade:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009d
com.seres.usb.upgrade:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009c
com.seres.usb.upgrade:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009a
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1002e1
com.seres.usb.upgrade:dimen/hint_pressed_alpha_material_dark = 0x7f060098
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Year = 0x7f1003a4
com.seres.usb.upgrade:string/m3_ref_typeface_brand_medium = 0x7f0f0038
com.seres.usb.upgrade:attr/badgeRadius = 0x7f030054
com.seres.usb.upgrade:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.seres.usb.upgrade:dimen/hint_alpha_material_light = 0x7f060097
com.seres.usb.upgrade:style/Theme.AppCompat.Light.NoActionBar = 0x7f100220
com.seres.usb.upgrade:dimen/highlight_alpha_material_light = 0x7f060095
com.seres.usb.upgrade:dimen/highlight_alpha_material_dark = 0x7f060094
com.seres.usb.upgrade:dimen/m3_card_hovered_z = 0x7f0600eb
com.seres.usb.upgrade:dimen/disabled_alpha_material_light = 0x7f06008f
com.seres.usb.upgrade:dimen/design_textinput_caption_translate_y = 0x7f06008d
com.seres.usb.upgrade:dimen/fastscroll_default_thickness = 0x7f060090
com.seres.usb.upgrade:attr/values = 0x7f0304ba
com.seres.usb.upgrade:dimen/design_tab_text_size = 0x7f06008b
com.seres.usb.upgrade:dimen/design_snackbar_padding_vertical_2lines = 0x7f060087
com.seres.usb.upgrade:dimen/design_snackbar_action_inline_max_width = 0x7f06007e
com.seres.usb.upgrade:id/view_tree_lifecycle_owner = 0x7f0801f3
com.seres.usb.upgrade:dimen/design_navigation_item_vertical_padding = 0x7f06007a
com.seres.usb.upgrade:styleable/OnClick = 0x7f11006d
com.seres.usb.upgrade:attr/itemMinHeight = 0x7f03024a
com.seres.usb.upgrade:dimen/design_navigation_icon_size = 0x7f060077
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500a9
com.seres.usb.upgrade:dimen/design_navigation_elevation = 0x7f060075
com.seres.usb.upgrade:id/SHOW_ALL = 0x7f080008
com.seres.usb.upgrade:dimen/design_fab_size_mini = 0x7f060071
com.seres.usb.upgrade:attr/fabAlignmentModeEndMargin = 0x7f0301ca
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f06020a
com.seres.usb.upgrade:style/Widget.Material3.SearchView = 0x7f1003bf
com.seres.usb.upgrade:attr/icon = 0x7f030224
com.seres.usb.upgrade:dimen/design_fab_image_size = 0x7f060070
com.seres.usb.upgrade:style/Widget.MaterialComponents.FloatingActionButton = 0x7f100410
com.seres.usb.upgrade:dimen/design_fab_elevation = 0x7f06006f
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Button = 0x7f100197
com.seres.usb.upgrade:dimen/design_fab_border_width = 0x7f06006e
com.seres.usb.upgrade:id/mtrl_calendar_main_pane = 0x7f08011e
com.seres.usb.upgrade:dimen/design_bottom_sheet_elevation = 0x7f06006b
com.seres.usb.upgrade:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
com.seres.usb.upgrade:attr/itemActiveIndicatorStyle = 0x7f030241
com.seres.usb.upgrade:dimen/design_bottom_navigation_shadow_height = 0x7f060069
com.seres.usb.upgrade:style/Base.Widget.AppCompat.PopupWindow = 0x7f1000ed
com.seres.usb.upgrade:attr/motionEffect_translationY = 0x7f03033c
com.seres.usb.upgrade:dimen/design_bottom_navigation_label_padding = 0x7f060067
com.seres.usb.upgrade:attr/carousel_infinite = 0x7f0300a6
com.seres.usb.upgrade:dimen/design_bottom_navigation_item_min_width = 0x7f060066
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant30 = 0x7f050113
com.seres.usb.upgrade:drawable/abc_list_focused_holo = 0x7f07004f
com.seres.usb.upgrade:attr/materialAlertDialogTitleIconStyle = 0x7f0302d3
com.seres.usb.upgrade:dimen/design_bottom_navigation_item_max_width = 0x7f060065
com.seres.usb.upgrade:dimen/design_bottom_navigation_icon_size = 0x7f060064
com.seres.usb.upgrade:drawable/ic_m3_chip_checked_circle = 0x7f070095
com.seres.usb.upgrade:id/legacy = 0x7f0800f0
com.seres.usb.upgrade:dimen/design_bottom_navigation_height = 0x7f060063
com.seres.usb.upgrade:attr/colorError = 0x7f0300f9
com.seres.usb.upgrade:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f06028a
com.seres.usb.upgrade:dimen/design_bottom_navigation_active_text_size = 0x7f060061
com.seres.usb.upgrade:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1003e8
com.seres.usb.upgrade:dimen/design_fab_translation_z_pressed = 0x7f060074
com.seres.usb.upgrade:id/accessibility_custom_action_0 = 0x7f080010
com.seres.usb.upgrade:dimen/design_bottom_navigation_active_item_max_width = 0x7f06005f
com.seres.usb.upgrade:dimen/compat_control_corner_material = 0x7f06005a
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500dc
com.seres.usb.upgrade:dimen/compat_button_padding_horizontal_material = 0x7f060058
com.seres.usb.upgrade:color/mtrl_textinput_filled_box_default_background_color = 0x7f0502cd
com.seres.usb.upgrade:dimen/compat_button_inset_vertical_material = 0x7f060057
com.seres.usb.upgrade:dimen/cardview_default_radius = 0x7f060054
com.seres.usb.upgrade:color/material_dynamic_neutral_variant80 = 0x7f05021a
com.seres.usb.upgrade:dimen/abc_text_size_title_material = 0x7f06004f
com.seres.usb.upgrade:integer/m3_sys_motion_duration_short1 = 0x7f09001b
com.seres.usb.upgrade:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.seres.usb.upgrade:dimen/mtrl_calendar_action_padding = 0x7f06026c
com.seres.usb.upgrade:style/Widget.MaterialComponents.CardView = 0x7f1003ff
com.seres.usb.upgrade:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.seres.usb.upgrade:dimen/abc_text_size_medium_material = 0x7f060049
com.seres.usb.upgrade:id/view_offset_helper = 0x7f0801f1
com.seres.usb.upgrade:dimen/abc_text_size_large_material = 0x7f060048
com.seres.usb.upgrade:style/Widget.Material3.CheckedTextView = 0x7f100362
com.seres.usb.upgrade:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1000be
com.seres.usb.upgrade:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300ed
com.seres.usb.upgrade:dimen/abc_text_size_display_4_material = 0x7f060046
com.seres.usb.upgrade:integer/m3_sys_shape_corner_large_corner_family = 0x7f090023
com.seres.usb.upgrade:dimen/abc_text_size_display_3_material = 0x7f060045
com.seres.usb.upgrade:dimen/abc_text_size_display_2_material = 0x7f060044
com.seres.usb.upgrade:color/material_personalized_color_control_normal = 0x7f05025f
com.seres.usb.upgrade:dimen/abc_text_size_button_material = 0x7f060041
com.seres.usb.upgrade:dimen/abc_text_size_body_2_material = 0x7f060040
com.seres.usb.upgrade:attr/textAppearanceSubtitle2 = 0x7f03044b
com.seres.usb.upgrade:dimen/abc_text_size_body_1_material = 0x7f06003f
com.seres.usb.upgrade:dimen/abc_switch_padding = 0x7f06003e
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f10037e
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c006b
com.seres.usb.upgrade:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f060215
com.seres.usb.upgrade:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f060179
com.seres.usb.upgrade:dimen/abc_star_small = 0x7f06003d
com.seres.usb.upgrade:attr/actionTextColorAlpha = 0x7f030024
com.seres.usb.upgrade:dimen/abc_star_medium = 0x7f06003c
com.seres.usb.upgrade:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.seres.usb.upgrade:dimen/abc_star_big = 0x7f06003b
com.seres.usb.upgrade:style/Widget.MaterialComponents.PopupMenu = 0x7f100433
com.seres.usb.upgrade:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f10007c
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001e
com.seres.usb.upgrade:dimen/abc_search_view_preferred_width = 0x7f060037
com.seres.usb.upgrade:string/password_toggle_content_description = 0x7f0f009b
com.seres.usb.upgrade:dimen/abc_panel_menu_list_width = 0x7f060034
com.seres.usb.upgrade:dimen/m3_card_disabled_z = 0x7f0600e4
com.seres.usb.upgrade:style/Widget.AppCompat.ActionMode = 0x7f1002ed
com.seres.usb.upgrade:dimen/abc_list_item_height_material = 0x7f060031
com.seres.usb.upgrade:dimen/abc_list_item_height_large_material = 0x7f060030
com.seres.usb.upgrade:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014b
com.seres.usb.upgrade:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.seres.usb.upgrade:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.seres.usb.upgrade:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f1
com.seres.usb.upgrade:layout/material_clockface_view = 0x7f0b0039
com.seres.usb.upgrade:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.seres.usb.upgrade:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.seres.usb.upgrade:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.seres.usb.upgrade:attr/layout_constraintEnd_toStartOf = 0x7f030280
com.seres.usb.upgrade:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f060186
com.seres.usb.upgrade:drawable/abc_textfield_search_material = 0x7f070076
com.seres.usb.upgrade:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.seres.usb.upgrade:attr/itemIconTint = 0x7f030248
com.seres.usb.upgrade:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600ae
com.seres.usb.upgrade:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.seres.usb.upgrade:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.seres.usb.upgrade:style/TextAppearance.Material3.HeadlineMedium = 0x7f1001ec
com.seres.usb.upgrade:attr/listChoiceBackgroundIndicator = 0x7f0302b9
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f06011a
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f100380
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_tertiary = 0x7f05018a
com.seres.usb.upgrade:dimen/abc_dialog_padding_top_material = 0x7f060025
com.seres.usb.upgrade:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1002a5
com.seres.usb.upgrade:integer/m3_sys_motion_duration_long3 = 0x7f090015
com.seres.usb.upgrade:dimen/abc_dialog_padding_material = 0x7f060024
com.seres.usb.upgrade:layout/abc_list_menu_item_radio = 0x7f0b0011
com.seres.usb.upgrade:attr/cursorColor = 0x7f03015e
com.seres.usb.upgrade:dimen/abc_dialog_min_width_minor = 0x7f060023
com.seres.usb.upgrade:dimen/abc_dialog_min_width_major = 0x7f060022
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f100191
com.seres.usb.upgrade:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.seres.usb.upgrade:drawable/abc_ratingbar_small_material = 0x7f07005d
com.seres.usb.upgrade:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f06029e
com.seres.usb.upgrade:styleable/SwitchCompat = 0x7f110085
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f0601ff
com.seres.usb.upgrade:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.seres.usb.upgrade:style/Widget.Material3.CircularProgressIndicator = 0x7f10036e
com.seres.usb.upgrade:dimen/abc_control_padding_material = 0x7f06001a
com.seres.usb.upgrade:dimen/abc_config_prefDialogWidth = 0x7f060017
com.seres.usb.upgrade:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1000ef
com.seres.usb.upgrade:id/chronometer = 0x7f080078
com.seres.usb.upgrade:dimen/abc_button_padding_vertical_material = 0x7f060015
com.seres.usb.upgrade:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.seres.usb.upgrade:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.seres.usb.upgrade:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.seres.usb.upgrade:id/SYM = 0x7f08000b
com.seres.usb.upgrade:color/design_fab_stroke_top_inner_color = 0x7f050052
com.seres.usb.upgrade:drawable/mtrl_tabs_default_indicator = 0x7f0700d7
com.seres.usb.upgrade:drawable/abc_list_longpressed_holo = 0x7f070050
com.seres.usb.upgrade:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.seres.usb.upgrade:drawable/ic_call_decline = 0x7f07008d
com.seres.usb.upgrade:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0144
com.seres.usb.upgrade:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.seres.usb.upgrade:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.seres.usb.upgrade:dimen/abc_text_size_display_1_material = 0x7f060043
com.seres.usb.upgrade:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070036
com.seres.usb.upgrade:string/m3_sys_motion_easing_standard_decelerate = 0x7f0f0046
com.seres.usb.upgrade:color/material_dynamic_neutral_variant60 = 0x7f050218
com.seres.usb.upgrade:dimen/mtrl_calendar_day_height = 0x7f060270
com.seres.usb.upgrade:id/material_timepicker_cancel_button = 0x7f080109
com.seres.usb.upgrade:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.seres.usb.upgrade:dimen/abc_action_bar_elevation_material = 0x7f060005
com.seres.usb.upgrade:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.seres.usb.upgrade:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060167
com.seres.usb.upgrade:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.seres.usb.upgrade:color/white = 0x7f0502e8
com.seres.usb.upgrade:color/tooltip_background_light = 0x7f0502e7
com.seres.usb.upgrade:dimen/mtrl_btn_corner_radius = 0x7f060252
com.seres.usb.upgrade:color/tooltip_background_dark = 0x7f0502e6
com.seres.usb.upgrade:color/switch_thumb_normal_material_light = 0x7f0502e5
com.seres.usb.upgrade:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
com.seres.usb.upgrade:dimen/material_clock_display_height = 0x7f06021b
com.seres.usb.upgrade:attr/tintMode = 0x7f030482
com.seres.usb.upgrade:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601b8
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601ca
com.seres.usb.upgrade:color/switch_thumb_material_light = 0x7f0502e3
com.seres.usb.upgrade:color/switch_thumb_material_dark = 0x7f0502e2
com.seres.usb.upgrade:integer/m3_card_anim_delay_ms = 0x7f09000c
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f060203
com.seres.usb.upgrade:color/switch_thumb_disabled_material_dark = 0x7f0502e0
com.seres.usb.upgrade:color/secondary_text_disabled_material_light = 0x7f0502df
com.seres.usb.upgrade:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f06019f
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0172
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f060205
com.seres.usb.upgrade:color/secondary_text_disabled_material_dark = 0x7f0502de
com.seres.usb.upgrade:color/secondary_text_default_material_light = 0x7f0502dd
com.seres.usb.upgrade:attr/deriveConstraintsFrom = 0x7f030176
com.seres.usb.upgrade:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f060188
com.seres.usb.upgrade:color/secondary_text_default_material_dark = 0x7f0502dc
com.seres.usb.upgrade:attr/textureWidth = 0x7f030469
com.seres.usb.upgrade:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c0
com.seres.usb.upgrade:color/ripple_material_light = 0x7f0502db
com.seres.usb.upgrade:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f10040e
com.seres.usb.upgrade:style/Theme.Design.NoActionBar = 0x7f100227
com.seres.usb.upgrade:color/ripple_material_dark = 0x7f0502da
com.seres.usb.upgrade:color/primary_text_default_material_light = 0x7f0502d7
com.seres.usb.upgrade:color/primary_material_light = 0x7f0502d5
com.seres.usb.upgrade:color/primary_material_dark = 0x7f0502d4
com.seres.usb.upgrade:color/mtrl_textinput_disabled_color = 0x7f0502cc
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1001d9
com.seres.usb.upgrade:color/mtrl_textinput_default_box_stroke_color = 0x7f0502cb
com.seres.usb.upgrade:color/mtrl_text_btn_text_color_selector = 0x7f0502ca
com.seres.usb.upgrade:color/mtrl_tabs_icon_color_selector_colored = 0x7f0502c7
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1002ae
com.seres.usb.upgrade:id/showCustom = 0x7f080191
com.seres.usb.upgrade:attr/chipSurfaceColor = 0x7f0300d0
com.seres.usb.upgrade:color/mtrl_tabs_icon_color_selector = 0x7f0502c6
com.seres.usb.upgrade:color/m3_sys_color_light_on_primary_container = 0x7f0501c0
com.seres.usb.upgrade:color/mtrl_switch_thumb_tint = 0x7f0502c2
com.seres.usb.upgrade:styleable/MaterialTextView = 0x7f11005b
com.seres.usb.upgrade:attr/boxStrokeErrorColor = 0x7f030086
com.seres.usb.upgrade:color/mtrl_switch_thumb_icon_tint = 0x7f0502c1
com.seres.usb.upgrade:attr/textLocale = 0x7f030460
com.seres.usb.upgrade:attr/fontWeight = 0x7f030205
com.seres.usb.upgrade:color/mtrl_scrim_color = 0x7f0502c0
com.seres.usb.upgrade:attr/shapeAppearance = 0x7f0303af
com.seres.usb.upgrade:color/mtrl_popupmenu_overlay_color = 0x7f0502bf
com.seres.usb.upgrade:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.seres.usb.upgrade:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602d3
com.seres.usb.upgrade:color/m3_ref_palette_neutral50 = 0x7f050102
com.seres.usb.upgrade:color/mtrl_outlined_icon_tint = 0x7f0502bd
com.seres.usb.upgrade:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0502bb
com.seres.usb.upgrade:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0106
com.seres.usb.upgrade:dimen/mtrl_calendar_action_height = 0x7f06026b
com.seres.usb.upgrade:color/m3_ref_palette_tertiary70 = 0x7f05013e
com.seres.usb.upgrade:color/mtrl_navigation_bar_item_tint = 0x7f0502b6
com.seres.usb.upgrade:string/mtrl_switch_thumb_path_pressed = 0x7f0f0095
com.seres.usb.upgrade:color/mtrl_indicator_text_color = 0x7f0502b3
com.seres.usb.upgrade:color/mtrl_filled_background_color = 0x7f0502b0
com.seres.usb.upgrade:integer/mtrl_card_anim_delay_ms = 0x7f090033
com.seres.usb.upgrade:id/accessibility_custom_action_14 = 0x7f080016
com.seres.usb.upgrade:drawable/m3_bottom_sheet_drag_handle = 0x7f0700a0
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f06020e
com.seres.usb.upgrade:dimen/m3_bottom_sheet_elevation = 0x7f0600c3
com.seres.usb.upgrade:id/material_label = 0x7f080105
com.seres.usb.upgrade:color/mtrl_fab_bg_color_selector = 0x7f0502ad
com.seres.usb.upgrade:color/mtrl_choice_chip_text_color = 0x7f0502ab
com.seres.usb.upgrade:color/mtrl_choice_chip_ripple_color = 0x7f0502aa
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f100166
com.seres.usb.upgrade:id/accessibility_custom_action_28 = 0x7f080025
com.seres.usb.upgrade:dimen/appcompat_dialog_background_inset = 0x7f060051
com.seres.usb.upgrade:color/mtrl_choice_chip_background_color = 0x7f0502a9
com.seres.usb.upgrade:attr/topInsetScrimEnabled = 0x7f03049c
com.seres.usb.upgrade:color/mtrl_chip_text_color = 0x7f0502a8
com.seres.usb.upgrade:color/mtrl_chip_close_icon_tint = 0x7f0502a6
com.seres.usb.upgrade:color/mtrl_card_view_ripple = 0x7f0502a4
com.seres.usb.upgrade:id/useLogo = 0x7f0801ef
com.seres.usb.upgrade:id/edit_query = 0x7f0800b2
com.seres.usb.upgrade:color/mtrl_calendar_item_stroke_color = 0x7f0502a1
com.seres.usb.upgrade:color/mtrl_btn_text_color_selector = 0x7f05029f
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_text_size = 0x7f0602cc
com.seres.usb.upgrade:color/mtrl_btn_text_color_disabled = 0x7f05029e
com.seres.usb.upgrade:attr/firstBaselineToTopHeight = 0x7f0301d7
com.seres.usb.upgrade:color/mtrl_btn_text_btn_bg_color_selector = 0x7f05029c
com.seres.usb.upgrade:color/mtrl_btn_stroke_color_selector = 0x7f05029b
com.seres.usb.upgrade:string/call_notification_screening_text = 0x7f0f002a
com.seres.usb.upgrade:attr/marginRightSystemWindowInsets = 0x7f0302ce
com.seres.usb.upgrade:attr/motionDurationLong4 = 0x7f030321
com.seres.usb.upgrade:color/material_timepicker_clock_text_color = 0x7f050296
com.seres.usb.upgrade:id/ghost_view = 0x7f0800cd
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500d7
com.seres.usb.upgrade:color/material_timepicker_button_background = 0x7f050294
com.seres.usb.upgrade:color/material_slider_inactive_track_color = 0x7f050292
com.seres.usb.upgrade:integer/m3_sys_motion_duration_long2 = 0x7f090014
com.seres.usb.upgrade:attr/textColorSearchUrl = 0x7f030455
com.seres.usb.upgrade:color/material_slider_active_track_color = 0x7f05028f
com.seres.usb.upgrade:attr/carousel_forwardTransition = 0x7f0300a5
com.seres.usb.upgrade:color/material_slider_active_tick_marks_color = 0x7f05028e
com.seres.usb.upgrade:dimen/mtrl_slider_label_square_side = 0x7f0602e1
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant95 = 0x7f05011a
com.seres.usb.upgrade:color/material_personalized_primary_inverse_text_disable_only = 0x7f05028c
com.seres.usb.upgrade:color/material_personalized_hint_foreground_inverse = 0x7f05028b
com.seres.usb.upgrade:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f050289
com.seres.usb.upgrade:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700ae
com.seres.usb.upgrade:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f050288
com.seres.usb.upgrade:color/material_personalized_color_surface_dim = 0x7f050280
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601c7
com.seres.usb.upgrade:color/material_personalized_color_surface_container_high = 0x7f05027c
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d4
com.seres.usb.upgrade:color/material_personalized_color_surface_container = 0x7f05027b
com.seres.usb.upgrade:styleable/GradientColorItem = 0x7f11003c
com.seres.usb.upgrade:attr/thumbIcon = 0x7f03046e
com.seres.usb.upgrade:color/m3_bottom_sheet_drag_handle_color = 0x7f050063
com.seres.usb.upgrade:color/material_personalized_color_surface_bright = 0x7f05027a
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f10018d
com.seres.usb.upgrade:color/material_personalized_color_surface = 0x7f050279
com.seres.usb.upgrade:layout/mtrl_alert_dialog_actions = 0x7f0b0042
com.seres.usb.upgrade:attr/emojiCompatEnabled = 0x7f03019f
com.seres.usb.upgrade:color/material_personalized_color_secondary_text_inverse = 0x7f050278
com.seres.usb.upgrade:styleable/LinearLayoutCompat = 0x7f110048
com.seres.usb.upgrade:color/material_personalized_color_secondary_text = 0x7f050277
com.seres.usb.upgrade:color/material_personalized_color_secondary = 0x7f050275
com.seres.usb.upgrade:drawable/test_level_drawable = 0x7f0700e7
com.seres.usb.upgrade:color/material_personalized_color_primary_text = 0x7f050273
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f10025c
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f0601f9
com.seres.usb.upgrade:color/material_personalized_color_primary_inverse = 0x7f050272
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004d
com.seres.usb.upgrade:color/material_personalized_color_primary = 0x7f050270
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060142
com.seres.usb.upgrade:attr/errorTextAppearance = 0x7f0301b4
com.seres.usb.upgrade:attr/dialogPreferredPadding = 0x7f030178
com.seres.usb.upgrade:color/material_personalized_color_on_tertiary_container = 0x7f05026d
com.seres.usb.upgrade:string/abc_menu_delete_shortcut_label = 0x7f0f000a
com.seres.usb.upgrade:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f06029c
com.seres.usb.upgrade:string/abc_searchview_description_search = 0x7f0f0015
com.seres.usb.upgrade:color/material_personalized_color_on_surface_variant = 0x7f05026b
com.seres.usb.upgrade:id/material_clock_hand = 0x7f0800fe
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070024
com.seres.usb.upgrade:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a6
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0051
com.seres.usb.upgrade:color/material_personalized_color_on_surface = 0x7f050269
com.seres.usb.upgrade:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.seres.usb.upgrade:attr/drawerLayoutCornerSize = 0x7f030191
com.seres.usb.upgrade:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060103
com.seres.usb.upgrade:color/material_personalized_color_on_secondary = 0x7f050267
com.seres.usb.upgrade:drawable/notification_bg = 0x7f0700da
com.seres.usb.upgrade:color/material_personalized_color_on_primary_container = 0x7f050266
com.seres.usb.upgrade:attr/defaultDuration = 0x7f03016f
com.seres.usb.upgrade:color/material_personalized_color_on_primary = 0x7f050265
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f1003fc
com.seres.usb.upgrade:style/Base.Widget.AppCompat.EditText = 0x7f1000dc
com.seres.usb.upgrade:color/material_personalized_color_on_background = 0x7f050262
com.seres.usb.upgrade:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f100013
com.seres.usb.upgrade:color/material_personalized_color_control_highlight = 0x7f05025e
com.seres.usb.upgrade:color/material_personalized_color_control_activated = 0x7f05025d
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f100147
com.seres.usb.upgrade:attr/textInputFilledStyle = 0x7f03045a
com.seres.usb.upgrade:color/material_personalized_color_surface_container_lowest = 0x7f05027f
com.seres.usb.upgrade:color/m3_ref_palette_neutral24 = 0x7f0500fe
com.seres.usb.upgrade:color/material_personalized__highlighted_text_inverse = 0x7f05025b
com.seres.usb.upgrade:color/material_on_surface_stroke = 0x7f050259
com.seres.usb.upgrade:color/material_on_surface_disabled = 0x7f050256
com.seres.usb.upgrade:color/material_on_primary_emphasis_high_type = 0x7f050254
com.seres.usb.upgrade:attr/hintAnimationEnabled = 0x7f03021b
com.seres.usb.upgrade:color/material_on_background_disabled = 0x7f050250
com.seres.usb.upgrade:color/material_harmonized_color_on_error_container = 0x7f05024f
com.seres.usb.upgrade:color/material_harmonized_color_on_error = 0x7f05024e
com.seres.usb.upgrade:style/TextAppearance.Material3.LabelLarge = 0x7f1001ee
com.seres.usb.upgrade:color/material_harmonized_color_error = 0x7f05024c
com.seres.usb.upgrade:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1003e1
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f05018d
com.seres.usb.upgrade:color/material_grey_900 = 0x7f05024b
com.seres.usb.upgrade:dimen/m3_comp_switch_track_height = 0x7f060191
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700bc
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c016a
com.seres.usb.upgrade:color/material_personalized_color_outline_variant = 0x7f05026f
com.seres.usb.upgrade:color/material_grey_850 = 0x7f05024a
com.seres.usb.upgrade:color/material_grey_600 = 0x7f050248
com.seres.usb.upgrade:color/material_dynamic_tertiary99 = 0x7f050244
com.seres.usb.upgrade:dimen/design_snackbar_action_text_color_alpha = 0x7f06007f
com.seres.usb.upgrade:attr/colorControlHighlight = 0x7f0300f7
com.seres.usb.upgrade:color/material_dynamic_tertiary70 = 0x7f050240
com.seres.usb.upgrade:color/material_dynamic_tertiary30 = 0x7f05023c
com.seres.usb.upgrade:color/material_on_background_emphasis_high_type = 0x7f050251
com.seres.usb.upgrade:attr/itemTextColor = 0x7f03025d
com.seres.usb.upgrade:color/material_dynamic_tertiary100 = 0x7f05023a
com.seres.usb.upgrade:color/material_dynamic_tertiary10 = 0x7f050239
com.seres.usb.upgrade:color/material_dynamic_tertiary0 = 0x7f050238
com.seres.usb.upgrade:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060166
com.seres.usb.upgrade:string/searchview_clear_text_content_description = 0x7f0f00a2
com.seres.usb.upgrade:color/material_dynamic_secondary95 = 0x7f050236
com.seres.usb.upgrade:anim/abc_slide_in_top = 0x7f010007
com.seres.usb.upgrade:attr/thumbColor = 0x7f03046c
com.seres.usb.upgrade:dimen/mtrl_progress_circular_inset_medium = 0x7f0602cf
com.seres.usb.upgrade:style/Base.V7.Widget.AppCompat.EditText = 0x7f1000c1
com.seres.usb.upgrade:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601c0
com.seres.usb.upgrade:color/material_dynamic_secondary80 = 0x7f050234
com.seres.usb.upgrade:color/material_dynamic_secondary50 = 0x7f050231
com.seres.usb.upgrade:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601a6
com.seres.usb.upgrade:color/material_dynamic_secondary40 = 0x7f050230
com.seres.usb.upgrade:color/material_dynamic_secondary30 = 0x7f05022f
com.seres.usb.upgrade:color/material_dynamic_secondary100 = 0x7f05022d
com.seres.usb.upgrade:integer/mtrl_btn_anim_delay_ms = 0x7f09002e
com.seres.usb.upgrade:id/activity_chooser_view_content = 0x7f080045
com.seres.usb.upgrade:color/material_dynamic_secondary10 = 0x7f05022c
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1001dc
com.seres.usb.upgrade:color/material_dynamic_secondary0 = 0x7f05022b
com.seres.usb.upgrade:styleable/ChipGroup = 0x7f11001e
com.seres.usb.upgrade:id/save_overlay_view = 0x7f080178
com.seres.usb.upgrade:color/accent_material_dark = 0x7f050019
com.seres.usb.upgrade:color/material_dynamic_primary90 = 0x7f050228
com.seres.usb.upgrade:dimen/mtrl_slider_label_padding = 0x7f0602df
com.seres.usb.upgrade:color/material_dynamic_primary70 = 0x7f050226
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f100075
com.seres.usb.upgrade:color/material_dynamic_primary40 = 0x7f050223
com.seres.usb.upgrade:styleable/DrawerArrowToggle = 0x7f11002f
com.seres.usb.upgrade:dimen/notification_action_text_size = 0x7f060305
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f100449
com.seres.usb.upgrade:style/TextAppearance.Material3.LabelMedium = 0x7f1001ef
com.seres.usb.upgrade:color/material_dynamic_neutral_variant95 = 0x7f05021c
com.seres.usb.upgrade:id/dragUp = 0x7f0800ab
com.seres.usb.upgrade:attr/colorSecondaryFixedDim = 0x7f03011d
com.seres.usb.upgrade:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007c
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1001bd
com.seres.usb.upgrade:attr/guidelineUseRtl = 0x7f03020d
com.seres.usb.upgrade:attr/layoutManager = 0x7f03026c
com.seres.usb.upgrade:color/material_dynamic_neutral_variant30 = 0x7f050215
com.seres.usb.upgrade:color/material_dynamic_neutral_variant20 = 0x7f050214
com.seres.usb.upgrade:color/material_dynamic_neutral_variant100 = 0x7f050213
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_bright = 0x7f050164
com.seres.usb.upgrade:color/material_dynamic_neutral_variant10 = 0x7f050212
com.seres.usb.upgrade:attr/wavePeriod = 0x7f0304c7
com.seres.usb.upgrade:color/material_dynamic_neutral95 = 0x7f05020f
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050174
com.seres.usb.upgrade:dimen/m3_btn_dialog_btn_min_width = 0x7f0600cb
com.seres.usb.upgrade:color/material_dynamic_neutral80 = 0x7f05020d
com.seres.usb.upgrade:color/material_dynamic_neutral60 = 0x7f05020b
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.Snackbar = 0x7f10011c
com.seres.usb.upgrade:color/material_dynamic_neutral30 = 0x7f050208
com.seres.usb.upgrade:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.seres.usb.upgrade:color/material_dynamic_neutral0 = 0x7f050204
com.seres.usb.upgrade:color/material_deep_teal_500 = 0x7f050202
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1002ac
com.seres.usb.upgrade:color/material_cursor_color = 0x7f050200
com.seres.usb.upgrade:color/material_blue_grey_950 = 0x7f0501ff
com.seres.usb.upgrade:id/scale = 0x7f08017a
com.seres.usb.upgrade:color/material_blue_grey_900 = 0x7f0501fe
com.seres.usb.upgrade:color/material_blue_grey_800 = 0x7f0501fd
com.seres.usb.upgrade:color/m3_tonal_button_ripple_color_selector = 0x7f0501fc
com.seres.usb.upgrade:id/with_icon = 0x7f0801fb
com.seres.usb.upgrade:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c2
com.seres.usb.upgrade:color/m3_timepicker_time_input_stroke_color = 0x7f0501fb
com.seres.usb.upgrade:attr/materialIconButtonFilledStyle = 0x7f0302f1
com.seres.usb.upgrade:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f0501f9
com.seres.usb.upgrade:color/m3_ref_palette_tertiary10 = 0x7f050137
com.seres.usb.upgrade:color/m3_timepicker_display_background_color = 0x7f0501f6
com.seres.usb.upgrade:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1003df
com.seres.usb.upgrade:drawable/status_background = 0x7f0700e6
com.seres.usb.upgrade:color/m3_timepicker_clock_text_color = 0x7f0501f5
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0021
com.seres.usb.upgrade:integer/app_bar_elevation_anim_duration = 0x7f090002
com.seres.usb.upgrade:color/m3_timepicker_button_text_color = 0x7f0501f4
com.seres.usb.upgrade:id/italic = 0x7f0800e8
com.seres.usb.upgrade:color/m3_timepicker_button_ripple_color = 0x7f0501f3
com.seres.usb.upgrade:attr/motionDurationExtraLong4 = 0x7f03031d
com.seres.usb.upgrade:color/m3_timepicker_button_background_color = 0x7f0501f2
com.seres.usb.upgrade:macro/m3_comp_switch_selected_icon_color = 0x7f0c012a
com.seres.usb.upgrade:color/m3_textfield_input_text_color = 0x7f0501ef
com.seres.usb.upgrade:attr/checkedIcon = 0x7f0300b4
com.seres.usb.upgrade:color/m3_textfield_filled_background_color = 0x7f0501ed
com.seres.usb.upgrade:attr/windowActionBar = 0x7f0304cb
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_outline = 0x7f05017b
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500d8
com.seres.usb.upgrade:color/m3_text_button_background_color_selector = 0x7f0501ea
com.seres.usb.upgrade:color/m3_tabs_text_color_secondary = 0x7f0501e9
com.seres.usb.upgrade:attr/motionEasingLinearInterpolator = 0x7f030331
com.seres.usb.upgrade:dimen/mtrl_badge_with_text_size = 0x7f06024b
com.seres.usb.upgrade:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f10030e
com.seres.usb.upgrade:attr/contentPaddingStart = 0x7f030144
com.seres.usb.upgrade:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1001dd
com.seres.usb.upgrade:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
com.seres.usb.upgrade:color/material_personalized_color_on_error_container = 0x7f050264
com.seres.usb.upgrade:color/m3_tabs_text_color = 0x7f0501e8
com.seres.usb.upgrade:color/material_dynamic_neutral_variant90 = 0x7f05021b
com.seres.usb.upgrade:color/switch_thumb_normal_material_dark = 0x7f0502e4
com.seres.usb.upgrade:color/abc_tint_switch_track = 0x7f050018
com.seres.usb.upgrade:color/m3_tabs_ripple_color_secondary = 0x7f0501e7
com.seres.usb.upgrade:attr/colorPrimary = 0x7f030112
com.seres.usb.upgrade:attr/behavior_fitToContents = 0x7f03006b
com.seres.usb.upgrade:dimen/mtrl_btn_pressed_z = 0x7f060262
com.seres.usb.upgrade:color/m3_tabs_ripple_color = 0x7f0501e6
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1000d5
com.seres.usb.upgrade:attr/trackTint = 0x7f0304aa
com.seres.usb.upgrade:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a3
com.seres.usb.upgrade:color/m3_tabs_icon_color = 0x7f0501e4
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f10020f
com.seres.usb.upgrade:id/startHorizontal = 0x7f0801aa
com.seres.usb.upgrade:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a1
com.seres.usb.upgrade:color/material_on_surface_emphasis_medium = 0x7f050258
com.seres.usb.upgrade:id/design_bottom_sheet = 0x7f080097
com.seres.usb.upgrade:attr/layout_constraintWidth = 0x7f03029b
com.seres.usb.upgrade:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0501dd
com.seres.usb.upgrade:dimen/m3_navigation_item_shape_inset_end = 0x7f0601bc
com.seres.usb.upgrade:color/m3_sys_color_on_tertiary_fixed = 0x7f0501dc
com.seres.usb.upgrade:color/m3_sys_color_on_primary_fixed = 0x7f0501d8
com.seres.usb.upgrade:dimen/mtrl_textinput_start_icon_margin_end = 0x7f0602fc
com.seres.usb.upgrade:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1001f1
com.seres.usb.upgrade:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00fa
com.seres.usb.upgrade:color/m3_sys_color_light_tertiary_container = 0x7f0501d7
com.seres.usb.upgrade:attr/materialSwitchStyle = 0x7f0302fa
com.seres.usb.upgrade:drawable/avd_hide_password = 0x7f070078
com.seres.usb.upgrade:id/month_navigation_next = 0x7f080116
com.seres.usb.upgrade:color/m3_sys_color_light_tertiary = 0x7f0501d6
com.seres.usb.upgrade:color/m3_ref_palette_neutral92 = 0x7f050109
com.seres.usb.upgrade:dimen/design_appbar_elevation = 0x7f06005e
com.seres.usb.upgrade:color/m3_sys_color_light_surface_container_lowest = 0x7f0501d3
com.seres.usb.upgrade:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060241
com.seres.usb.upgrade:dimen/mtrl_btn_z = 0x7f060269
com.seres.usb.upgrade:styleable/MotionHelper = 0x7f110064
com.seres.usb.upgrade:attr/backgroundTint = 0x7f030050
com.seres.usb.upgrade:color/m3_sys_color_light_surface_container_high = 0x7f0501d0
com.seres.usb.upgrade:color/m3_sys_color_light_surface_container = 0x7f0501cf
com.seres.usb.upgrade:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f10043f
com.seres.usb.upgrade:color/m3_sys_color_light_secondary = 0x7f0501cb
com.seres.usb.upgrade:dimen/material_cursor_inset = 0x7f060228
com.seres.usb.upgrade:color/m3_sys_color_light_primary_container = 0x7f0501ca
com.seres.usb.upgrade:attr/fabAnimationMode = 0x7f0301cc
com.seres.usb.upgrade:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.seres.usb.upgrade:layout/abc_tooltip = 0x7f0b001b
com.seres.usb.upgrade:color/m3_sys_color_light_primary = 0x7f0501c9
com.seres.usb.upgrade:id/elastic = 0x7f0800b3
com.seres.usb.upgrade:color/primary_text_disabled_material_light = 0x7f0502d9
com.seres.usb.upgrade:color/m3_sys_color_light_outline_variant = 0x7f0501c8
com.seres.usb.upgrade:color/m3_sys_color_light_outline = 0x7f0501c7
com.seres.usb.upgrade:color/m3_sys_color_light_on_tertiary_container = 0x7f0501c6
com.seres.usb.upgrade:color/m3_sys_color_light_on_tertiary = 0x7f0501c5
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008f
com.seres.usb.upgrade:color/m3_sys_color_light_on_surface_variant = 0x7f0501c4
com.seres.usb.upgrade:color/m3_sys_color_light_on_error = 0x7f0501bd
com.seres.usb.upgrade:color/m3_sys_color_light_inverse_surface = 0x7f0501bb
com.seres.usb.upgrade:color/m3_sys_color_light_error_container = 0x7f0501b8
com.seres.usb.upgrade:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0123
com.seres.usb.upgrade:color/m3_sys_color_light_error = 0x7f0501b7
com.seres.usb.upgrade:color/m3_sys_color_light_background = 0x7f0501b6
com.seres.usb.upgrade:id/mtrl_internal_children_alpha_tag = 0x7f080125
com.seres.usb.upgrade:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501b5
com.seres.usb.upgrade:style/Base.TextAppearance.MaterialComponents.Button = 0x7f100045
com.seres.usb.upgrade:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501b3
com.seres.usb.upgrade:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501b1
com.seres.usb.upgrade:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501b0
com.seres.usb.upgrade:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501af
com.seres.usb.upgrade:color/m3_sys_color_light_inverse_on_surface = 0x7f0501b9
com.seres.usb.upgrade:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501ae
com.seres.usb.upgrade:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501ac
com.seres.usb.upgrade:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501ab
com.seres.usb.upgrade:id/view_tree_view_model_store_owner = 0x7f0801f6
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501a8
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602c8
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501a7
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c0160
com.seres.usb.upgrade:attr/layout_constraintGuide_begin = 0x7f030281
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501a5
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501a3
com.seres.usb.upgrade:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f100314
com.seres.usb.upgrade:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600af
com.seres.usb.upgrade:color/m3_sys_color_light_on_secondary_container = 0x7f0501c2
com.seres.usb.upgrade:string/abc_menu_space_shortcut_label = 0x7f0f000f
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501a2
com.seres.usb.upgrade:id/autoComplete = 0x7f080054
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501a0
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface = 0x7f05019f
com.seres.usb.upgrade:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f0602fb
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_secondary_container = 0x7f05019e
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_primary_container = 0x7f05019c
com.seres.usb.upgrade:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060172
com.seres.usb.upgrade:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602f4
com.seres.usb.upgrade:dimen/m3_btn_translation_z_hovered = 0x7f0600e3
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f10041d
com.seres.usb.upgrade:attr/titleMarginTop = 0x7f03048c
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f050198
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500e6
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f050196
com.seres.usb.upgrade:attr/behavior_saveFlags = 0x7f030070
com.seres.usb.upgrade:dimen/abc_floating_window_z = 0x7f06002f
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_surface = 0x7f050195
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f10038a
com.seres.usb.upgrade:string/mtrl_picker_toggle_to_day_selection = 0x7f0f008e
com.seres.usb.upgrade:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060157
com.seres.usb.upgrade:style/Base.Theme.AppCompat.CompactMenu = 0x7f10004c
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_secondary = 0x7f050193
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f10039d
com.seres.usb.upgrade:attr/indicatorDirectionCircular = 0x7f030237
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f050192
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_primary = 0x7f050191
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1003ad
com.seres.usb.upgrade:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060281
com.seres.usb.upgrade:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f100281
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_background = 0x7f050190
com.seres.usb.upgrade:style/Widget.Material3.BottomAppBar = 0x7f100344
com.seres.usb.upgrade:id/unlabeled = 0x7f0801ed
com.seres.usb.upgrade:color/notification_action_color_filter = 0x7f0502d0
com.seres.usb.upgrade:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1000fa
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f05018f
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_background = 0x7f05018c
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f05018b
com.seres.usb.upgrade:integer/material_motion_path = 0x7f09002c
com.seres.usb.upgrade:attr/offsetAlignmentMode = 0x7f030353
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f050186
com.seres.usb.upgrade:dimen/m3_comp_linear_progress_indicator_active_indicator_height = 0x7f060136
com.seres.usb.upgrade:color/design_default_color_on_secondary = 0x7f050044
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f050185
com.seres.usb.upgrade:macro/m3_comp_snackbar_container_shape = 0x7f0c0115
com.seres.usb.upgrade:dimen/mtrl_card_checked_icon_size = 0x7f060295
com.seres.usb.upgrade:id/BOTTOM_END = 0x7f080001
com.seres.usb.upgrade:id/home = 0x7f0800d7
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050183
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface = 0x7f050181
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050180
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c008a
com.seres.usb.upgrade:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060170
com.seres.usb.upgrade:attr/layout_constraintTag = 0x7f030294
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_secondary = 0x7f05017f
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0098
com.seres.usb.upgrade:attr/layout_constraintBottom_toTopOf = 0x7f03027a
com.seres.usb.upgrade:dimen/m3_navigation_rail_default_width = 0x7f0601c2
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_primary_container = 0x7f05017e
com.seres.usb.upgrade:attr/colorTertiaryFixedDim = 0x7f03012d
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011c
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05017a
com.seres.usb.upgrade:id/mtrl_picker_header_title_and_selection = 0x7f08012a
com.seres.usb.upgrade:dimen/m3_comp_search_bar_container_elevation = 0x7f06016d
com.seres.usb.upgrade:attr/carousel_touchUpMode = 0x7f0300a9
com.seres.usb.upgrade:attr/autoCompleteTextViewStyle = 0x7f03003e
com.seres.usb.upgrade:anim/abc_slide_out_bottom = 0x7f010008
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_container_highest = 0x7f050167
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050175
com.seres.usb.upgrade:color/material_personalized_color_surface_container_highest = 0x7f05027d
com.seres.usb.upgrade:id/action_context_bar = 0x7f08003b
com.seres.usb.upgrade:attr/maxActionInlineWidth = 0x7f030300
com.seres.usb.upgrade:color/material_personalized_color_surface_inverse = 0x7f050281
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f05016f
com.seres.usb.upgrade:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602b6
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_background = 0x7f05016e
com.seres.usb.upgrade:style/Widget.Material3.BottomSheet = 0x7f100349
com.seres.usb.upgrade:id/action_menu_presenter = 0x7f08003f
com.seres.usb.upgrade:drawable/abc_tab_indicator_material = 0x7f07006c
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f100022
com.seres.usb.upgrade:dimen/mtrl_btn_dialog_btn_min_width = 0x7f060253
com.seres.usb.upgrade:styleable/ViewStubCompat = 0x7f110096
com.seres.usb.upgrade:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0035
com.seres.usb.upgrade:color/m3_sys_color_dark_tertiary_container = 0x7f05016d
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_variant = 0x7f05016b
com.seres.usb.upgrade:drawable/$avd_show_password__0 = 0x7f070003
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f10003f
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_container_lowest = 0x7f050169
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0094
com.seres.usb.upgrade:dimen/fastscroll_minimum_range = 0x7f060092
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_container_low = 0x7f050168
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f10028b
com.seres.usb.upgrade:attr/backgroundSplit = 0x7f03004e
com.seres.usb.upgrade:dimen/material_clock_period_toggle_height = 0x7f060223
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f060212
com.seres.usb.upgrade:dimen/design_navigation_item_horizontal_padding = 0x7f060078
com.seres.usb.upgrade:attr/checkedIconEnabled = 0x7f0300b5
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_container = 0x7f050165
com.seres.usb.upgrade:attr/dayTodayStyle = 0x7f03016e
com.seres.usb.upgrade:color/m3_sys_color_dark_primary_container = 0x7f050160
com.seres.usb.upgrade:dimen/design_snackbar_padding_horizontal = 0x7f060085
com.seres.usb.upgrade:string/m3_sys_motion_easing_linear = 0x7f0f0043
com.seres.usb.upgrade:color/m3_sys_color_dark_primary = 0x7f05015f
com.seres.usb.upgrade:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601b1
com.seres.usb.upgrade:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f100459
com.seres.usb.upgrade:color/m3_sys_color_dark_on_tertiary = 0x7f05015b
com.seres.usb.upgrade:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010f
com.seres.usb.upgrade:color/mtrl_on_surface_ripple_color = 0x7f0502bc
com.seres.usb.upgrade:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1002f7
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Headline2 = 0x7f1001ff
com.seres.usb.upgrade:color/m3_sys_color_dark_on_surface = 0x7f050159
com.seres.usb.upgrade:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1003e5
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f10006b
com.seres.usb.upgrade:dimen/abc_text_size_small_material = 0x7f06004c
com.seres.usb.upgrade:drawable/abc_btn_colored_material = 0x7f070030
com.seres.usb.upgrade:color/m3_sys_color_dark_on_secondary_container = 0x7f050158
com.seres.usb.upgrade:id/accessibility_action_clickable_span = 0x7f08000f
com.seres.usb.upgrade:id/sharedValueUnset = 0x7f08018f
com.seres.usb.upgrade:dimen/material_emphasis_disabled_background = 0x7f06022c
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f060206
com.seres.usb.upgrade:animator/fragment_close_exit = 0x7f020004
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary99 = 0x7f0500cf
com.seres.usb.upgrade:color/m3_sys_color_dark_on_secondary = 0x7f050157
com.seres.usb.upgrade:color/m3_sys_color_dark_on_primary = 0x7f050155
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500dd
com.seres.usb.upgrade:id/aligned = 0x7f080048
com.seres.usb.upgrade:styleable/ConstraintLayout_Layout = 0x7f110027
com.seres.usb.upgrade:id/left = 0x7f0800ee
com.seres.usb.upgrade:attr/dynamicColorThemeOverlay = 0x7f030197
com.seres.usb.upgrade:color/m3_sys_color_dark_on_error_container = 0x7f050154
com.seres.usb.upgrade:attr/popupTheme = 0x7f03037d
com.seres.usb.upgrade:color/m3_sys_color_dark_inverse_surface = 0x7f050151
com.seres.usb.upgrade:color/m3_sys_color_dark_error_container = 0x7f05014e
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f100447
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f100095
com.seres.usb.upgrade:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.seres.usb.upgrade:color/m3_ref_palette_tertiary100 = 0x7f050138
com.seres.usb.upgrade:color/m3_sys_color_dark_error = 0x7f05014d
com.seres.usb.upgrade:style/Base.Widget.Material3.Snackbar = 0x7f10010e
com.seres.usb.upgrade:drawable/abc_list_divider_material = 0x7f07004d
com.seres.usb.upgrade:color/m3_switch_track_tint = 0x7f05014b
com.seres.usb.upgrade:attr/waveShape = 0x7f0304c9
com.seres.usb.upgrade:color/m3_switch_thumb_tint = 0x7f05014a
com.seres.usb.upgrade:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003d
com.seres.usb.upgrade:dimen/m3_alert_dialog_icon_margin = 0x7f0600a1
com.seres.usb.upgrade:attr/circularProgressIndicatorStyle = 0x7f0300d2
com.seres.usb.upgrade:attr/layout_constraintRight_creator = 0x7f03028f
com.seres.usb.upgrade:color/m3_slider_inactive_track_color = 0x7f050148
com.seres.usb.upgrade:color/m3_slider_active_track_color = 0x7f050146
com.seres.usb.upgrade:color/m3_ref_palette_white = 0x7f050143
com.seres.usb.upgrade:id/action_bar_subtitle = 0x7f080038
com.seres.usb.upgrade:attr/itemRippleColor = 0x7f03024e
com.seres.usb.upgrade:color/material_personalized_color_primary_text_inverse = 0x7f050274
com.seres.usb.upgrade:color/m3_ref_palette_tertiary99 = 0x7f050142
com.seres.usb.upgrade:color/m3_ref_palette_tertiary95 = 0x7f050141
com.seres.usb.upgrade:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012c
com.seres.usb.upgrade:drawable/notification_bg_low = 0x7f0700db
com.seres.usb.upgrade:color/m3_ref_palette_tertiary80 = 0x7f05013f
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f100301
com.seres.usb.upgrade:drawable/abc_vector_test = 0x7f070077
com.seres.usb.upgrade:attr/labelStyle = 0x7f030264
com.seres.usb.upgrade:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501b4
com.seres.usb.upgrade:style/Animation.Material3.SideSheetDialog.Left = 0x7f100008
com.seres.usb.upgrade:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0128
com.seres.usb.upgrade:color/m3_ref_palette_tertiary50 = 0x7f05013c
com.seres.usb.upgrade:color/m3_ref_palette_tertiary40 = 0x7f05013b
com.seres.usb.upgrade:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a4
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0014
com.seres.usb.upgrade:color/m3_ref_palette_tertiary30 = 0x7f05013a
com.seres.usb.upgrade:styleable/TextEffects = 0x7f11008a
com.seres.usb.upgrade:color/material_personalized__highlighted_text = 0x7f05025a
com.seres.usb.upgrade:color/m3_ref_palette_secondary99 = 0x7f050135
com.seres.usb.upgrade:layout/notification_template_custom_big = 0x7f0b0063
com.seres.usb.upgrade:color/m3_timepicker_display_text_color = 0x7f0501f8
com.seres.usb.upgrade:color/m3_selection_control_ripple_color_selector = 0x7f050144
com.seres.usb.upgrade:color/m3_ref_palette_secondary95 = 0x7f050134
com.seres.usb.upgrade:color/m3_ref_palette_secondary80 = 0x7f050132
com.seres.usb.upgrade:attr/layout_constraintHorizontal_chainStyle = 0x7f03028a
com.seres.usb.upgrade:color/m3_ref_palette_secondary70 = 0x7f050131
com.seres.usb.upgrade:color/m3_ref_palette_secondary50 = 0x7f05012f
com.seres.usb.upgrade:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602c2
com.seres.usb.upgrade:dimen/compat_button_padding_vertical_material = 0x7f060059
com.seres.usb.upgrade:color/m3_ref_palette_secondary40 = 0x7f05012e
com.seres.usb.upgrade:color/m3_ref_palette_secondary20 = 0x7f05012c
com.seres.usb.upgrade:color/m3_ref_palette_secondary0 = 0x7f050129
com.seres.usb.upgrade:color/m3_ref_palette_primary99 = 0x7f050128
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
com.seres.usb.upgrade:id/fitCenter = 0x7f0800c1
com.seres.usb.upgrade:color/mtrl_tabs_legacy_text_color_selector = 0x7f0502c8
com.seres.usb.upgrade:color/m3_ref_palette_primary90 = 0x7f050126
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f100028
com.seres.usb.upgrade:color/m3_ref_palette_primary80 = 0x7f050125
com.seres.usb.upgrade:color/m3_ref_palette_primary60 = 0x7f050123
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1003a6
com.seres.usb.upgrade:id/disjoint = 0x7f0800a3
com.seres.usb.upgrade:color/m3_ref_palette_primary50 = 0x7f050122
com.seres.usb.upgrade:color/m3_ref_palette_primary40 = 0x7f050121
com.seres.usb.upgrade:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0043
com.seres.usb.upgrade:color/mtrl_switch_track_tint = 0x7f0502c4
com.seres.usb.upgrade:integer/bottom_sheet_slide_duration = 0x7f090003
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary60 = 0x7f0500ca
com.seres.usb.upgrade:color/m3_ref_palette_primary30 = 0x7f050120
com.seres.usb.upgrade:color/m3_ref_palette_primary20 = 0x7f05011f
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f100446
com.seres.usb.upgrade:color/m3_ref_palette_primary100 = 0x7f05011e
com.seres.usb.upgrade:color/design_default_color_background = 0x7f05003f
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant90 = 0x7f050119
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1001c1
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f100158
com.seres.usb.upgrade:layout/mtrl_picker_header_fullscreen = 0x7f0b0059
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant80 = 0x7f050118
com.seres.usb.upgrade:style/Theme.MaterialComponents.NoActionBar = 0x7f100270
com.seres.usb.upgrade:attr/theme = 0x7f03046a
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant40 = 0x7f050114
com.seres.usb.upgrade:attr/tabBackground = 0x7f030408
com.seres.usb.upgrade:attr/actionProviderClass = 0x7f030023
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant100 = 0x7f050111
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050171
com.seres.usb.upgrade:attr/floatingActionButtonPrimaryStyle = 0x7f0301dd
com.seres.usb.upgrade:style/Widget.Material3.Snackbar.TextView = 0x7f1003ca
com.seres.usb.upgrade:id/text_input_start_icon = 0x7f0801cb
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant0 = 0x7f05010f
com.seres.usb.upgrade:id/material_hour_tv = 0x7f080104
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700cd
com.seres.usb.upgrade:style/Base.V7.Theme.AppCompat.Light = 0x7f1000bd
com.seres.usb.upgrade:dimen/mtrl_card_elevation = 0x7f060298
com.seres.usb.upgrade:dimen/cardview_default_elevation = 0x7f060053
com.seres.usb.upgrade:attr/checkedIconMargin = 0x7f0300b7
com.seres.usb.upgrade:attr/textAppearancePopupMenuHeader = 0x7f030446
com.seres.usb.upgrade:color/bright_foreground_inverse_material_light = 0x7f050025
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f100387
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f10024e
com.seres.usb.upgrade:macro/m3_comp_fab_surface_container_color = 0x7f0c003e
com.seres.usb.upgrade:color/m3_ref_palette_neutral95 = 0x7f05010b
com.seres.usb.upgrade:attr/expandedTitleGravity = 0x7f0301b9
com.seres.usb.upgrade:color/m3_ref_palette_neutral94 = 0x7f05010a
com.seres.usb.upgrade:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009b
com.seres.usb.upgrade:attr/autoSizeMinTextSize = 0x7f030041
com.seres.usb.upgrade:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c5
com.seres.usb.upgrade:color/m3_ref_palette_neutral60 = 0x7f050104
com.seres.usb.upgrade:attr/allowStacking = 0x7f03002d
com.seres.usb.upgrade:color/design_dark_default_color_background = 0x7f050032
com.seres.usb.upgrade:color/m3_ref_palette_neutral40 = 0x7f050101
com.seres.usb.upgrade:color/m3_sys_color_light_surface_container_low = 0x7f0501d2
com.seres.usb.upgrade:attr/layout_keyline = 0x7f0302ab
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f100212
com.seres.usb.upgrade:color/m3_ref_palette_neutral30 = 0x7f0500ff
com.seres.usb.upgrade:attr/suffixTextColor = 0x7f030402
com.seres.usb.upgrade:styleable/Tooltip = 0x7f11008f
com.seres.usb.upgrade:style/Widget.MaterialComponents.Chip.Choice = 0x7f100402
com.seres.usb.upgrade:attr/textureHeight = 0x7f030468
com.seres.usb.upgrade:color/m3_ref_palette_neutral22 = 0x7f0500fd
com.seres.usb.upgrade:styleable/StateListDrawable = 0x7f110082
com.seres.usb.upgrade:color/m3_ref_palette_neutral10 = 0x7f0500f8
com.seres.usb.upgrade:color/m3_ref_palette_error99 = 0x7f0500f6
com.seres.usb.upgrade:macro/m3_comp_time_picker_headline_color = 0x7f0c0151
com.seres.usb.upgrade:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.seres.usb.upgrade:attr/flow_lastHorizontalBias = 0x7f0301ef
com.seres.usb.upgrade:color/m3_ref_palette_error80 = 0x7f0500f3
com.seres.usb.upgrade:integer/m3_btn_anim_delay_ms = 0x7f09000a
com.seres.usb.upgrade:drawable/design_ic_visibility_off = 0x7f070085
com.seres.usb.upgrade:color/m3_ref_palette_error60 = 0x7f0500f1
com.seres.usb.upgrade:attr/springDamping = 0x7f0303d9
com.seres.usb.upgrade:dimen/mtrl_switch_thumb_elevation = 0x7f0602ef
com.seres.usb.upgrade:color/m3_ref_palette_error40 = 0x7f0500ef
com.seres.usb.upgrade:attr/shortcutMatchRequired = 0x7f0303ba
com.seres.usb.upgrade:color/m3_ref_palette_error100 = 0x7f0500ec
com.seres.usb.upgrade:color/m3_ref_palette_error10 = 0x7f0500eb
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500e5
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500e4
com.seres.usb.upgrade:style/Widget.MaterialComponents.TabLayout = 0x7f10043d
com.seres.usb.upgrade:attr/chipBackgroundColor = 0x7f0300bd
com.seres.usb.upgrade:attr/counterOverflowTextAppearance = 0x7f030158
com.seres.usb.upgrade:dimen/mtrl_snackbar_margin = 0x7f0602eb
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500df
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601f1
com.seres.usb.upgrade:dimen/mtrl_badge_text_size = 0x7f060248
com.seres.usb.upgrade:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f0
com.seres.usb.upgrade:attr/windowMinWidthMajor = 0x7f0304d2
com.seres.usb.upgrade:color/cardview_shadow_start_color = 0x7f05002f
com.seres.usb.upgrade:dimen/m3_badge_with_text_offset = 0x7f0600b8
com.seres.usb.upgrade:attr/maxCharacterCount = 0x7f030302
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant99 = 0x7f05011b
com.seres.usb.upgrade:style/Widget.AppCompat.EditText = 0x7f1002fd
com.seres.usb.upgrade:attr/motionDurationLong2 = 0x7f03031f
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500d5
com.seres.usb.upgrade:dimen/mtrl_calendar_header_height = 0x7f06027a
com.seres.usb.upgrade:attr/itemIconSize = 0x7f030247
com.seres.usb.upgrade:dimen/design_navigation_padding_bottom = 0x7f06007c
com.seres.usb.upgrade:color/material_personalized_primary_text_disable_only = 0x7f05028d
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500d1
com.seres.usb.upgrade:attr/dividerInsetStart = 0x7f03017f
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500d0
com.seres.usb.upgrade:attr/wavePhase = 0x7f0304c8
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary50 = 0x7f0500c9
com.seres.usb.upgrade:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f060185
com.seres.usb.upgrade:dimen/mtrl_slider_halo_radius = 0x7f0602de
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1001b7
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary40 = 0x7f0500c8
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1000e1
com.seres.usb.upgrade:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600ca
com.seres.usb.upgrade:color/m3_sys_color_primary_fixed_dim = 0x7f0501df
com.seres.usb.upgrade:attr/checkedTextViewStyle = 0x7f0300bc
com.seres.usb.upgrade:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f5
com.seres.usb.upgrade:attr/customColorDrawableValue = 0x7f030162
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary100 = 0x7f0500c5
com.seres.usb.upgrade:attr/telltales_tailColor = 0x7f030426
com.seres.usb.upgrade:id/accessibility_custom_action_17 = 0x7f080019
com.seres.usb.upgrade:style/Widget.Material3.SideSheet.Detached = 0x7f1003c3
com.seres.usb.upgrade:anim/abc_tooltip_enter = 0x7f01000a
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500c1
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500a0
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500be
com.seres.usb.upgrade:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500bd
com.seres.usb.upgrade:id/accessibility_custom_action_2 = 0x7f08001c
com.seres.usb.upgrade:drawable/abc_seekbar_thumb_material = 0x7f070063
com.seres.usb.upgrade:attr/snackbarTextViewStyle = 0x7f0303d2
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500bc
com.seres.usb.upgrade:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f100412
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500b7
com.seres.usb.upgrade:drawable/m3_avd_hide_password = 0x7f07009e
com.seres.usb.upgrade:attr/alphabeticModifiers = 0x7f03002f
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f100183
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500b4
com.seres.usb.upgrade:style/Base.Animation.AppCompat.Tooltip = 0x7f10000f
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c0100
com.seres.usb.upgrade:color/m3_sys_color_light_on_secondary = 0x7f0501c1
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog = 0x7f100265
com.seres.usb.upgrade:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0107
com.seres.usb.upgrade:color/m3_ref_palette_secondary60 = 0x7f050130
com.seres.usb.upgrade:attr/layout_dodgeInsetEdges = 0x7f0302a0
com.seres.usb.upgrade:attr/tabIconTintMode = 0x7f03040c
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500b1
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500ac
com.seres.usb.upgrade:string/icon_content_description = 0x7f0f0035
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500ab
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1001df
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500a8
com.seres.usb.upgrade:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.seres.usb.upgrade:id/dragAnticlockwise = 0x7f0800a4
com.seres.usb.upgrade:attr/viewInflaterClass = 0x7f0304bd
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500a6
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral10 = 0x7f05009f
com.seres.usb.upgrade:attr/contrast = 0x7f030147
com.seres.usb.upgrade:attr/reactiveGuide_applyToAllConstraintSets = 0x7f030392
com.seres.usb.upgrade:color/mtrl_tabs_ripple_color = 0x7f0502c9
com.seres.usb.upgrade:color/m3_ref_palette_black = 0x7f05009d
com.seres.usb.upgrade:attr/cornerFamily = 0x7f03014b
com.seres.usb.upgrade:attr/liftOnScrollTargetViewId = 0x7f0302b4
com.seres.usb.upgrade:color/m3_radiobutton_button_tint = 0x7f05009b
com.seres.usb.upgrade:attr/colorBackgroundFloating = 0x7f0300f3
com.seres.usb.upgrade:color/m3_primary_text_disable_only = 0x7f05009a
com.seres.usb.upgrade:color/m3_popupmenu_overlay_color = 0x7f050099
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500e0
com.seres.usb.upgrade:attr/autoSizeTextType = 0x7f030044
com.seres.usb.upgrade:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601a4
com.seres.usb.upgrade:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501aa
com.seres.usb.upgrade:color/m3_navigation_item_text_color = 0x7f050095
com.seres.usb.upgrade:attr/buttonIconTintMode = 0x7f030094
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f100150
com.seres.usb.upgrade:id/open_search_bar_text_view = 0x7f080147
com.seres.usb.upgrade:color/m3_navigation_item_ripple_color = 0x7f050094
com.seres.usb.upgrade:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
com.seres.usb.upgrade:color/m3_navigation_bar_ripple_color_selector = 0x7f050091
com.seres.usb.upgrade:color/m3_hint_foreground = 0x7f05008d
com.seres.usb.upgrade:color/m3_filled_icon_button_container_color_selector = 0x7f05008b
com.seres.usb.upgrade:color/m3_fab_ripple_color_selector = 0x7f05008a
com.seres.usb.upgrade:color/m3_fab_efab_foreground_color_selector = 0x7f050089
com.seres.usb.upgrade:id/text2 = 0x7f0801c3
com.seres.usb.upgrade:drawable/mtrl_popupmenu_background_overlay = 0x7f0700ca
com.seres.usb.upgrade:color/m3_elevated_chip_background_color = 0x7f050087
com.seres.usb.upgrade:color/m3_ref_palette_tertiary0 = 0x7f050136
com.seres.usb.upgrade:animator/design_fab_show_motion_spec = 0x7f020002
com.seres.usb.upgrade:color/m3_dynamic_default_color_secondary_text = 0x7f050082
com.seres.usb.upgrade:color/m3_dynamic_dark_highlighted_text = 0x7f05007e
com.seres.usb.upgrade:id/always = 0x7f08004b
com.seres.usb.upgrade:string/material_timepicker_pm = 0x7f0f005a
com.seres.usb.upgrade:color/m3_default_color_secondary_text = 0x7f05007b
com.seres.usb.upgrade:color/m3_dark_primary_text_disable_only = 0x7f050079
com.seres.usb.upgrade:drawable/notification_bg_low_normal = 0x7f0700dc
com.seres.usb.upgrade:attr/closeIconSize = 0x7f0300e1
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f10012c
com.seres.usb.upgrade:color/m3_dark_hint_foreground = 0x7f050078
com.seres.usb.upgrade:style/Widget.MaterialComponents.Chip.Action = 0x7f100401
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500a7
com.seres.usb.upgrade:anim/design_bottom_sheet_slide_in = 0x7f010018
com.seres.usb.upgrade:attr/selectableItemBackgroundBorderless = 0x7f0303ab
com.seres.usb.upgrade:color/m3_dark_highlighted_text = 0x7f050077
com.seres.usb.upgrade:attr/startIconCheckable = 0x7f0303e0
com.seres.usb.upgrade:id/accessibility_custom_action_25 = 0x7f080022
com.seres.usb.upgrade:color/abc_search_url_text_pressed = 0x7f05000f
com.seres.usb.upgrade:color/material_dynamic_tertiary80 = 0x7f050241
com.seres.usb.upgrade:color/m3_chip_text_color = 0x7f050074
com.seres.usb.upgrade:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.seres.usb.upgrade:attr/fabAnchorMode = 0x7f0301cb
com.seres.usb.upgrade:color/m3_calendar_item_stroke_color = 0x7f05006a
com.seres.usb.upgrade:style/Theme.Material3.DayNight = 0x7f100230
com.seres.usb.upgrade:color/m3_ref_palette_neutral87 = 0x7f050107
com.seres.usb.upgrade:color/m3_button_ripple_color = 0x7f050067
com.seres.usb.upgrade:style/Base.V28.Theme.AppCompat.Light = 0x7f1000ba
com.seres.usb.upgrade:color/m3_button_outline_color_selector = 0x7f050066
com.seres.usb.upgrade:styleable/Transition = 0x7f110091
com.seres.usb.upgrade:styleable/MaterialToolbar = 0x7f11005d
com.seres.usb.upgrade:color/m3_assist_chip_stroke_color = 0x7f050062
com.seres.usb.upgrade:dimen/m3_toolbar_text_size_title = 0x7f060219
com.seres.usb.upgrade:color/m3_sys_color_light_on_error_container = 0x7f0501be
com.seres.usb.upgrade:color/highlighted_text_material_dark = 0x7f05005e
com.seres.usb.upgrade:color/material_timepicker_modebutton_tint = 0x7f050298
com.seres.usb.upgrade:color/error_color_material_light = 0x7f05005b
com.seres.usb.upgrade:color/dim_foreground_material_light = 0x7f050059
com.seres.usb.upgrade:color/material_on_background_emphasis_medium = 0x7f050252
com.seres.usb.upgrade:color/dim_foreground_disabled_material_light = 0x7f050057
com.seres.usb.upgrade:style/Widget.Material3.Badge = 0x7f100342
com.seres.usb.upgrade:id/disableScroll = 0x7f0800a2
com.seres.usb.upgrade:color/design_snackbar_background_color = 0x7f050055
com.seres.usb.upgrade:style/Theme.AppCompat.NoActionBar = 0x7f100221
com.seres.usb.upgrade:color/design_fab_stroke_top_outer_color = 0x7f050053
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f100113
com.seres.usb.upgrade:attr/layout_constraintBaseline_toBaselineOf = 0x7f030275
com.seres.usb.upgrade:color/design_error = 0x7f05004c
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f0
com.seres.usb.upgrade:color/design_default_color_secondary_variant = 0x7f05004a
com.seres.usb.upgrade:id/circle_center = 0x7f080079
com.seres.usb.upgrade:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f060263
com.seres.usb.upgrade:style/Base.Widget.AppCompat.PopupMenu = 0x7f1000eb
com.seres.usb.upgrade:color/m3_ref_palette_secondary100 = 0x7f05012b
com.seres.usb.upgrade:attr/verticalOffset = 0x7f0304bb
com.seres.usb.upgrade:color/design_default_color_primary_dark = 0x7f050047
com.seres.usb.upgrade:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ad
com.seres.usb.upgrade:color/design_default_color_primary = 0x7f050046
com.seres.usb.upgrade:attr/itemPaddingBottom = 0x7f03024c
com.seres.usb.upgrade:id/split_action_bar = 0x7f0801a0
com.seres.usb.upgrade:attr/indicatorInset = 0x7f030239
com.seres.usb.upgrade:style/Platform.MaterialComponents.Dialog = 0x7f10013a
com.seres.usb.upgrade:color/design_default_color_on_background = 0x7f050041
com.seres.usb.upgrade:color/design_default_color_error = 0x7f050040
com.seres.usb.upgrade:style/Theme.Material3.DayNight.NoActionBar = 0x7f100236
com.seres.usb.upgrade:attr/flow_lastVerticalStyle = 0x7f0301f2
com.seres.usb.upgrade:attr/chipStartPadding = 0x7f0300cc
com.seres.usb.upgrade:attr/materialCardViewOutlinedStyle = 0x7f0302ea
com.seres.usb.upgrade:color/design_dark_default_color_secondary_variant = 0x7f05003d
com.seres.usb.upgrade:style/Widget.Design.BottomNavigationView = 0x7f100331
com.seres.usb.upgrade:id/accessibility_custom_action_30 = 0x7f080028
com.seres.usb.upgrade:attr/textAppearanceOverline = 0x7f030445
com.seres.usb.upgrade:color/design_dark_default_color_secondary = 0x7f05003c
com.seres.usb.upgrade:style/Theme.AppCompat.CompactMenu = 0x7f10020d
com.seres.usb.upgrade:dimen/mtrl_calendar_year_corner = 0x7f06028f
com.seres.usb.upgrade:color/design_dark_default_color_primary_variant = 0x7f05003b
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1002c0
com.seres.usb.upgrade:string/mtrl_picker_navigate_to_year_description = 0x7f0f007d
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0062
com.seres.usb.upgrade:dimen/mtrl_btn_padding_top = 0x7f060261
com.seres.usb.upgrade:color/design_dark_default_color_primary_dark = 0x7f05003a
com.seres.usb.upgrade:id/default_activity_button = 0x7f080094
com.seres.usb.upgrade:color/material_dynamic_neutral_variant40 = 0x7f050216
com.seres.usb.upgrade:style/Theme.Material3.DynamicColors.Dark = 0x7f100238
com.seres.usb.upgrade:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f06024f
com.seres.usb.upgrade:dimen/def_drawer_elevation = 0x7f06005d
com.seres.usb.upgrade:attr/tooltipText = 0x7f03049b
com.seres.usb.upgrade:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1000ec
com.seres.usb.upgrade:color/design_dark_default_color_on_error = 0x7f050035
com.seres.usb.upgrade:color/design_bottom_navigation_shadow_color = 0x7f050030
com.seres.usb.upgrade:dimen/mtrl_btn_hovered_z = 0x7f060258
com.seres.usb.upgrade:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f060234
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_background = 0x7f050172
com.seres.usb.upgrade:color/cardview_shadow_end_color = 0x7f05002e
com.seres.usb.upgrade:color/cardview_light_background = 0x7f05002d
com.seres.usb.upgrade:color/cardview_dark_background = 0x7f05002c
com.seres.usb.upgrade:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f100409
com.seres.usb.upgrade:id/callMeasure = 0x7f08006b
com.seres.usb.upgrade:color/material_dynamic_primary10 = 0x7f05021f
com.seres.usb.upgrade:color/call_notification_decline_color = 0x7f05002b
com.seres.usb.upgrade:attr/textAppearanceTitleMedium = 0x7f03044d
com.seres.usb.upgrade:styleable/LinearLayoutCompat_Layout = 0x7f110049
com.seres.usb.upgrade:attr/compatShadowEnabled = 0x7f03012f
com.seres.usb.upgrade:color/button_material_light = 0x7f050029
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050170
com.seres.usb.upgrade:color/button_material_dark = 0x7f050028
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500ba
com.seres.usb.upgrade:color/foreground_material_dark = 0x7f05005c
com.seres.usb.upgrade:color/mtrl_card_view_foreground = 0x7f0502a3
com.seres.usb.upgrade:color/bright_foreground_material_light = 0x7f050027
com.seres.usb.upgrade:color/bright_foreground_material_dark = 0x7f050026
com.seres.usb.upgrade:animator/mtrl_card_state_list_anim = 0x7f020017
com.seres.usb.upgrade:color/m3_dynamic_primary_text_disable_only = 0x7f050085
com.seres.usb.upgrade:id/fitStart = 0x7f0800c3
com.seres.usb.upgrade:color/m3_ref_palette_neutral20 = 0x7f0500fc
com.seres.usb.upgrade:attr/toggleCheckedStateOnClick = 0x7f030493
com.seres.usb.upgrade:dimen/mtrl_tooltip_padding = 0x7f060302
com.seres.usb.upgrade:attr/paddingEnd = 0x7f03035f
com.seres.usb.upgrade:attr/lastItemDecorated = 0x7f030268
com.seres.usb.upgrade:color/bright_foreground_disabled_material_dark = 0x7f050022
com.seres.usb.upgrade:color/mtrl_btn_ripple_color = 0x7f05029a
com.seres.usb.upgrade:string/abc_action_bar_up_description = 0x7f0f0001
com.seres.usb.upgrade:attr/textAppearanceListItem = 0x7f030442
com.seres.usb.upgrade:animator/fragment_fade_exit = 0x7f020006
com.seres.usb.upgrade:color/background_material_light = 0x7f050020
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_container_elevation = 0x7f06013a
com.seres.usb.upgrade:color/androidx_core_ripple_material_light = 0x7f05001b
com.seres.usb.upgrade:attr/fabCradleMargin = 0x7f0301cd
com.seres.usb.upgrade:id/marquee = 0x7f0800f7
com.seres.usb.upgrade:attr/itemPadding = 0x7f03024b
com.seres.usb.upgrade:attr/onNegativeCross = 0x7f030356
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500b0
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f10030f
com.seres.usb.upgrade:color/accent_material_light = 0x7f05001a
com.seres.usb.upgrade:attr/targetId = 0x7f030425
com.seres.usb.upgrade:attr/flow_firstVerticalBias = 0x7f0301e9
com.seres.usb.upgrade:color/abc_tint_seek_thumb = 0x7f050016
com.seres.usb.upgrade:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006d
com.seres.usb.upgrade:style/Widget.Material3.ActionMode = 0x7f10033c
com.seres.usb.upgrade:dimen/abc_button_inset_vertical_material = 0x7f060013
com.seres.usb.upgrade:attr/layout_marginBaseline = 0x7f0302ac
com.seres.usb.upgrade:color/abc_tint_default = 0x7f050014
com.seres.usb.upgrade:color/abc_secondary_text_material_light = 0x7f050012
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601c5
com.seres.usb.upgrade:style/Base.AlertDialog.AppCompat.Light = 0x7f10000c
com.seres.usb.upgrade:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070043
com.seres.usb.upgrade:color/abc_search_url_text_selected = 0x7f050010
com.seres.usb.upgrade:color/abc_search_url_text_normal = 0x7f05000e
com.seres.usb.upgrade:attr/cardPreventCornerOverlap = 0x7f03009f
com.seres.usb.upgrade:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.seres.usb.upgrade:color/abc_search_url_text = 0x7f05000d
com.seres.usb.upgrade:string/abc_activitychooserview_choose_application = 0x7f0f0005
com.seres.usb.upgrade:id/barrier = 0x7f080057
com.seres.usb.upgrade:attr/buttonBarStyle = 0x7f03008e
com.seres.usb.upgrade:color/abc_primary_text_material_dark = 0x7f05000b
com.seres.usb.upgrade:color/abc_hint_foreground_material_dark = 0x7f050007
com.seres.usb.upgrade:attr/dropDownBackgroundTint = 0x7f030193
com.seres.usb.upgrade:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.seres.usb.upgrade:color/abc_decor_view_status_guard_light = 0x7f050006
com.seres.usb.upgrade:attr/actionModeCloseContentDescription = 0x7f030014
com.seres.usb.upgrade:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.seres.usb.upgrade:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
com.seres.usb.upgrade:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.seres.usb.upgrade:style/TextAppearance.Design.Counter = 0x7f1001ca
com.seres.usb.upgrade:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0036
com.seres.usb.upgrade:color/m3_ref_palette_secondary90 = 0x7f050133
com.seres.usb.upgrade:attr/yearTodayStyle = 0x7f0304d7
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1001d4
com.seres.usb.upgrade:id/contentPanel = 0x7f080085
com.seres.usb.upgrade:attr/counterTextColor = 0x7f03015b
com.seres.usb.upgrade:color/material_personalized_color_on_error = 0x7f050263
com.seres.usb.upgrade:attr/yearSelectedStyle = 0x7f0304d5
com.seres.usb.upgrade:color/m3_sys_color_dark_tertiary = 0x7f05016c
com.seres.usb.upgrade:id/hideable = 0x7f0800d6
com.seres.usb.upgrade:attr/windowMinWidthMinor = 0x7f0304d3
com.seres.usb.upgrade:attr/windowFixedWidthMinor = 0x7f0304d1
com.seres.usb.upgrade:dimen/m3_searchbar_margin_horizontal = 0x7f0601d4
com.seres.usb.upgrade:color/m3_sys_color_tertiary_fixed = 0x7f0501e2
com.seres.usb.upgrade:dimen/abc_text_size_menu_material = 0x7f06004b
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500da
com.seres.usb.upgrade:attr/waveOffset = 0x7f0304c6
com.seres.usb.upgrade:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f100376
com.seres.usb.upgrade:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1000f4
com.seres.usb.upgrade:attr/warmth = 0x7f0304c4
com.seres.usb.upgrade:color/primary_text_default_material_dark = 0x7f0502d6
com.seres.usb.upgrade:attr/cornerSize = 0x7f030151
com.seres.usb.upgrade:styleable/PopupWindowBackgroundState = 0x7f110070
com.seres.usb.upgrade:attr/voiceIcon = 0x7f0304c3
com.seres.usb.upgrade:attr/viewTransitionOnPositiveCross = 0x7f0304c1
com.seres.usb.upgrade:styleable/ActionMenuItemView = 0x7f110002
com.seres.usb.upgrade:attr/floatingActionButtonSurfaceStyle = 0x7f0301e5
com.seres.usb.upgrade:style/Base.AlertDialog.AppCompat = 0x7f10000b
com.seres.usb.upgrade:dimen/material_clock_period_toggle_width = 0x7f060226
com.seres.usb.upgrade:attr/viewTransitionOnCross = 0x7f0304bf
com.seres.usb.upgrade:color/design_default_color_on_primary = 0x7f050043
com.seres.usb.upgrade:styleable/MenuItem = 0x7f11005f
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Headline1 = 0x7f1001fe
com.seres.usb.upgrade:color/material_dynamic_tertiary90 = 0x7f050242
com.seres.usb.upgrade:attr/ttcIndex = 0x7f0304b5
com.seres.usb.upgrade:id/pathRelative = 0x7f080160
com.seres.usb.upgrade:dimen/design_bottom_navigation_margin = 0x7f060068
com.seres.usb.upgrade:color/m3_sys_color_on_primary_fixed_variant = 0x7f0501d9
com.seres.usb.upgrade:string/abc_menu_function_shortcut_label = 0x7f0f000c
com.seres.usb.upgrade:attr/layout_optimizationLevel = 0x7f0302ad
com.seres.usb.upgrade:color/m3_chip_stroke_color = 0x7f050073
com.seres.usb.upgrade:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
com.seres.usb.upgrade:attr/textAppearanceListItemSecondary = 0x7f030443
com.seres.usb.upgrade:drawable/material_ic_edit_black_24dp = 0x7f0700ac
com.seres.usb.upgrade:drawable/design_password_eye = 0x7f070086
com.seres.usb.upgrade:dimen/mtrl_btn_text_btn_padding_right = 0x7f060267
com.seres.usb.upgrade:color/material_dynamic_neutral20 = 0x7f050207
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f10018a
com.seres.usb.upgrade:color/call_notification_answer_color = 0x7f05002a
com.seres.usb.upgrade:attr/colorTertiaryContainer = 0x7f03012b
com.seres.usb.upgrade:bool/abc_action_bar_embed_tabs = 0x7f040000
com.seres.usb.upgrade:id/visible = 0x7f0801f7
com.seres.usb.upgrade:attr/transitionFlags = 0x7f0304af
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070012
com.seres.usb.upgrade:attr/transitionEasing = 0x7f0304ae
com.seres.usb.upgrade:drawable/abc_cab_background_top_material = 0x7f070039
com.seres.usb.upgrade:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1001e3
com.seres.usb.upgrade:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ae
com.seres.usb.upgrade:attr/layout_editor_absoluteY = 0x7f0302a2
com.seres.usb.upgrade:attr/transformPivotTarget = 0x7f0304ac
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f10008d
com.seres.usb.upgrade:attr/helperTextTextColor = 0x7f030215
com.seres.usb.upgrade:attr/submitBackground = 0x7f0303fa
com.seres.usb.upgrade:id/SHOW_PATH = 0x7f080009
com.seres.usb.upgrade:attr/trackThickness = 0x7f0304a9
com.seres.usb.upgrade:layout/abc_action_bar_up_container = 0x7f0b0001
com.seres.usb.upgrade:attr/queryPatterns = 0x7f03038b
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500d6
com.seres.usb.upgrade:dimen/design_fab_translation_z_hovered_focused = 0x7f060073
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0085
com.seres.usb.upgrade:dimen/design_navigation_max_width = 0x7f06007b
com.seres.usb.upgrade:attr/trackDecorationTint = 0x7f0304a6
com.seres.usb.upgrade:attr/trackColorInactive = 0x7f0304a3
com.seres.usb.upgrade:attr/colorSurfaceContainer = 0x7f030121
com.seres.usb.upgrade:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.seres.usb.upgrade:dimen/design_tab_text_size_2line = 0x7f06008c
com.seres.usb.upgrade:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1000aa
com.seres.usb.upgrade:attr/colorOnSurfaceVariant = 0x7f03010b
com.seres.usb.upgrade:attr/gapBetweenBars = 0x7f03020a
com.seres.usb.upgrade:attr/trackColor = 0x7f0304a1
com.seres.usb.upgrade:color/background_floating_material_light = 0x7f05001e
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f100298
com.seres.usb.upgrade:attr/splitTrack = 0x7f0303d7
com.seres.usb.upgrade:macro/m3_comp_time_picker_headline_type = 0x7f0c0152
com.seres.usb.upgrade:attr/track = 0x7f0304a0
com.seres.usb.upgrade:layout/item_storage_device = 0x7f0b002d
com.seres.usb.upgrade:dimen/material_clock_number_text_size = 0x7f060222
com.seres.usb.upgrade:attr/touchAnchorSide = 0x7f03049e
com.seres.usb.upgrade:id/disableIntraAutoTransition = 0x7f0800a0
com.seres.usb.upgrade:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.seres.usb.upgrade:attr/touchAnchorId = 0x7f03049d
com.seres.usb.upgrade:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1003cf
com.seres.usb.upgrade:attr/tooltipStyle = 0x7f03049a
com.seres.usb.upgrade:dimen/clock_face_margin_start = 0x7f060055
com.seres.usb.upgrade:attr/carousel_touchUp_dampeningFactor = 0x7f0300aa
com.seres.usb.upgrade:attr/toolbarId = 0x7f030494
com.seres.usb.upgrade:attr/collapsingToolbarLayoutStyle = 0x7f0300f0
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f10004f
com.seres.usb.upgrade:attr/titleTextStyle = 0x7f030492
com.seres.usb.upgrade:attr/motionEffect_start = 0x7f030339
com.seres.usb.upgrade:attr/textAppearanceCaption = 0x7f030430
com.seres.usb.upgrade:attr/buttonIconDimen = 0x7f030092
com.seres.usb.upgrade:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f05008f
com.seres.usb.upgrade:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.seres.usb.upgrade:style/TextAppearance.Material3.BodyMedium = 0x7f1001e6
com.seres.usb.upgrade:animator/design_fab_hide_motion_spec = 0x7f020001
com.seres.usb.upgrade:attr/titleMargins = 0x7f03048d
com.seres.usb.upgrade:attr/badgeText = 0x7f030058
com.seres.usb.upgrade:attr/layout_constraintVertical_weight = 0x7f03029a
com.seres.usb.upgrade:attr/titleMarginStart = 0x7f03048b
com.seres.usb.upgrade:color/material_dynamic_primary50 = 0x7f050224
com.seres.usb.upgrade:style/TextAppearance.Material3.DisplaySmall = 0x7f1001ea
com.seres.usb.upgrade:attr/titleMarginEnd = 0x7f03048a
com.seres.usb.upgrade:attr/titleMarginBottom = 0x7f030489
com.seres.usb.upgrade:attr/windowActionModeOverlay = 0x7f0304cd
com.seres.usb.upgrade:dimen/material_clock_period_toggle_vertical_gap = 0x7f060225
com.seres.usb.upgrade:drawable/tooltip_frame_light = 0x7f0700e9
com.seres.usb.upgrade:attr/tintNavigationIcon = 0x7f030483
com.seres.usb.upgrade:attr/chipSpacingHorizontal = 0x7f0300c9
com.seres.usb.upgrade:color/bright_foreground_disabled_material_light = 0x7f050023
com.seres.usb.upgrade:dimen/m3_btn_icon_only_min_width = 0x7f0600d6
com.seres.usb.upgrade:attr/tint = 0x7f030481
com.seres.usb.upgrade:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f10042e
com.seres.usb.upgrade:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1002a4
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant10 = 0x7f050110
com.seres.usb.upgrade:attr/circleRadius = 0x7f0300d1
com.seres.usb.upgrade:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f10032d
com.seres.usb.upgrade:attr/textAppearanceLineHeightEnabled = 0x7f030441
com.seres.usb.upgrade:attr/tickRadiusActive = 0x7f03047e
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f10006a
com.seres.usb.upgrade:attr/region_widthLessThan = 0x7f030398
com.seres.usb.upgrade:anim/abc_fade_in = 0x7f010000
com.seres.usb.upgrade:id/cancel_button = 0x7f08006c
com.seres.usb.upgrade:attr/thumbIconSize = 0x7f03046f
com.seres.usb.upgrade:attr/tickColorActive = 0x7f030479
com.seres.usb.upgrade:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602b9
com.seres.usb.upgrade:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1000b5
com.seres.usb.upgrade:color/m3_ref_palette_neutral12 = 0x7f0500fa
com.seres.usb.upgrade:attr/tickColor = 0x7f030478
com.seres.usb.upgrade:attr/visibilityMode = 0x7f0304c2
com.seres.usb.upgrade:color/material_slider_thumb_color = 0x7f050293
com.seres.usb.upgrade:color/material_dynamic_tertiary20 = 0x7f05023b
com.seres.usb.upgrade:style/Widget.Material3.Chip.Filter.Elevated = 0x7f100366
com.seres.usb.upgrade:attr/thumbIconTintMode = 0x7f030471
com.seres.usb.upgrade:attr/thumbTextPadding = 0x7f030475
com.seres.usb.upgrade:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a8
com.seres.usb.upgrade:attr/floatingActionButtonLargeStyle = 0x7f0301da
com.seres.usb.upgrade:id/bounce = 0x7f08005e
com.seres.usb.upgrade:attr/thumbStrokeWidth = 0x7f030474
com.seres.usb.upgrade:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007f
com.seres.usb.upgrade:color/design_fab_shadow_end_color = 0x7f05004d
com.seres.usb.upgrade:attr/trackDecoration = 0x7f0304a5
com.seres.usb.upgrade:attr/thumbElevation = 0x7f03046d
com.seres.usb.upgrade:styleable/MaterialCalendarItem = 0x7f110052
com.seres.usb.upgrade:dimen/m3_card_stroke_width = 0x7f0600ec
com.seres.usb.upgrade:color/m3_text_button_ripple_color_selector = 0x7f0501ec
com.seres.usb.upgrade:attr/transitionShapeAppearance = 0x7f0304b1
com.seres.usb.upgrade:attr/iconifiedByDefault = 0x7f03022c
com.seres.usb.upgrade:attr/paddingRightSystemWindowInsets = 0x7f030361
com.seres.usb.upgrade:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0055
com.seres.usb.upgrade:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602a4
com.seres.usb.upgrade:attr/shapeAppearanceCornerMedium = 0x7f0303b3
com.seres.usb.upgrade:attr/circularflow_angles = 0x7f0300d3
com.seres.usb.upgrade:attr/thickness = 0x7f03046b
com.seres.usb.upgrade:string/abc_toolbar_collapse_description = 0x7f0f001a
com.seres.usb.upgrade:attr/closeIconTint = 0x7f0300e3
com.seres.usb.upgrade:attr/colorOnError = 0x7f0300fe
com.seres.usb.upgrade:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060238
com.seres.usb.upgrade:color/m3_ref_palette_primary10 = 0x7f05011d
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007f
com.seres.usb.upgrade:attr/attributeName = 0x7f03003b
com.seres.usb.upgrade:id/textinput_prefix_text = 0x7f0801d0
com.seres.usb.upgrade:attr/motionProgress = 0x7f030341
com.seres.usb.upgrade:id/navigation_header_container = 0x7f080138
com.seres.usb.upgrade:dimen/m3_navigation_item_icon_padding = 0x7f0601ba
com.seres.usb.upgrade:color/dim_foreground_disabled_material_dark = 0x7f050056
com.seres.usb.upgrade:attr/tickMark = 0x7f03047b
com.seres.usb.upgrade:attr/textureBlurFactor = 0x7f030466
com.seres.usb.upgrade:attr/textStartPadding = 0x7f030465
com.seres.usb.upgrade:attr/textOutlineThickness = 0x7f030462
com.seres.usb.upgrade:color/mtrl_chip_background_color = 0x7f0502a5
com.seres.usb.upgrade:attr/textOutlineColor = 0x7f030461
com.seres.usb.upgrade:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f100390
com.seres.usb.upgrade:color/material_deep_teal_200 = 0x7f050201
com.seres.usb.upgrade:attr/textInputOutlinedDenseStyle = 0x7f03045c
com.seres.usb.upgrade:id/accessibility_custom_action_21 = 0x7f08001e
com.seres.usb.upgrade:attr/textInputLayoutFocusedRectEnabled = 0x7f03045b
com.seres.usb.upgrade:styleable/ThemeEnforcement = 0x7f11008d
com.seres.usb.upgrade:layout/design_navigation_item_header = 0x7f0b0026
com.seres.usb.upgrade:attr/materialSearchViewToolbarStyle = 0x7f0302f9
com.seres.usb.upgrade:string/mtrl_checkbox_button_icon_path_group_name = 0x7f0f005f
com.seres.usb.upgrade:attr/textInputFilledExposedDropdownMenuStyle = 0x7f030459
com.seres.usb.upgrade:style/Widget.Design.TextInputLayout = 0x7f10033a
com.seres.usb.upgrade:anim/abc_tooltip_exit = 0x7f01000b
com.seres.usb.upgrade:dimen/mtrl_slider_widget_height = 0x7f0602e7
com.seres.usb.upgrade:attr/textFillColor = 0x7f030457
com.seres.usb.upgrade:attr/itemShapeInsetBottom = 0x7f030252
com.seres.usb.upgrade:attr/collapsingToolbarLayoutMediumSize = 0x7f0300ee
com.seres.usb.upgrade:attr/shapeAppearanceCornerExtraSmall = 0x7f0303b1
com.seres.usb.upgrade:styleable/RangeSlider = 0x7f110073
com.seres.usb.upgrade:attr/textColorAlertDialogListItem = 0x7f030454
com.seres.usb.upgrade:styleable/KeyFrame = 0x7f110041
com.seres.usb.upgrade:attr/listPreferredItemPaddingEnd = 0x7f0302c4
com.seres.usb.upgrade:color/m3_icon_button_icon_color_selector = 0x7f05008e
com.seres.usb.upgrade:attr/textBackgroundZoom = 0x7f030453
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant20 = 0x7f050112
com.seres.usb.upgrade:attr/textBackgroundRotate = 0x7f030452
com.seres.usb.upgrade:attr/textBackgroundPanY = 0x7f030451
com.seres.usb.upgrade:color/m3_dynamic_dark_hint_foreground = 0x7f05007f
com.seres.usb.upgrade:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
com.seres.usb.upgrade:attr/textBackgroundPanX = 0x7f030450
com.seres.usb.upgrade:attr/textAppearanceTitleLarge = 0x7f03044c
com.seres.usb.upgrade:attr/textAppearanceSubtitle1 = 0x7f03044a
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker = 0x7f1003aa
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1002c5
com.seres.usb.upgrade:attr/textAppearanceListItemSmall = 0x7f030444
com.seres.usb.upgrade:color/m3_dark_default_color_primary_text = 0x7f050075
com.seres.usb.upgrade:attr/thumbStrokeColor = 0x7f030473
com.seres.usb.upgrade:attr/barrierDirection = 0x7f030065
com.seres.usb.upgrade:dimen/m3_btn_text_btn_padding_left = 0x7f0600e0
com.seres.usb.upgrade:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
com.seres.usb.upgrade:attr/textAppearanceLabelSmall = 0x7f03043f
com.seres.usb.upgrade:attr/errorIconTint = 0x7f0301b1
com.seres.usb.upgrade:attr/textAppearanceLabelLarge = 0x7f03043d
com.seres.usb.upgrade:dimen/design_bottom_navigation_text_size = 0x7f06006a
com.seres.usb.upgrade:color/m3_ref_palette_neutral0 = 0x7f0500f7
com.seres.usb.upgrade:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f100458
com.seres.usb.upgrade:id/touch_outside = 0x7f0801d9
com.seres.usb.upgrade:dimen/m3_sys_elevation_level5 = 0x7f0601ee
com.seres.usb.upgrade:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050090
com.seres.usb.upgrade:attr/textAppearanceHeadlineLarge = 0x7f03043a
com.seres.usb.upgrade:attr/flow_horizontalAlign = 0x7f0301eb
com.seres.usb.upgrade:dimen/design_snackbar_max_width = 0x7f060083
com.seres.usb.upgrade:attr/textAppearanceHeadline6 = 0x7f030439
com.seres.usb.upgrade:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070035
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700ce
com.seres.usb.upgrade:attr/textAppearanceHeadline4 = 0x7f030437
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f100026
com.seres.usb.upgrade:attr/layout_constraintHeight_percent = 0x7f030288
com.seres.usb.upgrade:macro/m3_comp_outlined_card_container_color = 0x7f0c00aa
com.seres.usb.upgrade:dimen/mtrl_calendar_header_selection_line_height = 0x7f06027c
com.seres.usb.upgrade:attr/textAppearanceHeadline2 = 0x7f030435
com.seres.usb.upgrade:color/m3_dynamic_dark_default_color_primary_text = 0x7f05007c
com.seres.usb.upgrade:attr/imagePanY = 0x7f030231
com.seres.usb.upgrade:attr/thumbTintMode = 0x7f030477
com.seres.usb.upgrade:id/none = 0x7f08013e
com.seres.usb.upgrade:color/primary_dark_material_light = 0x7f0502d3
com.seres.usb.upgrade:attr/textAppearanceHeadline1 = 0x7f030434
com.seres.usb.upgrade:attr/badgeShapeAppearance = 0x7f030055
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601f3
com.seres.usb.upgrade:attr/textAppearanceDisplaySmall = 0x7f030433
com.seres.usb.upgrade:attr/textAppearanceDisplayLarge = 0x7f030431
com.seres.usb.upgrade:id/action_mode_bar_stub = 0x7f080041
com.seres.usb.upgrade:attr/transitionDisable = 0x7f0304ad
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601f2
com.seres.usb.upgrade:dimen/mtrl_calendar_year_horizontal_padding = 0x7f060291
com.seres.usb.upgrade:attr/flow_firstHorizontalBias = 0x7f0301e7
com.seres.usb.upgrade:id/expand_activities_button = 0x7f0800ba
com.seres.usb.upgrade:attr/minHeight = 0x7f03030e
com.seres.usb.upgrade:attr/expandedTitleMarginStart = 0x7f0301bd
com.seres.usb.upgrade:attr/textAppearanceButton = 0x7f03042f
com.seres.usb.upgrade:attr/headerLayout = 0x7f030210
com.seres.usb.upgrade:attr/textAppearanceBodySmall = 0x7f03042e
com.seres.usb.upgrade:color/m3_ref_palette_neutral4 = 0x7f050100
com.seres.usb.upgrade:color/mtrl_chip_surface_color = 0x7f0502a7
com.seres.usb.upgrade:attr/controlBackground = 0x7f030148
com.seres.usb.upgrade:attr/textAppearanceBodyMedium = 0x7f03042d
com.seres.usb.upgrade:attr/errorEnabled = 0x7f0301af
com.seres.usb.upgrade:dimen/material_helper_text_font_1_3_padding_top = 0x7f060237
com.seres.usb.upgrade:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1000a1
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents = 0x7f100065
com.seres.usb.upgrade:attr/transitionPathRotate = 0x7f0304b0
com.seres.usb.upgrade:attr/textAppearanceBody2 = 0x7f03042b
com.seres.usb.upgrade:color/material_personalized_color_on_surface_inverse = 0x7f05026a
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0012
com.seres.usb.upgrade:id/tvServiceStatus = 0x7f0801e5
com.seres.usb.upgrade:attr/textAppearanceBody1 = 0x7f03042a
com.seres.usb.upgrade:attr/textAllCaps = 0x7f030429
com.seres.usb.upgrade:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f100234
com.seres.usb.upgrade:dimen/material_helper_text_default_padding_top = 0x7f060235
com.seres.usb.upgrade:dimen/tooltip_margin = 0x7f060315
com.seres.usb.upgrade:attr/colorSurfaceContainerLowest = 0x7f030125
com.seres.usb.upgrade:attr/tabUnboundedRipple = 0x7f030424
com.seres.usb.upgrade:color/m3_card_foreground_color = 0x7f05006b
com.seres.usb.upgrade:attr/tabTextColor = 0x7f030423
com.seres.usb.upgrade:attr/constraintRotate = 0x7f030130
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f100182
com.seres.usb.upgrade:attr/tabTextAppearance = 0x7f030422
com.seres.usb.upgrade:attr/tabPaddingEnd = 0x7f03041a
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f10003c
com.seres.usb.upgrade:attr/minWidth = 0x7f030312
com.seres.usb.upgrade:attr/tabPaddingBottom = 0x7f030419
com.seres.usb.upgrade:attr/scrimBackground = 0x7f0303a3
com.seres.usb.upgrade:attr/percentHeight = 0x7f030370
com.seres.usb.upgrade:attr/actionMenuTextAppearance = 0x7f030010
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500d4
com.seres.usb.upgrade:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f06018f
com.seres.usb.upgrade:attr/tabInlineLabel = 0x7f030414
com.seres.usb.upgrade:attr/tabIndicatorColor = 0x7f030410
com.seres.usb.upgrade:attr/minSeparation = 0x7f030310
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f100098
com.seres.usb.upgrade:attr/tabIndicatorAnimationMode = 0x7f03040f
com.seres.usb.upgrade:macro/m3_comp_circular_progress_indicator_active_indicator_color = 0x7f0c000d
com.seres.usb.upgrade:attr/title = 0x7f030484
com.seres.usb.upgrade:attr/maxVelocity = 0x7f030307
com.seres.usb.upgrade:attr/switchTextAppearance = 0x7f030407
com.seres.usb.upgrade:attr/materialCalendarHeaderToggleButton = 0x7f0302e2
com.seres.usb.upgrade:attr/tabMinWidth = 0x7f030416
com.seres.usb.upgrade:attr/shapeAppearanceCornerExtraLarge = 0x7f0303b0
com.seres.usb.upgrade:attr/switchMinWidth = 0x7f030404
com.seres.usb.upgrade:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f06029d
com.seres.usb.upgrade:integer/m3_sys_motion_duration_short4 = 0x7f09001e
com.seres.usb.upgrade:attr/progressBarPadding = 0x7f030384
com.seres.usb.upgrade:attr/suffixTextAppearance = 0x7f030401
com.seres.usb.upgrade:attr/suffixText = 0x7f030400
com.seres.usb.upgrade:dimen/mtrl_calendar_header_content_padding = 0x7f060277
com.seres.usb.upgrade:attr/showDelay = 0x7f0303be
com.seres.usb.upgrade:attr/switchStyle = 0x7f030406
com.seres.usb.upgrade:attr/subtitleTextStyle = 0x7f0303ff
com.seres.usb.upgrade:drawable/mtrl_ic_cancel = 0x7f0700c2
com.seres.usb.upgrade:attr/subtitleTextAppearance = 0x7f0303fd
com.seres.usb.upgrade:color/m3_timepicker_display_ripple_color = 0x7f0501f7
com.seres.usb.upgrade:attr/trackColorActive = 0x7f0304a2
com.seres.usb.upgrade:attr/subtitleCentered = 0x7f0303fc
com.seres.usb.upgrade:attr/fabSize = 0x7f0301d1
com.seres.usb.upgrade:attr/tabPaddingStart = 0x7f03041b
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06010f
com.seres.usb.upgrade:styleable/AppCompatEmojiHelper = 0x7f11000d
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f10015c
com.seres.usb.upgrade:attr/chipSpacingVertical = 0x7f0300ca
com.seres.usb.upgrade:attr/tickColorInactive = 0x7f03047a
com.seres.usb.upgrade:id/scroll = 0x7f08017c
com.seres.usb.upgrade:attr/subMenuArrow = 0x7f0303f5
com.seres.usb.upgrade:dimen/mtrl_progress_circular_size_small = 0x7f0602d5
com.seres.usb.upgrade:attr/strokeWidth = 0x7f0303f4
com.seres.usb.upgrade:attr/strokeColor = 0x7f0303f3
com.seres.usb.upgrade:color/material_dynamic_neutral99 = 0x7f050210
com.seres.usb.upgrade:id/mtrl_calendar_text_input_frame = 0x7f080121
com.seres.usb.upgrade:attr/chipIconTint = 0x7f0300c4
com.seres.usb.upgrade:attr/statusBarScrim = 0x7f0303f2
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ButtonBar = 0x7f1000d4
com.seres.usb.upgrade:attr/state_lifted = 0x7f0303ee
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f10039b
com.seres.usb.upgrade:attr/state_liftable = 0x7f0303ed
com.seres.usb.upgrade:drawable/notification_bg_low_pressed = 0x7f0700dd
com.seres.usb.upgrade:styleable/BottomSheetBehavior_Layout = 0x7f110017
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f100037
com.seres.usb.upgrade:drawable/ic_clock_black_24dp = 0x7f070090
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0159
com.seres.usb.upgrade:attr/layout_constraintCircleRadius = 0x7f03027d
com.seres.usb.upgrade:attr/passwordToggleDrawable = 0x7f03036a
com.seres.usb.upgrade:attr/colorSurfaceContainerHigh = 0x7f030122
com.seres.usb.upgrade:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ee
com.seres.usb.upgrade:id/design_navigation_view = 0x7f08009b
com.seres.usb.upgrade:dimen/m3_carousel_extra_small_item_size = 0x7f0600ee
com.seres.usb.upgrade:attr/startIconDrawable = 0x7f0303e2
com.seres.usb.upgrade:color/m3_ref_palette_error90 = 0x7f0500f4
com.seres.usb.upgrade:string/mtrl_picker_date_header_title = 0x7f0f0074
com.seres.usb.upgrade:attr/onStateTransition = 0x7f030359
com.seres.usb.upgrade:attr/colorOnSecondaryContainer = 0x7f030106
com.seres.usb.upgrade:attr/startIconContentDescription = 0x7f0303e1
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Light = 0x7f100096
com.seres.usb.upgrade:attr/path_percent = 0x7f03036f
com.seres.usb.upgrade:attr/colorContainer = 0x7f0300f5
com.seres.usb.upgrade:string/bottomsheet_drag_handle_content_description = 0x7f0f0023
com.seres.usb.upgrade:color/m3_dynamic_dark_default_color_secondary_text = 0x7f05007d
com.seres.usb.upgrade:color/m3_textfield_label_color = 0x7f0501f0
com.seres.usb.upgrade:styleable/Motion = 0x7f110062
com.seres.usb.upgrade:attr/stackFromEnd = 0x7f0303de
com.seres.usb.upgrade:dimen/design_fab_size_normal = 0x7f060072
com.seres.usb.upgrade:attr/srcCompat = 0x7f0303dd
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014e
com.seres.usb.upgrade:attr/springStopThreshold = 0x7f0303dc
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_primary = 0x7f05017d
com.seres.usb.upgrade:attr/fastScrollHorizontalTrackDrawable = 0x7f0301d4
com.seres.usb.upgrade:attr/motion_postLayoutCollision = 0x7f030344
com.seres.usb.upgrade:attr/springStiffness = 0x7f0303db
com.seres.usb.upgrade:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f06017e
com.seres.usb.upgrade:integer/m3_chip_anim_duration = 0x7f09000e
com.seres.usb.upgrade:attr/springMass = 0x7f0303da
com.seres.usb.upgrade:attr/popupMenuBackground = 0x7f03037b
com.seres.usb.upgrade:string/m3_ref_typeface_plain_regular = 0x7f0f003b
com.seres.usb.upgrade:attr/springBoundary = 0x7f0303d8
com.seres.usb.upgrade:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602a0
com.seres.usb.upgrade:attr/clockFaceBackgroundColor = 0x7f0300da
com.seres.usb.upgrade:attr/spinnerStyle = 0x7f0303d6
com.seres.usb.upgrade:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
com.seres.usb.upgrade:attr/state_indeterminate = 0x7f0303ec
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f05018e
com.seres.usb.upgrade:attr/sizePercent = 0x7f0303ce
com.seres.usb.upgrade:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f060278
com.seres.usb.upgrade:dimen/design_navigation_icon_padding = 0x7f060076
com.seres.usb.upgrade:attr/singleSelection = 0x7f0303cd
com.seres.usb.upgrade:attr/singleChoiceItemLayout = 0x7f0303cb
com.seres.usb.upgrade:color/material_on_primary_emphasis_medium = 0x7f050255
com.seres.usb.upgrade:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.seres.usb.upgrade:attr/simpleItemSelectedRippleColor = 0x7f0303c9
com.seres.usb.upgrade:attr/actionModePopupWindowStyle = 0x7f03001a
com.seres.usb.upgrade:attr/titlePositionInterpolator = 0x7f03048e
com.seres.usb.upgrade:mipmap/ic_launcher_round = 0x7f0d0001
com.seres.usb.upgrade:attr/materialSearchViewStyle = 0x7f0302f7
com.seres.usb.upgrade:attr/simpleItemSelectedColor = 0x7f0303c8
com.seres.usb.upgrade:attr/simpleItemLayout = 0x7f0303c7
com.seres.usb.upgrade:drawable/mtrl_popupmenu_background = 0x7f0700c9
com.seres.usb.upgrade:color/material_dynamic_secondary20 = 0x7f05022e
com.seres.usb.upgrade:attr/spinBars = 0x7f0303d4
com.seres.usb.upgrade:color/m3_sys_color_dark_on_tertiary_container = 0x7f05015c
com.seres.usb.upgrade:attr/sideSheetDialogTheme = 0x7f0303c5
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f1003ec
com.seres.usb.upgrade:attr/showTitle = 0x7f0303c3
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1003a3
com.seres.usb.upgrade:animator/fragment_fade_enter = 0x7f020005
com.seres.usb.upgrade:color/m3_navigation_item_icon_tint = 0x7f050093
com.seres.usb.upgrade:id/tabMode = 0x7f0801b4
com.seres.usb.upgrade:color/m3_dark_default_color_secondary_text = 0x7f050076
com.seres.usb.upgrade:attr/showText = 0x7f0303c2
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0083
com.seres.usb.upgrade:attr/showMotionSpec = 0x7f0303c0
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500d2
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f10006c
com.seres.usb.upgrade:id/on = 0x7f080145
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary95 = 0x7f0500ce
com.seres.usb.upgrade:attr/showDividers = 0x7f0303bf
com.seres.usb.upgrade:dimen/m3_chip_elevated_elevation = 0x7f0600f7
com.seres.usb.upgrade:attr/subheaderColor = 0x7f0303f6
com.seres.usb.upgrade:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f100048
com.seres.usb.upgrade:attr/endIconScaleType = 0x7f0301a6
com.seres.usb.upgrade:attr/showAsAction = 0x7f0303bd
com.seres.usb.upgrade:styleable/AnimatedStateListDrawableItem = 0x7f110008
com.seres.usb.upgrade:style/Widget.Material3.Button.IconButton.Filled = 0x7f100351
com.seres.usb.upgrade:style/Widget.Material3.AppBarLayout = 0x7f10033d
com.seres.usb.upgrade:id/startToEnd = 0x7f0801ab
com.seres.usb.upgrade:attr/minHideDelay = 0x7f03030f
com.seres.usb.upgrade:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301c7
com.seres.usb.upgrade:attr/navigationContentDescription = 0x7f030348
com.seres.usb.upgrade:dimen/mtrl_calendar_year_vertical_padding = 0x7f060292
com.seres.usb.upgrade:attr/showAnimationBehavior = 0x7f0303bc
com.seres.usb.upgrade:attr/actionBarStyle = 0x7f030007
com.seres.usb.upgrade:color/foreground_material_light = 0x7f05005d
com.seres.usb.upgrade:attr/shapeCornerFamily = 0x7f0303b9
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f100072
com.seres.usb.upgrade:attr/shapeAppearanceSmallComponent = 0x7f0303b8
com.seres.usb.upgrade:dimen/notification_media_narrow_margin = 0x7f06030b
com.seres.usb.upgrade:attr/paddingStartSystemWindowInsets = 0x7f030363
com.seres.usb.upgrade:attr/shapeAppearanceOverlay = 0x7f0303b7
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500e9
com.seres.usb.upgrade:attr/shapeAppearanceMediumComponent = 0x7f0303b6
com.seres.usb.upgrade:xml/data_extraction_rules = 0x7f120001
com.seres.usb.upgrade:id/stop = 0x7f0801af
com.seres.usb.upgrade:color/material_personalized_hint_foreground = 0x7f05028a
com.seres.usb.upgrade:attr/tabSecondaryStyle = 0x7f03041e
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Dark = 0x7f10008a
com.seres.usb.upgrade:id/invisible = 0x7f0800e5
com.seres.usb.upgrade:color/notification_icon_bg_color = 0x7f0502d1
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050176
com.seres.usb.upgrade:attr/useDrawerArrowDrawable = 0x7f0304b8
com.seres.usb.upgrade:id/sin = 0x7f080194
com.seres.usb.upgrade:attr/circularflow_defaultAngle = 0x7f0300d4
com.seres.usb.upgrade:attr/textAppearanceSearchResultSubtitle = 0x7f030447
com.seres.usb.upgrade:attr/sliderStyle = 0x7f0303cf
com.seres.usb.upgrade:id/outward = 0x7f080156
com.seres.usb.upgrade:dimen/mtrl_badge_horizontal_edge_offset = 0x7f060244
com.seres.usb.upgrade:attr/setsTag = 0x7f0303ae
com.seres.usb.upgrade:attr/selectorSize = 0x7f0303ad
com.seres.usb.upgrade:id/labeled = 0x7f0800ec
com.seres.usb.upgrade:attr/panelBackground = 0x7f030366
com.seres.usb.upgrade:id/btnOpenDevice = 0x7f080063
com.seres.usb.upgrade:color/m3_ref_palette_neutral70 = 0x7f050105
com.seres.usb.upgrade:style/Widget.Design.CollapsingToolbar = 0x7f100333
com.seres.usb.upgrade:attr/selectionRequired = 0x7f0303ac
com.seres.usb.upgrade:color/m3_fab_efab_background_color_selector = 0x7f050088
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1002d9
com.seres.usb.upgrade:attr/selectableItemBackground = 0x7f0303aa
com.seres.usb.upgrade:attr/listPreferredItemHeightLarge = 0x7f0302c2
com.seres.usb.upgrade:attr/seekBarStyle = 0x7f0303a9
com.seres.usb.upgrade:color/m3_sys_color_dark_secondary = 0x7f050161
com.seres.usb.upgrade:attr/searchViewStyle = 0x7f0303a8
com.seres.usb.upgrade:attr/scaleFromTextSize = 0x7f0303a1
com.seres.usb.upgrade:color/m3_sys_color_light_surface_variant = 0x7f0501d5
com.seres.usb.upgrade:attr/colorSecondaryFixed = 0x7f03011c
com.seres.usb.upgrade:color/m3_checkbox_button_tint = 0x7f05006f
com.seres.usb.upgrade:attr/saturation = 0x7f0303a0
com.seres.usb.upgrade:attr/roundPercent = 0x7f03039f
com.seres.usb.upgrade:attr/rippleColor = 0x7f03039c
com.seres.usb.upgrade:attr/state_dragged = 0x7f0303ea
com.seres.usb.upgrade:id/transition_current_scene = 0x7f0801dc
com.seres.usb.upgrade:attr/region_widthMoreThan = 0x7f030399
com.seres.usb.upgrade:attr/materialDividerStyle = 0x7f0302f0
com.seres.usb.upgrade:attr/flow_padding = 0x7f0301f4
com.seres.usb.upgrade:attr/reactiveGuide_valueId = 0x7f030394
com.seres.usb.upgrade:drawable/abc_btn_borderless_material = 0x7f07002b
com.seres.usb.upgrade:color/material_grey_100 = 0x7f050245
com.seres.usb.upgrade:attr/reactiveGuide_applyToConstraintSet = 0x7f030393
com.seres.usb.upgrade:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070059
com.seres.usb.upgrade:attr/ratingBarStyle = 0x7f03038e
com.seres.usb.upgrade:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.seres.usb.upgrade:dimen/mtrl_badge_size = 0x7f060246
com.seres.usb.upgrade:attr/boxCornerRadiusTopStart = 0x7f030084
com.seres.usb.upgrade:color/black = 0x7f050021
com.seres.usb.upgrade:dimen/m3_sys_elevation_level4 = 0x7f0601ed
com.seres.usb.upgrade:attr/radioButtonStyle = 0x7f03038c
com.seres.usb.upgrade:attr/chipIconSize = 0x7f0300c3
com.seres.usb.upgrade:attr/defaultState = 0x7f030173
com.seres.usb.upgrade:color/m3_ref_palette_secondary30 = 0x7f05012d
com.seres.usb.upgrade:attr/queryHint = 0x7f03038a
com.seres.usb.upgrade:drawable/abc_text_select_handle_left_mtrl = 0x7f07006f
com.seres.usb.upgrade:styleable/AlertDialog = 0x7f110006
com.seres.usb.upgrade:dimen/m3_fab_border_width = 0x7f0601af
com.seres.usb.upgrade:attr/quantizeMotionSteps = 0x7f030388
com.seres.usb.upgrade:attr/mock_showDiagonals = 0x7f030317
com.seres.usb.upgrade:attr/progressBarStyle = 0x7f030385
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f100397
com.seres.usb.upgrade:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1000ad
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f050194
com.seres.usb.upgrade:attr/checkMarkTint = 0x7f0300af
com.seres.usb.upgrade:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601b5
com.seres.usb.upgrade:attr/prefixTextColor = 0x7f030381
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050173
com.seres.usb.upgrade:id/start = 0x7f0801a9
com.seres.usb.upgrade:attr/chipEndPadding = 0x7f0300bf
com.seres.usb.upgrade:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f100286
com.seres.usb.upgrade:dimen/notification_main_column_padding_top = 0x7f06030a
com.seres.usb.upgrade:attr/autoSizeStepGranularity = 0x7f030043
com.seres.usb.upgrade:attr/prefixTextAppearance = 0x7f030380
com.seres.usb.upgrade:attr/prefixText = 0x7f03037f
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f100417
com.seres.usb.upgrade:attr/colorOnPrimaryFixedVariant = 0x7f030103
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Spinner = 0x7f1000f7
com.seres.usb.upgrade:attr/popupMenuStyle = 0x7f03037c
com.seres.usb.upgrade:color/m3_sys_color_dark_inverse_on_surface = 0x7f05014f
com.seres.usb.upgrade:attr/materialCalendarMonthNavigationButton = 0x7f0302e4
com.seres.usb.upgrade:attr/gestureInsetBottomIgnored = 0x7f03020b
com.seres.usb.upgrade:attr/placeholderTextColor = 0x7f030378
com.seres.usb.upgrade:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060180
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f1003fa
com.seres.usb.upgrade:color/m3_sys_color_light_surface = 0x7f0501cd
com.seres.usb.upgrade:color/m3_chip_assist_text_color = 0x7f050070
com.seres.usb.upgrade:attr/placeholderTextAppearance = 0x7f030377
com.seres.usb.upgrade:color/material_grey_50 = 0x7f050247
com.seres.usb.upgrade:color/material_dynamic_secondary90 = 0x7f050235
com.seres.usb.upgrade:color/m3_calendar_item_disabled_text = 0x7f050069
com.seres.usb.upgrade:attr/placeholderText = 0x7f030376
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1001af
com.seres.usb.upgrade:attr/pivotAnchor = 0x7f030375
com.seres.usb.upgrade:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060164
com.seres.usb.upgrade:dimen/cardview_compat_inset_shadow = 0x7f060052
com.seres.usb.upgrade:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.seres.usb.upgrade:attr/triggerReceiver = 0x7f0304b3
com.seres.usb.upgrade:attr/keylines = 0x7f030261
com.seres.usb.upgrade:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f10040d
com.seres.usb.upgrade:attr/textAppearanceHeadlineSmall = 0x7f03043c
com.seres.usb.upgrade:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1001c9
com.seres.usb.upgrade:dimen/m3_badge_with_text_vertical_offset = 0x7f0600ba
com.seres.usb.upgrade:id/group_divider = 0x7f0800d2
com.seres.usb.upgrade:attr/perpendicularPath_percent = 0x7f030374
com.seres.usb.upgrade:attr/framePosition = 0x7f030209
com.seres.usb.upgrade:attr/percentY = 0x7f030373
com.seres.usb.upgrade:string/item_view_role_description = 0x7f0f0036
com.seres.usb.upgrade:animator/m3_chip_state_list_anim = 0x7f02000e
com.seres.usb.upgrade:attr/pathMotionArc = 0x7f03036e
com.seres.usb.upgrade:dimen/abc_control_corner_material = 0x7f060018
com.seres.usb.upgrade:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1000d9
com.seres.usb.upgrade:attr/passwordToggleTint = 0x7f03036c
com.seres.usb.upgrade:attr/percentX = 0x7f030372
com.seres.usb.upgrade:attr/tooltipFrameBackground = 0x7f030499
com.seres.usb.upgrade:color/m3_ref_palette_error95 = 0x7f0500f5
com.seres.usb.upgrade:color/material_personalized_color_outline = 0x7f05026e
com.seres.usb.upgrade:attr/passwordToggleContentDescription = 0x7f030369
com.seres.usb.upgrade:attr/SharedValueId = 0x7f030001
com.seres.usb.upgrade:dimen/material_bottom_sheet_max_width = 0x7f06021a
com.seres.usb.upgrade:color/material_dynamic_secondary70 = 0x7f050233
com.seres.usb.upgrade:attr/layout_constraintStart_toStartOf = 0x7f030293
com.seres.usb.upgrade:id/submenuarrow = 0x7f0801b1
com.seres.usb.upgrade:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017a
com.seres.usb.upgrade:attr/textAppearanceLabelMedium = 0x7f03043e
com.seres.usb.upgrade:attr/panelMenuListTheme = 0x7f030367
com.seres.usb.upgrade:styleable/StateSet = 0x7f110084
com.seres.usb.upgrade:color/m3_ref_palette_neutral98 = 0x7f05010d
com.seres.usb.upgrade:attr/fastScrollEnabled = 0x7f0301d2
com.seres.usb.upgrade:attr/layout_constraintWidth_max = 0x7f03029d
com.seres.usb.upgrade:attr/textAppearanceTitleSmall = 0x7f03044e
com.seres.usb.upgrade:color/design_default_color_primary_variant = 0x7f050048
com.seres.usb.upgrade:string/material_motion_easing_emphasized = 0x7f0f0050
com.seres.usb.upgrade:attr/paddingTopSystemWindowInsets = 0x7f030365
com.seres.usb.upgrade:attr/maxNumber = 0x7f030306
com.seres.usb.upgrade:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.seres.usb.upgrade:color/m3_ref_palette_tertiary20 = 0x7f050139
com.seres.usb.upgrade:attr/paddingTopNoTitle = 0x7f030364
com.seres.usb.upgrade:id/material_timepicker_ok_button = 0x7f08010c
com.seres.usb.upgrade:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f06019e
com.seres.usb.upgrade:attr/motionEffect_translationX = 0x7f03033b
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500e2
com.seres.usb.upgrade:attr/textAppearanceHeadline3 = 0x7f030436
com.seres.usb.upgrade:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f06012a
com.seres.usb.upgrade:attr/paddingStart = 0x7f030362
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070026
com.seres.usb.upgrade:styleable/PopupWindow = 0x7f11006f
com.seres.usb.upgrade:attr/paddingLeftSystemWindowInsets = 0x7f030360
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015b
com.seres.usb.upgrade:attr/tickMarkTintMode = 0x7f03047d
com.seres.usb.upgrade:attr/iconStartPadding = 0x7f030229
com.seres.usb.upgrade:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e8
com.seres.usb.upgrade:id/mtrl_view_tag_bottom_padding = 0x7f080130
com.seres.usb.upgrade:attr/editTextColor = 0x7f030199
com.seres.usb.upgrade:attr/paddingBottomSystemWindowInsets = 0x7f03035e
com.seres.usb.upgrade:dimen/tooltip_y_offset_non_touch = 0x7f060319
com.seres.usb.upgrade:color/m3_button_foreground_color_selector = 0x7f050065
com.seres.usb.upgrade:attr/paddingBottomNoButtons = 0x7f03035d
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f1003f2
com.seres.usb.upgrade:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.seres.usb.upgrade:attr/overlapAnchor = 0x7f03035b
com.seres.usb.upgrade:attr/onShow = 0x7f030358
com.seres.usb.upgrade:dimen/mtrl_switch_track_height = 0x7f0602f2
com.seres.usb.upgrade:color/m3_slider_thumb_color = 0x7f050149
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f10025e
com.seres.usb.upgrade:attr/onPositiveCross = 0x7f030357
com.seres.usb.upgrade:attr/boxBackgroundMode = 0x7f03007f
com.seres.usb.upgrade:attr/itemTextAppearance = 0x7f030259
com.seres.usb.upgrade:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.seres.usb.upgrade:id/neverCompleteToStart = 0x7f08013b
com.seres.usb.upgrade:attr/itemHorizontalPadding = 0x7f030244
com.seres.usb.upgrade:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f10006f
com.seres.usb.upgrade:attr/materialTimePickerTheme = 0x7f0302fd
com.seres.usb.upgrade:attr/onCross = 0x7f030354
com.seres.usb.upgrade:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f10008b
com.seres.usb.upgrade:color/design_icon_tint = 0x7f050054
com.seres.usb.upgrade:attr/numericModifiers = 0x7f030352
com.seres.usb.upgrade:dimen/m3_small_fab_max_image_size = 0x7f0601e5
com.seres.usb.upgrade:color/mtrl_tabs_colored_ripple_color = 0x7f0502c5
com.seres.usb.upgrade:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501b2
com.seres.usb.upgrade:attr/triggerSlack = 0x7f0304b4
com.seres.usb.upgrade:id/center = 0x7f08006e
com.seres.usb.upgrade:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f060195
com.seres.usb.upgrade:anim/design_snackbar_in = 0x7f01001a
com.seres.usb.upgrade:attr/marginHorizontal = 0x7f0302cc
com.seres.usb.upgrade:integer/m3_sys_motion_duration_extra_long3 = 0x7f090011
com.seres.usb.upgrade:attr/state_with_icon = 0x7f0303ef
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f100214
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f10016f
com.seres.usb.upgrade:dimen/material_textinput_max_width = 0x7f06023a
com.seres.usb.upgrade:dimen/design_tab_scrollable_min_width = 0x7f06008a
com.seres.usb.upgrade:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060111
com.seres.usb.upgrade:attr/number = 0x7f030351
com.seres.usb.upgrade:attr/navigationRailStyle = 0x7f03034c
com.seres.usb.upgrade:attr/navigationMode = 0x7f03034b
com.seres.usb.upgrade:style/Widget.Material3.Chip.Input.Icon = 0x7f100369
com.seres.usb.upgrade:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601bb
com.seres.usb.upgrade:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.seres.usb.upgrade:attr/snackbarStyle = 0x7f0303d1
com.seres.usb.upgrade:attr/multiChoiceItemLayout = 0x7f030347
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1002ab
com.seres.usb.upgrade:attr/motion_triggerOnCollision = 0x7f030345
com.seres.usb.upgrade:attr/motionStagger = 0x7f030342
com.seres.usb.upgrade:layout/abc_list_menu_item_layout = 0x7f0b0010
com.seres.usb.upgrade:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.seres.usb.upgrade:color/m3_efab_ripple_color_selector = 0x7f050086
com.seres.usb.upgrade:attr/motionPath = 0x7f03033f
com.seres.usb.upgrade:attr/motionInterpolator = 0x7f03033e
com.seres.usb.upgrade:style/Widget.AppCompat.Spinner.Underlined = 0x7f100329
com.seres.usb.upgrade:attr/motionEffect_move = 0x7f030338
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ImageButton = 0x7f1000dd
com.seres.usb.upgrade:color/material_grey_300 = 0x7f050246
com.seres.usb.upgrade:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b0046
com.seres.usb.upgrade:attr/itemSpacing = 0x7f030256
com.seres.usb.upgrade:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070081
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_track_color = 0x7f0c0141
com.seres.usb.upgrade:attr/motionEffect_alpha = 0x7f030336
com.seres.usb.upgrade:dimen/mtrl_slider_label_radius = 0x7f0602e0
com.seres.usb.upgrade:attr/motionEasingStandardDecelerateInterpolator = 0x7f030334
com.seres.usb.upgrade:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f100336
com.seres.usb.upgrade:dimen/m3_comp_snackbar_container_elevation = 0x7f060183
com.seres.usb.upgrade:attr/enforceMaterialTheme = 0x7f0301a9
com.seres.usb.upgrade:attr/motionEasingStandard = 0x7f030332
com.seres.usb.upgrade:styleable/CoordinatorLayout_Layout = 0x7f11002d
com.seres.usb.upgrade:id/open_search_view_edit_text = 0x7f08014d
com.seres.usb.upgrade:attr/behavior_significantVelocityThreshold = 0x7f030071
com.seres.usb.upgrade:attr/materialCalendarMonth = 0x7f0302e3
com.seres.usb.upgrade:attr/motionEasingEmphasizedInterpolator = 0x7f03032f
com.seres.usb.upgrade:attr/customPixelDimension = 0x7f030168
com.seres.usb.upgrade:dimen/m3_carousel_debug_keyline_width = 0x7f0600ed
com.seres.usb.upgrade:dimen/mtrl_calendar_days_of_week_height = 0x7f060275
com.seres.usb.upgrade:attr/materialCalendarFullscreenTheme = 0x7f0302db
com.seres.usb.upgrade:attr/trackCornerRadius = 0x7f0304a4
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500db
com.seres.usb.upgrade:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1003b1
com.seres.usb.upgrade:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f03032d
com.seres.usb.upgrade:attr/motionEasingEmphasized = 0x7f03032c
com.seres.usb.upgrade:style/Widget.Material3.MaterialDivider = 0x7f1003a8
com.seres.usb.upgrade:attr/motionDurationShort2 = 0x7f030327
com.seres.usb.upgrade:attr/defaultQueryHint = 0x7f030171
com.seres.usb.upgrade:color/material_divider_color = 0x7f050203
com.seres.usb.upgrade:attr/deltaPolarRadius = 0x7f030175
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500aa
com.seres.usb.upgrade:attr/motionDurationShort1 = 0x7f030326
com.seres.usb.upgrade:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060153
com.seres.usb.upgrade:attr/useCompatPadding = 0x7f0304b7
com.seres.usb.upgrade:attr/motionDurationMedium4 = 0x7f030325
com.seres.usb.upgrade:attr/motionDurationMedium2 = 0x7f030323
com.seres.usb.upgrade:attr/motionDurationMedium1 = 0x7f030322
com.seres.usb.upgrade:attr/telltales_velocityMode = 0x7f030428
com.seres.usb.upgrade:attr/circularflow_defaultRadius = 0x7f0300d5
com.seres.usb.upgrade:attr/isMaterial3DynamicColorApplied = 0x7f03023e
com.seres.usb.upgrade:id/action_bar_activity_content = 0x7f080034
com.seres.usb.upgrade:attr/motionEasingStandardAccelerateInterpolator = 0x7f030333
com.seres.usb.upgrade:attr/motionEasingDecelerated = 0x7f03032b
com.seres.usb.upgrade:style/Widget.Material3.Button.OutlinedButton = 0x7f100354
com.seres.usb.upgrade:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602d9
com.seres.usb.upgrade:attr/motionDurationExtraLong3 = 0x7f03031c
com.seres.usb.upgrade:id/allStates = 0x7f08004a
com.seres.usb.upgrade:id/material_minute_text_input = 0x7f080106
com.seres.usb.upgrade:attr/motionDurationExtraLong2 = 0x7f03031b
com.seres.usb.upgrade:attr/motionDurationExtraLong1 = 0x7f03031a
com.seres.usb.upgrade:color/mtrl_navigation_item_text_color = 0x7f0502ba
com.seres.usb.upgrade:attr/motionDebug = 0x7f030319
com.seres.usb.upgrade:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f060232
com.seres.usb.upgrade:attr/materialAlertDialogTitlePanelStyle = 0x7f0302d4
com.seres.usb.upgrade:attr/mock_showLabel = 0x7f030318
com.seres.usb.upgrade:styleable/KeyTrigger = 0x7f110046
com.seres.usb.upgrade:attr/imageRotate = 0x7f030232
com.seres.usb.upgrade:attr/titleCollapseMode = 0x7f030486
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1003a7
com.seres.usb.upgrade:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f060177
com.seres.usb.upgrade:style/Widget.Design.FloatingActionButton = 0x7f100334
com.seres.usb.upgrade:id/fitEnd = 0x7f0800c2
com.seres.usb.upgrade:attr/colorPrimaryDark = 0x7f030114
com.seres.usb.upgrade:attr/textAppearanceBodyLarge = 0x7f03042c
com.seres.usb.upgrade:attr/mock_labelBackgroundColor = 0x7f030315
com.seres.usb.upgrade:attr/yearStyle = 0x7f0304d6
com.seres.usb.upgrade:attr/polarRelativeTo = 0x7f03037a
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_min_height = 0x7f0601c8
com.seres.usb.upgrade:attr/shapeAppearanceCornerSmall = 0x7f0303b4
com.seres.usb.upgrade:attr/mock_diagonalsColor = 0x7f030313
com.seres.usb.upgrade:attr/materialIconButtonOutlinedStyle = 0x7f0302f3
com.seres.usb.upgrade:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.seres.usb.upgrade:attr/actionModeCloseButtonStyle = 0x7f030013
com.seres.usb.upgrade:dimen/m3_chip_icon_size = 0x7f0600f9
com.seres.usb.upgrade:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601d8
com.seres.usb.upgrade:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1002be
com.seres.usb.upgrade:attr/layout_constraintHeight = 0x7f030284
com.seres.usb.upgrade:attr/behavior_peekHeight = 0x7f03006f
com.seres.usb.upgrade:attr/methodName = 0x7f03030d
com.seres.usb.upgrade:color/design_default_color_on_surface = 0x7f050045
com.seres.usb.upgrade:attr/menuGravity = 0x7f03030c
com.seres.usb.upgrade:attr/menuAlignmentMode = 0x7f03030b
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00ff
com.seres.usb.upgrade:attr/measureWithLargestChild = 0x7f030309
com.seres.usb.upgrade:attr/maxWidth = 0x7f030308
com.seres.usb.upgrade:attr/daySelectedStyle = 0x7f03016c
com.seres.usb.upgrade:style/Widget.Material3.BottomAppBar.Legacy = 0x7f100346
com.seres.usb.upgrade:attr/searchHintIcon = 0x7f0303a5
com.seres.usb.upgrade:attr/bottomInsetScrimEnabled = 0x7f030079
com.seres.usb.upgrade:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.seres.usb.upgrade:attr/maxHeight = 0x7f030303
com.seres.usb.upgrade:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f10035b
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary10 = 0x7f0500c4
com.seres.usb.upgrade:drawable/abc_ic_menu_overflow_material = 0x7f070045
com.seres.usb.upgrade:attr/maxButtonHeight = 0x7f030301
com.seres.usb.upgrade:attr/contentInsetStartWithNavigation = 0x7f03013e
com.seres.usb.upgrade:dimen/material_clock_hand_padding = 0x7f060220
com.seres.usb.upgrade:attr/titleMargin = 0x7f030488
com.seres.usb.upgrade:attr/materialTimePickerStyle = 0x7f0302fc
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00be
com.seres.usb.upgrade:attr/fastScrollVerticalThumbDrawable = 0x7f0301d5
com.seres.usb.upgrade:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.seres.usb.upgrade:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0502b5
com.seres.usb.upgrade:attr/nestedScrollViewStyle = 0x7f03034f
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary30 = 0x7f0500c7
com.seres.usb.upgrade:id/animateToStart = 0x7f08004d
com.seres.usb.upgrade:id/mtrl_picker_text_input_date = 0x7f08012c
com.seres.usb.upgrade:color/abc_color_highlight_material = 0x7f050004
com.seres.usb.upgrade:color/m3_sys_color_light_on_background = 0x7f0501bc
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f100441
com.seres.usb.upgrade:animator/m3_card_state_list_anim = 0x7f02000d
com.seres.usb.upgrade:style/Base.V22.Theme.AppCompat.Light = 0x7f1000af
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c0140
com.seres.usb.upgrade:color/primary_text_disabled_material_dark = 0x7f0502d8
com.seres.usb.upgrade:attr/materialSearchViewPrefixStyle = 0x7f0302f6
com.seres.usb.upgrade:attr/materialIconButtonStyle = 0x7f0302f4
com.seres.usb.upgrade:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f100411
com.seres.usb.upgrade:attr/foregroundInsidePadding = 0x7f030208
com.seres.usb.upgrade:attr/materialIconButtonFilledTonalStyle = 0x7f0302f2
com.seres.usb.upgrade:style/Theme.Design = 0x7f100222
com.seres.usb.upgrade:dimen/m3_card_elevation = 0x7f0600ea
com.seres.usb.upgrade:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f100407
com.seres.usb.upgrade:color/m3_checkbox_button_icon_tint = 0x7f05006e
com.seres.usb.upgrade:drawable/abc_ratingbar_material = 0x7f07005c
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f060141
com.seres.usb.upgrade:dimen/abc_action_button_min_height_material = 0x7f06000d
com.seres.usb.upgrade:attr/materialCircleRadius = 0x7f0302ec
com.seres.usb.upgrade:attr/materialCardViewStyle = 0x7f0302eb
com.seres.usb.upgrade:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.seres.usb.upgrade:drawable/mtrl_checkbox_button = 0x7f0700b4
com.seres.usb.upgrade:attr/listChoiceIndicatorSingleAnimated = 0x7f0302bb
com.seres.usb.upgrade:attr/materialCardViewFilledStyle = 0x7f0302e9
com.seres.usb.upgrade:attr/materialCalendarYearNavigationButton = 0x7f0302e7
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500a2
com.seres.usb.upgrade:id/showTitle = 0x7f080193
com.seres.usb.upgrade:attr/materialCalendarTheme = 0x7f0302e6
com.seres.usb.upgrade:attr/materialCalendarStyle = 0x7f0302e5
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary0 = 0x7f0500c3
com.seres.usb.upgrade:attr/itemTextAppearanceActive = 0x7f03025a
com.seres.usb.upgrade:attr/activityChooserViewStyle = 0x7f030027
com.seres.usb.upgrade:attr/materialCalendarHeaderDivider = 0x7f0302de
com.seres.usb.upgrade:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602b4
com.seres.usb.upgrade:attr/materialCalendarHeaderConfirmButton = 0x7f0302dd
com.seres.usb.upgrade:attr/layout_constraintHeight_min = 0x7f030287
com.seres.usb.upgrade:attr/reverseLayout = 0x7f03039b
com.seres.usb.upgrade:attr/materialCalendarHeaderCancelButton = 0x7f0302dc
com.seres.usb.upgrade:attr/tabContentStart = 0x7f030409
com.seres.usb.upgrade:attr/materialCalendarDayOfWeekLabel = 0x7f0302da
com.seres.usb.upgrade:attr/materialButtonOutlinedStyle = 0x7f0302d6
com.seres.usb.upgrade:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.seres.usb.upgrade:attr/startIconMinSize = 0x7f0303e3
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500d3
com.seres.usb.upgrade:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302d1
com.seres.usb.upgrade:attr/marginTopSystemWindowInsets = 0x7f0302cf
com.seres.usb.upgrade:color/material_on_surface_emphasis_high_type = 0x7f050257
com.seres.usb.upgrade:attr/tabIndicatorHeight = 0x7f030413
com.seres.usb.upgrade:color/design_box_stroke_color = 0x7f050031
com.seres.usb.upgrade:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090024
com.seres.usb.upgrade:color/material_personalized_color_secondary_container = 0x7f050276
com.seres.usb.upgrade:attr/indeterminateAnimationType = 0x7f030234
com.seres.usb.upgrade:attr/marginLeftSystemWindowInsets = 0x7f0302cd
com.seres.usb.upgrade:drawable/abc_ratingbar_indicator_material = 0x7f07005b
com.seres.usb.upgrade:attr/buttonIcon = 0x7f030091
com.seres.usb.upgrade:attr/buttonBarPositiveButtonStyle = 0x7f03008d
com.seres.usb.upgrade:attr/SharedValue = 0x7f030000
com.seres.usb.upgrade:id/transition_position = 0x7f0801de
com.seres.usb.upgrade:attr/titleTextColor = 0x7f030490
com.seres.usb.upgrade:attr/logoScaleType = 0x7f0302cb
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_primary = 0x7f05019b
com.seres.usb.upgrade:id/material_timepicker_container = 0x7f08010a
com.seres.usb.upgrade:attr/windowFixedWidthMajor = 0x7f0304d0
com.seres.usb.upgrade:styleable/TextInputEditText = 0x7f11008b
com.seres.usb.upgrade:attr/maxImageSize = 0x7f030304
com.seres.usb.upgrade:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060122
com.seres.usb.upgrade:animator/mtrl_chip_state_list_anim = 0x7f020018
com.seres.usb.upgrade:attr/spinnerDropDownItemStyle = 0x7f0303d5
com.seres.usb.upgrade:attr/bottomSheetStyle = 0x7f03007d
com.seres.usb.upgrade:color/mtrl_outlined_stroke_color = 0x7f0502be
com.seres.usb.upgrade:dimen/m3_comp_switch_disabled_track_opacity = 0x7f06018b
com.seres.usb.upgrade:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302ba
com.seres.usb.upgrade:dimen/m3_extended_fab_end_padding = 0x7f0601aa
com.seres.usb.upgrade:attr/logoAdjustViewBounds = 0x7f0302c9
com.seres.usb.upgrade:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0600fd
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_container_color = 0x7f0c0086
com.seres.usb.upgrade:drawable/mtrl_ic_arrow_drop_up = 0x7f0700c1
com.seres.usb.upgrade:attr/logo = 0x7f0302c8
com.seres.usb.upgrade:style/Widget.Material3.CompoundButton.Switch = 0x7f100378
com.seres.usb.upgrade:attr/hintTextAppearance = 0x7f03021d
com.seres.usb.upgrade:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.seres.usb.upgrade:string/call_notification_ongoing_text = 0x7f0f0029
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cd
com.seres.usb.upgrade:attr/contentPaddingRight = 0x7f030143
com.seres.usb.upgrade:attr/listPreferredItemPaddingStart = 0x7f0302c7
com.seres.usb.upgrade:id/fullscreen_header = 0x7f0800cc
com.seres.usb.upgrade:attr/listPreferredItemPaddingLeft = 0x7f0302c5
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f10016d
com.seres.usb.upgrade:id/search_go_btn = 0x7f080186
com.seres.usb.upgrade:attr/state_error = 0x7f0303eb
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant60 = 0x7f050116
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
com.seres.usb.upgrade:attr/actionMenuTextColor = 0x7f030011
com.seres.usb.upgrade:attr/listPreferredItemHeightSmall = 0x7f0302c3
com.seres.usb.upgrade:dimen/design_snackbar_elevation = 0x7f060081
com.seres.usb.upgrade:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.seres.usb.upgrade:attr/checkedIconSize = 0x7f0300b8
com.seres.usb.upgrade:attr/lineSpacing = 0x7f0302b7
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0132
com.seres.usb.upgrade:attr/preserveIconSpacing = 0x7f030382
com.seres.usb.upgrade:attr/motionDurationLong3 = 0x7f030320
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000e
com.seres.usb.upgrade:attr/layout_goneMarginTop = 0x7f0302a9
com.seres.usb.upgrade:attr/layout_constraintVertical_bias = 0x7f030298
com.seres.usb.upgrade:integer/m3_sys_motion_duration_medium1 = 0x7f090017
com.seres.usb.upgrade:attr/trackDecorationTintMode = 0x7f0304a7
com.seres.usb.upgrade:color/m3_card_stroke_color = 0x7f05006d
com.seres.usb.upgrade:attr/layout_wrapBehaviorInParent = 0x7f0302b1
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_outline_variant = 0x7f05019a
com.seres.usb.upgrade:attr/layout_goneMarginBaseline = 0x7f0302a3
com.seres.usb.upgrade:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f06023e
com.seres.usb.upgrade:color/material_dynamic_tertiary50 = 0x7f05023e
com.seres.usb.upgrade:attr/cornerSizeBottomLeft = 0x7f030152
com.seres.usb.upgrade:attr/limitBoundsTo = 0x7f0302b5
com.seres.usb.upgrade:attr/cornerFamilyBottomLeft = 0x7f03014c
com.seres.usb.upgrade:attr/actionModeStyle = 0x7f03001e
com.seres.usb.upgrade:dimen/m3_comp_time_picker_container_elevation = 0x7f06019a
com.seres.usb.upgrade:attr/layout_constraintTop_toTopOf = 0x7f030297
com.seres.usb.upgrade:color/mtrl_switch_track_decoration_tint = 0x7f0502c3
com.seres.usb.upgrade:attr/layout_constraintTop_toBottomOf = 0x7f030296
com.seres.usb.upgrade:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f03045d
com.seres.usb.upgrade:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c010b
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700d1
com.seres.usb.upgrade:attr/ratingBarStyleSmall = 0x7f030390
com.seres.usb.upgrade:attr/customColorValue = 0x7f030163
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Large = 0x7f10019f
com.seres.usb.upgrade:dimen/mtrl_navigation_rail_compact_width = 0x7f0602c5
com.seres.usb.upgrade:attr/arrowHeadLength = 0x7f030039
com.seres.usb.upgrade:attr/layout_constraintStart_toEndOf = 0x7f030292
com.seres.usb.upgrade:attr/behavior_halfExpandedRatio = 0x7f03006c
com.seres.usb.upgrade:attr/layout_constraintLeft_toRightOf = 0x7f03028e
com.seres.usb.upgrade:style/ShapeAppearance.Material3.Corner.Small = 0x7f100175
com.seres.usb.upgrade:color/material_dynamic_tertiary95 = 0x7f050243
com.seres.usb.upgrade:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300ef
com.seres.usb.upgrade:id/carryVelocity = 0x7f08006d
com.seres.usb.upgrade:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070019
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.Button = 0x7f1001ba
com.seres.usb.upgrade:dimen/mtrl_calendar_month_vertical_padding = 0x7f060283
com.seres.usb.upgrade:attr/navigationIconTint = 0x7f03034a
com.seres.usb.upgrade:color/design_fab_stroke_end_outer_color = 0x7f050051
com.seres.usb.upgrade:attr/hoveredFocusedTranslationZ = 0x7f030223
com.seres.usb.upgrade:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ac
com.seres.usb.upgrade:string/searchview_navigation_content_description = 0x7f0f00a3
com.seres.usb.upgrade:color/design_dark_default_color_on_secondary = 0x7f050037
com.seres.usb.upgrade:attr/layout_constraintHeight_default = 0x7f030285
com.seres.usb.upgrade:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070028
com.seres.usb.upgrade:dimen/m3_slider_inactive_track_height = 0x7f0601e3
com.seres.usb.upgrade:attr/percentWidth = 0x7f030371
com.seres.usb.upgrade:attr/checkedState = 0x7f0300bb
com.seres.usb.upgrade:attr/itemBackground = 0x7f030242
com.seres.usb.upgrade:attr/tabRippleColor = 0x7f03041d
com.seres.usb.upgrade:attr/floatingActionButtonTertiaryStyle = 0x7f0301e6
com.seres.usb.upgrade:string/mtrl_switch_thumb_path_unchecked = 0x7f0f0096
com.seres.usb.upgrade:attr/linearProgressIndicatorStyle = 0x7f0302b8
com.seres.usb.upgrade:style/TextAppearance.Material3.LabelSmall = 0x7f1001f0
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Menu = 0x7f1001a7
com.seres.usb.upgrade:attr/chipIcon = 0x7f0300c1
com.seres.usb.upgrade:attr/windowFixedHeightMajor = 0x7f0304ce
com.seres.usb.upgrade:string/mtrl_picker_today_description = 0x7f0f008c
com.seres.usb.upgrade:attr/layout_constraintGuide_percent = 0x7f030283
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013b
com.seres.usb.upgrade:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602dc
com.seres.usb.upgrade:attr/circularflow_viewCenter = 0x7f0300d7
com.seres.usb.upgrade:dimen/m3_carousel_small_item_size_min = 0x7f0600f2
com.seres.usb.upgrade:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f100434
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f10004e
com.seres.usb.upgrade:attr/upDuration = 0x7f0304b6
com.seres.usb.upgrade:attr/errorShown = 0x7f0301b3
com.seres.usb.upgrade:attr/layout_constraintDimensionRatio = 0x7f03027e
com.seres.usb.upgrade:attr/tabPaddingTop = 0x7f03041c
com.seres.usb.upgrade:attr/layout_goneMarginRight = 0x7f0302a7
com.seres.usb.upgrade:macro/m3_comp_outlined_card_container_shape = 0x7f0c00ab
com.seres.usb.upgrade:attr/layout_constraintHeight_max = 0x7f030286
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1002de
com.seres.usb.upgrade:dimen/disabled_alpha_material_dark = 0x7f06008e
com.seres.usb.upgrade:attr/listMenuViewStyle = 0x7f0302bf
com.seres.usb.upgrade:attr/colorSurfaceVariant = 0x7f030128
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009e
com.seres.usb.upgrade:attr/layout_constraintBottom_creator = 0x7f030278
com.seres.usb.upgrade:attr/layout_constraintBaseline_toTopOf = 0x7f030277
com.seres.usb.upgrade:attr/editTextBackground = 0x7f030198
com.seres.usb.upgrade:attr/layout_anchor = 0x7f03026d
com.seres.usb.upgrade:style/Theme.Material3.Light.Dialog = 0x7f10023d
com.seres.usb.upgrade:attr/layout_constraintBaseline_creator = 0x7f030274
com.seres.usb.upgrade:attr/layout_collapseParallaxMultiplier = 0x7f030271
com.seres.usb.upgrade:attr/layout_behavior = 0x7f03026f
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f10026e
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f060202
com.seres.usb.upgrade:attr/closeIconEnabled = 0x7f0300df
com.seres.usb.upgrade:attr/layout = 0x7f030269
com.seres.usb.upgrade:attr/listPopupWindowStyle = 0x7f0302c0
com.seres.usb.upgrade:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.seres.usb.upgrade:attr/materialCalendarHeaderLayout = 0x7f0302df
com.seres.usb.upgrade:id/search_close_btn = 0x7f080184
com.seres.usb.upgrade:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a7
com.seres.usb.upgrade:attr/layout_constrainedWidth = 0x7f030273
com.seres.usb.upgrade:drawable/design_snackbar_background = 0x7f070087
com.seres.usb.upgrade:color/design_dark_default_color_on_surface = 0x7f050038
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f100253
com.seres.usb.upgrade:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050080
com.seres.usb.upgrade:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700b0
com.seres.usb.upgrade:attr/badgeTextColor = 0x7f03005a
com.seres.usb.upgrade:attr/textPanY = 0x7f030464
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501a1
com.seres.usb.upgrade:attr/queryBackground = 0x7f030389
com.seres.usb.upgrade:attr/itemTextAppearanceInactive = 0x7f03025c
com.seres.usb.upgrade:id/cache_measures = 0x7f08006a
com.seres.usb.upgrade:dimen/m3_bottom_nav_min_height = 0x7f0600c1
com.seres.usb.upgrade:dimen/m3_appbar_size_large = 0x7f0600aa
com.seres.usb.upgrade:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f100352
com.seres.usb.upgrade:attr/minTouchTargetSize = 0x7f030311
com.seres.usb.upgrade:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1003a9
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f10016e
com.seres.usb.upgrade:attr/tabMode = 0x7f030417
com.seres.usb.upgrade:attr/itemShapeInsetStart = 0x7f030254
com.seres.usb.upgrade:layout/abc_alert_dialog_material = 0x7f0b0009
com.seres.usb.upgrade:id/tag_accessibility_actions = 0x7f0801b5
com.seres.usb.upgrade:dimen/m3_carousel_small_item_size_max = 0x7f0600f1
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary70 = 0x7f0500cb
com.seres.usb.upgrade:attr/itemShapeInsetEnd = 0x7f030253
com.seres.usb.upgrade:style/Widget.AppCompat.ActionBar = 0x7f1002e5
com.seres.usb.upgrade:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.seres.usb.upgrade:color/material_timepicker_clockface = 0x7f050297
com.seres.usb.upgrade:attr/tickVisible = 0x7f030480
com.seres.usb.upgrade:attr/lStar = 0x7f030262
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Dialog = 0x7f10004d
com.seres.usb.upgrade:attr/itemPaddingTop = 0x7f03024d
com.seres.usb.upgrade:color/mtrl_navigation_bar_colored_item_tint = 0x7f0502b4
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f10030a
com.seres.usb.upgrade:color/mtrl_fab_icon_text_color_selector = 0x7f0502ae
com.seres.usb.upgrade:attr/customBoolean = 0x7f030161
com.seres.usb.upgrade:string/m3_sys_motion_easing_legacy_decelerate = 0x7f0f0042
com.seres.usb.upgrade:attr/itemShapeAppearance = 0x7f03024f
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f100256
com.seres.usb.upgrade:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060082
com.seres.usb.upgrade:attr/backgroundInsetEnd = 0x7f03004a
com.seres.usb.upgrade:styleable/GradientColor = 0x7f11003b
com.seres.usb.upgrade:attr/carousel_backwardTransition = 0x7f0300a2
com.seres.usb.upgrade:anim/abc_fade_out = 0x7f010001
com.seres.usb.upgrade:id/accessibility_custom_action_6 = 0x7f08002c
com.seres.usb.upgrade:attr/drawableLeftCompat = 0x7f030189
com.seres.usb.upgrade:style/Widget.AppCompat.ActionBar.TabView = 0x7f1002e9
com.seres.usb.upgrade:style/Theme.Design.Light.NoActionBar = 0x7f100226
com.seres.usb.upgrade:attr/itemMaxLines = 0x7f030249
com.seres.usb.upgrade:id/image = 0x7f0800e0
com.seres.usb.upgrade:attr/bottomSheetDragHandleStyle = 0x7f03007c
com.seres.usb.upgrade:attr/labelBehavior = 0x7f030263
com.seres.usb.upgrade:color/m3_textfield_stroke_color = 0x7f0501f1
com.seres.usb.upgrade:attr/barLength = 0x7f030063
com.seres.usb.upgrade:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.seres.usb.upgrade:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004b
com.seres.usb.upgrade:attr/isMaterial3Theme = 0x7f03023f
com.seres.usb.upgrade:attr/layout_constraintRight_toLeftOf = 0x7f030290
com.seres.usb.upgrade:styleable/CollapsingToolbarLayout_Layout = 0x7f110023
com.seres.usb.upgrade:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c004a
com.seres.usb.upgrade:color/design_default_color_on_error = 0x7f050042
com.seres.usb.upgrade:color/material_personalized_color_text_hint_foreground_inverse = 0x7f050285
com.seres.usb.upgrade:attr/motionDurationMedium3 = 0x7f030324
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1003a2
com.seres.usb.upgrade:attr/indicatorSize = 0x7f03023a
com.seres.usb.upgrade:attr/elevationOverlayColor = 0x7f03019d
com.seres.usb.upgrade:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0602f5
com.seres.usb.upgrade:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600bd
com.seres.usb.upgrade:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.seres.usb.upgrade:attr/itemFillColor = 0x7f030243
com.seres.usb.upgrade:attr/itemVerticalPadding = 0x7f03025e
com.seres.usb.upgrade:id/textSpacerNoButtons = 0x7f0801c5
com.seres.usb.upgrade:attr/indicatorDirectionLinear = 0x7f030238
com.seres.usb.upgrade:attr/elevationOverlayAccentColor = 0x7f03019c
com.seres.usb.upgrade:attr/backHandlingEnabled = 0x7f030046
com.seres.usb.upgrade:macro/m3_comp_input_chip_label_text_type = 0x7f0c005d
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500e3
com.seres.usb.upgrade:attr/horizontalOffsetWithText = 0x7f030222
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f100444
com.seres.usb.upgrade:attr/indicatorColor = 0x7f030236
com.seres.usb.upgrade:attr/imageZoom = 0x7f030233
com.seres.usb.upgrade:string/mtrl_checkbox_button_path_unchecked = 0x7f0f0065
com.seres.usb.upgrade:macro/m3_comp_slider_handle_color = 0x7f0c0110
com.seres.usb.upgrade:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009f
com.seres.usb.upgrade:attr/hintTextColor = 0x7f03021e
com.seres.usb.upgrade:anim/abc_slide_out_top = 0x7f010009
com.seres.usb.upgrade:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060135
com.seres.usb.upgrade:attr/ifTagNotSet = 0x7f03022d
com.seres.usb.upgrade:dimen/compat_notification_large_icon_max_width = 0x7f06005c
com.seres.usb.upgrade:attr/iconTint = 0x7f03022a
com.seres.usb.upgrade:attr/tabPadding = 0x7f030418
com.seres.usb.upgrade:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1003e7
com.seres.usb.upgrade:id/submit_area = 0x7f0801b2
com.seres.usb.upgrade:attr/applyMotionScene = 0x7f030037
com.seres.usb.upgrade:styleable/NavigationBarView = 0x7f11006a
com.seres.usb.upgrade:id/action_bar = 0x7f080033
com.seres.usb.upgrade:attr/suggestionRowLayout = 0x7f030403
com.seres.usb.upgrade:attr/iconGravity = 0x7f030226
com.seres.usb.upgrade:id/forever = 0x7f0800c9
com.seres.usb.upgrade:dimen/m3_comp_switch_track_width = 0x7f060192
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f10025a
com.seres.usb.upgrade:attr/scrimAnimationDuration = 0x7f0303a2
com.seres.usb.upgrade:attr/liftOnScroll = 0x7f0302b2
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500b8
com.seres.usb.upgrade:id/onInterceptTouchReturnSwipe = 0x7f080146
com.seres.usb.upgrade:attr/horizontalOffset = 0x7f030221
com.seres.usb.upgrade:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f050287
com.seres.usb.upgrade:attr/homeLayout = 0x7f030220
com.seres.usb.upgrade:attr/subtitleTextColor = 0x7f0303fe
com.seres.usb.upgrade:color/m3_dynamic_hint_foreground = 0x7f050084
com.seres.usb.upgrade:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f10040f
com.seres.usb.upgrade:style/Widget.Material3.FloatingActionButton.Surface = 0x7f10038c
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1001d7
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f100118
com.seres.usb.upgrade:attr/fontStyle = 0x7f030203
com.seres.usb.upgrade:style/Widget.Material3.Chip.Filter = 0x7f100365
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0131
com.seres.usb.upgrade:attr/hideOnContentScroll = 0x7f030219
com.seres.usb.upgrade:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1003d2
com.seres.usb.upgrade:id/mtrl_picker_fullscreen = 0x7f080127
com.seres.usb.upgrade:id/accessibility_custom_action_5 = 0x7f08002b
com.seres.usb.upgrade:attr/staggered = 0x7f0303df
com.seres.usb.upgrade:attr/hideNavigationIcon = 0x7f030218
com.seres.usb.upgrade:style/Base.V21.Theme.AppCompat.Light = 0x7f1000a4
com.seres.usb.upgrade:drawable/btn_checkbox_checked_mtrl = 0x7f07007a
com.seres.usb.upgrade:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060271
com.seres.usb.upgrade:attr/layout_scrollEffect = 0x7f0302ae
com.seres.usb.upgrade:attr/tabIndicator = 0x7f03040d
com.seres.usb.upgrade:attr/materialDividerHeavyStyle = 0x7f0302ef
com.seres.usb.upgrade:id/FUNCTION = 0x7f080004
com.seres.usb.upgrade:color/material_slider_inactive_tick_marks_color = 0x7f050291
com.seres.usb.upgrade:attr/checkedIconVisible = 0x7f0300ba
com.seres.usb.upgrade:attr/chipStyle = 0x7f0300cf
com.seres.usb.upgrade:id/ghost_view_holder = 0x7f0800ce
com.seres.usb.upgrade:attr/badgeWithTextHeight = 0x7f03005e
com.seres.usb.upgrade:attr/expandedTitleMarginBottom = 0x7f0301bb
com.seres.usb.upgrade:dimen/design_tab_max_width = 0x7f060089
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1002aa
com.seres.usb.upgrade:attr/insetForeground = 0x7f03023c
com.seres.usb.upgrade:attr/passwordToggleEnabled = 0x7f03036b
com.seres.usb.upgrade:id/fitXY = 0x7f0800c5
com.seres.usb.upgrade:attr/cardBackgroundColor = 0x7f03009a
com.seres.usb.upgrade:layout/mtrl_calendar_months = 0x7f0b004f
com.seres.usb.upgrade:attr/helperTextEnabled = 0x7f030213
com.seres.usb.upgrade:style/Widget.AppCompat.Button.Colored = 0x7f1002f4
com.seres.usb.upgrade:attr/height = 0x7f030211
com.seres.usb.upgrade:attr/subheaderInsetStart = 0x7f0303f8
com.seres.usb.upgrade:attr/forceApplySystemWindowInsetTop = 0x7f030206
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c0101
com.seres.usb.upgrade:attr/forceDefaultNavigationOnClickListener = 0x7f030207
com.seres.usb.upgrade:attr/tabIndicatorAnimationDuration = 0x7f03040e
com.seres.usb.upgrade:id/item_touch_helper_previous_elevation = 0x7f0800e9
com.seres.usb.upgrade:attr/materialAlertDialogTitleTextStyle = 0x7f0302d5
com.seres.usb.upgrade:color/m3_navigation_rail_ripple_color_selector = 0x7f050098
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1001b8
com.seres.usb.upgrade:macro/m3_comp_time_picker_container_shape = 0x7f0c0150
com.seres.usb.upgrade:id/open_search_view_status_bar_spacer = 0x7f080152
com.seres.usb.upgrade:color/abc_tint_edittext = 0x7f050015
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f10002e
com.seres.usb.upgrade:attr/extendedFloatingActionButtonStyle = 0x7f0301c5
com.seres.usb.upgrade:attr/dragScale = 0x7f030184
com.seres.usb.upgrade:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f100280
com.seres.usb.upgrade:layout/abc_action_mode_bar = 0x7f0b0004
com.seres.usb.upgrade:attr/fontProviderSystemFontFamily = 0x7f030202
com.seres.usb.upgrade:id/accessibility_custom_action_10 = 0x7f080012
com.seres.usb.upgrade:attr/contentInsetLeft = 0x7f03013b
com.seres.usb.upgrade:attr/fontProviderPackage = 0x7f030200
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f100395
com.seres.usb.upgrade:attr/dragDirection = 0x7f030183
com.seres.usb.upgrade:color/m3_card_ripple_color = 0x7f05006c
com.seres.usb.upgrade:dimen/m3_alert_dialog_corner_size = 0x7f06009f
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500a5
com.seres.usb.upgrade:attr/fontProviderFetchStrategy = 0x7f0301fe
com.seres.usb.upgrade:attr/errorAccessibilityLiveRegion = 0x7f0301ad
com.seres.usb.upgrade:dimen/mtrl_bottomappbar_height = 0x7f060251
com.seres.usb.upgrade:attr/fontFamily = 0x7f0301fb
com.seres.usb.upgrade:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016e
com.seres.usb.upgrade:drawable/mtrl_dropdown_arrow = 0x7f0700bf
com.seres.usb.upgrade:attr/flow_wrapMode = 0x7f0301f9
com.seres.usb.upgrade:attr/buttonBarNeutralButtonStyle = 0x7f03008c
com.seres.usb.upgrade:attr/chipCornerRadius = 0x7f0300be
com.seres.usb.upgrade:attr/lineHeight = 0x7f0302b6
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f100156
com.seres.usb.upgrade:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0502cf
com.seres.usb.upgrade:attr/colorOnPrimaryFixed = 0x7f030102
com.seres.usb.upgrade:color/m3_button_ripple_color_selector = 0x7f050068
com.seres.usb.upgrade:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f10009f
com.seres.usb.upgrade:attr/buttonStyleSmall = 0x7f030097
com.seres.usb.upgrade:color/material_dynamic_neutral100 = 0x7f050206
com.seres.usb.upgrade:attr/flow_verticalBias = 0x7f0301f6
com.seres.usb.upgrade:style/Theme.Material3.DynamicColors.DayNight = 0x7f100239
com.seres.usb.upgrade:color/mtrl_navigation_item_background_color = 0x7f0502b8
com.seres.usb.upgrade:attr/windowFixedHeightMinor = 0x7f0304cf
com.seres.usb.upgrade:integer/mtrl_chip_anim_duration = 0x7f090035
com.seres.usb.upgrade:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.seres.usb.upgrade:attr/scrimVisibleHeightTrigger = 0x7f0303a4
com.seres.usb.upgrade:attr/simpleItems = 0x7f0303ca
com.seres.usb.upgrade:attr/spanCount = 0x7f0303d3
com.seres.usb.upgrade:attr/initialActivityCount = 0x7f03023b
com.seres.usb.upgrade:attr/colorOutlineVariant = 0x7f030111
com.seres.usb.upgrade:attr/dividerThickness = 0x7f030181
com.seres.usb.upgrade:attr/chainUseRtl = 0x7f0300ad
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007d
com.seres.usb.upgrade:attr/textInputFilledDenseStyle = 0x7f030458
com.seres.usb.upgrade:attr/flow_maxElementsWrap = 0x7f0301f3
com.seres.usb.upgrade:attr/counterMaxLength = 0x7f030157
com.seres.usb.upgrade:anim/abc_popup_exit = 0x7f010004
com.seres.usb.upgrade:dimen/design_snackbar_text_size = 0x7f060088
com.seres.usb.upgrade:attr/floatingActionButtonStyle = 0x7f0301e4
com.seres.usb.upgrade:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301e3
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f10003a
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500e1
com.seres.usb.upgrade:color/m3_timepicker_secondary_text_button_text_color = 0x7f0501fa
com.seres.usb.upgrade:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301df
com.seres.usb.upgrade:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010b
com.seres.usb.upgrade:color/m3_sys_color_dark_on_error = 0x7f050153
com.seres.usb.upgrade:drawable/$m3_avd_hide_password__2 = 0x7f070009
com.seres.usb.upgrade:attr/layout_constraintVertical_chainStyle = 0x7f030299
com.seres.usb.upgrade:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070053
com.seres.usb.upgrade:color/m3_text_button_foreground_color_selector = 0x7f0501eb
com.seres.usb.upgrade:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1003e9
com.seres.usb.upgrade:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1002b8
com.seres.usb.upgrade:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f100012
com.seres.usb.upgrade:anim/design_bottom_sheet_slide_out = 0x7f010019
com.seres.usb.upgrade:attr/floatingActionButtonSecondaryStyle = 0x7f0301de
com.seres.usb.upgrade:dimen/m3_badge_horizontal_offset = 0x7f0600b3
com.seres.usb.upgrade:attr/motionDurationShort4 = 0x7f030329
com.seres.usb.upgrade:dimen/notification_top_pad = 0x7f060311
com.seres.usb.upgrade:dimen/m3_carousel_gone_size = 0x7f0600ef
com.seres.usb.upgrade:attr/flow_firstHorizontalStyle = 0x7f0301e8
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Button.Small = 0x7f1000d3
com.seres.usb.upgrade:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301db
com.seres.usb.upgrade:attr/fabCustomSize = 0x7f0301d0
com.seres.usb.upgrade:attr/fabCradleVerticalOffset = 0x7f0301cf
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00d0
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500e8
com.seres.usb.upgrade:attr/layout_constraintCircleAngle = 0x7f03027c
com.seres.usb.upgrade:attr/clockNumberTextColor = 0x7f0300dd
com.seres.usb.upgrade:drawable/m3_radiobutton_ripple = 0x7f0700a3
com.seres.usb.upgrade:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.seres.usb.upgrade:attr/actionModeFindDrawable = 0x7f030018
com.seres.usb.upgrade:attr/boxCollapsedPaddingTop = 0x7f030080
com.seres.usb.upgrade:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0054
com.seres.usb.upgrade:attr/fabCradleRoundedCornerRadius = 0x7f0301ce
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1000e0
com.seres.usb.upgrade:color/m3_chip_ripple_color = 0x7f050072
com.seres.usb.upgrade:id/animateToEnd = 0x7f08004c
com.seres.usb.upgrade:dimen/mtrl_calendar_header_divider_thickness = 0x7f060279
com.seres.usb.upgrade:color/m3_radiobutton_ripple_tint = 0x7f05009c
com.seres.usb.upgrade:id/parentPanel = 0x7f08015b
com.seres.usb.upgrade:attr/actionBarTabTextStyle = 0x7f03000a
com.seres.usb.upgrade:attr/materialAlertDialogBodyTextStyle = 0x7f0302d0
com.seres.usb.upgrade:id/accessibility_custom_action_15 = 0x7f080017
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070016
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0015
com.seres.usb.upgrade:attr/extraMultilineHeightEnabled = 0x7f0301c8
com.seres.usb.upgrade:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301c6
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601ef
com.seres.usb.upgrade:attr/motionEffect_viewTransition = 0x7f03033d
com.seres.usb.upgrade:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d5
com.seres.usb.upgrade:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301c3
com.seres.usb.upgrade:macro/m3_comp_sheet_side_docked_container_color = 0x7f0c010a
com.seres.usb.upgrade:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060230
com.seres.usb.upgrade:attr/badgeWidth = 0x7f03005d
com.seres.usb.upgrade:id/bounceEnd = 0x7f080060
com.seres.usb.upgrade:attr/pressedTranslationZ = 0x7f030383
com.seres.usb.upgrade:attr/colorSecondaryVariant = 0x7f03011e
com.seres.usb.upgrade:attr/extendMotionSpec = 0x7f0301c1
com.seres.usb.upgrade:attr/layout_constraintWidth_default = 0x7f03029c
com.seres.usb.upgrade:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005e
com.seres.usb.upgrade:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301dc
com.seres.usb.upgrade:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700c5
com.seres.usb.upgrade:id/anticipate = 0x7f08004f
com.seres.usb.upgrade:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f100049
com.seres.usb.upgrade:attr/statusBarForeground = 0x7f0303f1
com.seres.usb.upgrade:attr/expandedTitleTextColor = 0x7f0301c0
com.seres.usb.upgrade:drawable/btn_radio_off_mtrl = 0x7f07007e
com.seres.usb.upgrade:attr/expandedTitleMargin = 0x7f0301ba
com.seres.usb.upgrade:attr/expanded = 0x7f0301b7
com.seres.usb.upgrade:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f100211
com.seres.usb.upgrade:color/mtrl_btn_bg_color_selector = 0x7f050299
com.seres.usb.upgrade:attr/moveWhenScrollAtTop = 0x7f030346
com.seres.usb.upgrade:attr/isMaterialTheme = 0x7f030240
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f060204
com.seres.usb.upgrade:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.seres.usb.upgrade:attr/errorIconDrawable = 0x7f0301b0
com.seres.usb.upgrade:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060159
com.seres.usb.upgrade:id/withinBounds = 0x7f0801fc
com.seres.usb.upgrade:attr/errorAccessibilityLabel = 0x7f0301ac
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f1003fd
com.seres.usb.upgrade:style/Widget.Material3.TabLayout.Secondary = 0x7f1003cd
com.seres.usb.upgrade:attr/state_collapsed = 0x7f0303e8
com.seres.usb.upgrade:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012d
com.seres.usb.upgrade:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1002ef
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Headline3 = 0x7f100200
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
com.seres.usb.upgrade:attr/textAppearanceHeadline5 = 0x7f030438
com.seres.usb.upgrade:dimen/abc_text_size_caption_material = 0x7f060042
com.seres.usb.upgrade:attr/viewTransitionMode = 0x7f0304be
com.seres.usb.upgrade:attr/actionBarPopupTheme = 0x7f030004
com.seres.usb.upgrade:dimen/mtrl_btn_inset = 0x7f06025b
com.seres.usb.upgrade:drawable/abc_text_select_handle_right_mtrl = 0x7f070071
com.seres.usb.upgrade:dimen/mtrl_btn_icon_padding = 0x7f06025a
com.seres.usb.upgrade:attr/endIconMode = 0x7f0301a5
com.seres.usb.upgrade:attr/endIconMinSize = 0x7f0301a4
com.seres.usb.upgrade:attr/endIconCheckable = 0x7f0301a1
com.seres.usb.upgrade:attr/enableEdgeToEdge = 0x7f0301a0
com.seres.usb.upgrade:attr/editTextStyle = 0x7f03019a
com.seres.usb.upgrade:style/Widget.Design.AppBarLayout = 0x7f100330
com.seres.usb.upgrade:dimen/m3_badge_offset = 0x7f0600b4
com.seres.usb.upgrade:color/m3_ref_palette_neutral_variant50 = 0x7f050115
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f0601fd
com.seres.usb.upgrade:attr/drawableTint = 0x7f03018d
com.seres.usb.upgrade:id/actionDown = 0x7f080030
com.seres.usb.upgrade:attr/subheaderInsetEnd = 0x7f0303f7
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f100055
com.seres.usb.upgrade:attr/searchPrefixText = 0x7f0303a7
com.seres.usb.upgrade:attr/duration = 0x7f030196
com.seres.usb.upgrade:string/path_password_strike_through = 0x7f0f009f
com.seres.usb.upgrade:integer/m3_sys_motion_duration_extra_long4 = 0x7f090012
com.seres.usb.upgrade:id/noScroll = 0x7f08013c
com.seres.usb.upgrade:attr/dropdownListPreferredItemHeight = 0x7f030195
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c006a
com.seres.usb.upgrade:dimen/hint_pressed_alpha_material_light = 0x7f060099
com.seres.usb.upgrade:attr/actionBarTheme = 0x7f03000b
com.seres.usb.upgrade:attr/expandedHintEnabled = 0x7f0301b8
com.seres.usb.upgrade:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060173
com.seres.usb.upgrade:color/material_dynamic_neutral_variant50 = 0x7f050217
com.seres.usb.upgrade:attr/drawerLayoutStyle = 0x7f030192
com.seres.usb.upgrade:color/abc_btn_colored_text_material = 0x7f050003
com.seres.usb.upgrade:string/m3_sys_motion_easing_emphasized = 0x7f0f003c
com.seres.usb.upgrade:attr/chipMinHeight = 0x7f0300c6
com.seres.usb.upgrade:attr/chipStrokeColor = 0x7f0300cd
com.seres.usb.upgrade:attr/drawableTopCompat = 0x7f03018f
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0087
com.seres.usb.upgrade:attr/drawableTintMode = 0x7f03018e
com.seres.usb.upgrade:attr/colorOnPrimarySurface = 0x7f030104
com.seres.usb.upgrade:attr/drawableStartCompat = 0x7f03018c
com.seres.usb.upgrade:dimen/m3_divider_heavy_thickness = 0x7f0601a8
com.seres.usb.upgrade:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f100252
com.seres.usb.upgrade:id/percent = 0x7f080162
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060147
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_container_high = 0x7f050166
com.seres.usb.upgrade:attr/drawableSize = 0x7f03018b
com.seres.usb.upgrade:attr/itemTextAppearanceActiveBoldEnabled = 0x7f03025b
com.seres.usb.upgrade:attr/drawableRightCompat = 0x7f03018a
com.seres.usb.upgrade:attr/layoutDescription = 0x7f03026a
com.seres.usb.upgrade:attr/toolbarNavigationButtonStyle = 0x7f030495
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070014
com.seres.usb.upgrade:attr/tabGravity = 0x7f03040a
com.seres.usb.upgrade:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f100046
com.seres.usb.upgrade:attr/expandedTitleMarginEnd = 0x7f0301bc
com.seres.usb.upgrade:attr/dragThreshold = 0x7f030185
com.seres.usb.upgrade:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f100116
com.seres.usb.upgrade:dimen/abc_dialog_title_divider_material = 0x7f060026
com.seres.usb.upgrade:attr/layout_scrollFlags = 0x7f0302af
com.seres.usb.upgrade:color/m3_ref_palette_neutral17 = 0x7f0500fb
com.seres.usb.upgrade:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601a5
com.seres.usb.upgrade:attr/layout_constraintLeft_toLeftOf = 0x7f03028d
com.seres.usb.upgrade:layout/mtrl_calendar_days_of_week = 0x7f0b004a
com.seres.usb.upgrade:color/mtrl_navigation_bar_ripple_color = 0x7f0502b7
com.seres.usb.upgrade:attr/actionDropDownStyle = 0x7f03000e
com.seres.usb.upgrade:attr/elevationOverlayEnabled = 0x7f03019e
com.seres.usb.upgrade:attr/actionModeSplitBackground = 0x7f03001d
com.seres.usb.upgrade:color/m3_ref_palette_error20 = 0x7f0500ed
com.seres.usb.upgrade:attr/endIconDrawable = 0x7f0301a3
com.seres.usb.upgrade:attr/dividerPadding = 0x7f030180
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f100305
com.seres.usb.upgrade:style/Widget.AppCompat.ImageButton = 0x7f1002fe
com.seres.usb.upgrade:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f100229
com.seres.usb.upgrade:string/m3_exceed_max_badge_text_suffix = 0x7f0f0037
com.seres.usb.upgrade:id/navigation_bar_item_active_indicator_view = 0x7f080132
com.seres.usb.upgrade:id/accessibility_custom_action_11 = 0x7f080013
com.seres.usb.upgrade:dimen/m3_chip_disabled_translation_z = 0x7f0600f5
com.seres.usb.upgrade:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.seres.usb.upgrade:dimen/mtrl_progress_circular_radius = 0x7f0602d1
com.seres.usb.upgrade:attr/barrierMargin = 0x7f030066
com.seres.usb.upgrade:color/mtrl_btn_transparent_bg_color = 0x7f0502a0
com.seres.usb.upgrade:attr/collapsedSize = 0x7f0300e8
com.seres.usb.upgrade:attr/fabAlignmentMode = 0x7f0301c9
com.seres.usb.upgrade:attr/divider = 0x7f03017b
com.seres.usb.upgrade:styleable/FloatingActionButton_Behavior_Layout = 0x7f110034
com.seres.usb.upgrade:attr/badgeVerticalPadding = 0x7f03005b
com.seres.usb.upgrade:attr/displayOptions = 0x7f03017a
com.seres.usb.upgrade:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f10036a
com.seres.usb.upgrade:string/mtrl_checkbox_button_path_checked = 0x7f0f0062
com.seres.usb.upgrade:attr/dialogTheme = 0x7f030179
com.seres.usb.upgrade:attr/dialogCornerRadius = 0x7f030177
com.seres.usb.upgrade:color/material_dynamic_tertiary40 = 0x7f05023d
com.seres.usb.upgrade:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f060247
com.seres.usb.upgrade:animator/mtrl_btn_state_list_anim = 0x7f020015
com.seres.usb.upgrade:attr/shapeAppearanceLargeComponent = 0x7f0303b5
com.seres.usb.upgrade:attr/textAppearanceSmallPopupMenu = 0x7f030449
com.seres.usb.upgrade:attr/searchIcon = 0x7f0303a6
com.seres.usb.upgrade:attr/deltaPolarAngle = 0x7f030174
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f10026d
com.seres.usb.upgrade:attr/behavior_overlapTop = 0x7f03006e
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500de
com.seres.usb.upgrade:id/SHIFT = 0x7f080007
com.seres.usb.upgrade:dimen/mtrl_extended_fab_elevation = 0x7f0602a2
com.seres.usb.upgrade:color/m3_appbar_overlay_color = 0x7f050060
com.seres.usb.upgrade:attr/actionBarTabBarStyle = 0x7f030008
com.seres.usb.upgrade:color/m3_sys_color_dark_on_primary_container = 0x7f050156
com.seres.usb.upgrade:attr/actionModeCutDrawable = 0x7f030017
com.seres.usb.upgrade:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004f
com.seres.usb.upgrade:attr/dayStyle = 0x7f03016d
com.seres.usb.upgrade:attr/appBarLayoutStyle = 0x7f030036
com.seres.usb.upgrade:attr/actionModeCopyDrawable = 0x7f030016
com.seres.usb.upgrade:style/Widget.Design.TextInputEditText = 0x7f100339
com.seres.usb.upgrade:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.seres.usb.upgrade:attr/drawPath = 0x7f030186
com.seres.usb.upgrade:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060250
com.seres.usb.upgrade:attr/dividerColor = 0x7f03017c
com.seres.usb.upgrade:color/material_harmonized_color_error_container = 0x7f05024d
com.seres.usb.upgrade:color/m3_default_color_primary_text = 0x7f05007a
com.seres.usb.upgrade:attr/coplanarSiblingViewId = 0x7f03014a
com.seres.usb.upgrade:dimen/mtrl_slider_thumb_radius = 0x7f0602e3
com.seres.usb.upgrade:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.seres.usb.upgrade:attr/dayInvalidStyle = 0x7f03016b
com.seres.usb.upgrade:attr/badgeWithTextWidth = 0x7f030062
com.seres.usb.upgrade:color/m3_sys_color_dark_surface_dim = 0x7f05016a
com.seres.usb.upgrade:attr/customDimension = 0x7f030164
com.seres.usb.upgrade:attr/titleCentered = 0x7f030485
com.seres.usb.upgrade:attr/snackbarButtonStyle = 0x7f0303d0
com.seres.usb.upgrade:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00f0
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f050184
com.seres.usb.upgrade:attr/colorSwitchThumbNormal = 0x7f030129
com.seres.usb.upgrade:attr/trackTintMode = 0x7f0304ab
com.seres.usb.upgrade:attr/cursorErrorColor = 0x7f03015f
com.seres.usb.upgrade:attr/tabIndicatorFullWidth = 0x7f030411
com.seres.usb.upgrade:attr/navigationIcon = 0x7f030349
com.seres.usb.upgrade:dimen/m3_btn_inset = 0x7f0600d7
com.seres.usb.upgrade:attr/listLayout = 0x7f0302be
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f100259
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_container_elevation = 0x7f060116
com.seres.usb.upgrade:color/m3_sys_color_tertiary_fixed_dim = 0x7f0501e3
com.seres.usb.upgrade:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0501db
com.seres.usb.upgrade:attr/fontVariationSettings = 0x7f030204
com.seres.usb.upgrade:integer/mtrl_calendar_header_orientation = 0x7f090030
com.seres.usb.upgrade:color/m3_sys_color_light_inverse_primary = 0x7f0501ba
com.seres.usb.upgrade:attr/crossfade = 0x7f03015c
com.seres.usb.upgrade:color/error_color_material_dark = 0x7f05005a
com.seres.usb.upgrade:attr/tabSelectedTextAppearance = 0x7f03041f
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1001bc
com.seres.usb.upgrade:dimen/mtrl_shape_corner_size_large_component = 0x7f0602db
com.seres.usb.upgrade:dimen/abc_search_view_preferred_height = 0x7f060036
com.seres.usb.upgrade:attr/backgroundColor = 0x7f030048
com.seres.usb.upgrade:attr/borderRound = 0x7f030074
com.seres.usb.upgrade:attr/counterOverflowTextColor = 0x7f030159
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060201
com.seres.usb.upgrade:dimen/m3_chip_corner_size = 0x7f0600f4
com.seres.usb.upgrade:attr/materialCalendarHeaderSelection = 0x7f0302e0
com.seres.usb.upgrade:dimen/m3_btn_max_width = 0x7f0600d8
com.seres.usb.upgrade:style/Base.Widget.AppCompat.ProgressBar = 0x7f1000ee
com.seres.usb.upgrade:attr/itemShapeAppearanceOverlay = 0x7f030250
com.seres.usb.upgrade:color/material_personalized_color_surface_variant = 0x7f050282
com.seres.usb.upgrade:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.seres.usb.upgrade:attr/counterEnabled = 0x7f030156
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f10037c
com.seres.usb.upgrade:attr/buttonTintMode = 0x7f030099
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601cc
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f10012e
com.seres.usb.upgrade:layout/select_dialog_item_material = 0x7f0b0067
com.seres.usb.upgrade:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fb
com.seres.usb.upgrade:attr/cornerRadius = 0x7f030150
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_container_color = 0x7f0c006d
com.seres.usb.upgrade:attr/ratingBarStyleIndicator = 0x7f03038f
com.seres.usb.upgrade:dimen/mtrl_slider_thumb_elevation = 0x7f0602e2
com.seres.usb.upgrade:dimen/m3_searchview_height = 0x7f0601dc
com.seres.usb.upgrade:id/textinput_error = 0x7f0801cd
com.seres.usb.upgrade:attr/clockHandColor = 0x7f0300db
com.seres.usb.upgrade:attr/curveFit = 0x7f030160
com.seres.usb.upgrade:string/abc_menu_shift_shortcut_label = 0x7f0f000e
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0133
com.seres.usb.upgrade:attr/motionEffect_strict = 0x7f03033a
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500c2
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500b5
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501a9
com.seres.usb.upgrade:attr/autoShowKeyboard = 0x7f03003f
com.seres.usb.upgrade:attr/passwordToggleTintMode = 0x7f03036d
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f100396
com.seres.usb.upgrade:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f06024e
com.seres.usb.upgrade:attr/cornerFamilyTopLeft = 0x7f03014e
com.seres.usb.upgrade:dimen/m3_menu_elevation = 0x7f0601b6
com.seres.usb.upgrade:dimen/m3_extended_fab_min_height = 0x7f0601ac
com.seres.usb.upgrade:attr/layout_constraintWidth_min = 0x7f03029e
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Display3 = 0x7f10019b
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015f
com.seres.usb.upgrade:attr/ifTagSet = 0x7f03022e
com.seres.usb.upgrade:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
com.seres.usb.upgrade:attr/closeIconVisible = 0x7f0300e4
com.seres.usb.upgrade:attr/layout_goneMarginStart = 0x7f0302a8
com.seres.usb.upgrade:id/autoCompleteToStart = 0x7f080056
com.seres.usb.upgrade:attr/carousel_firstView = 0x7f0300a4
com.seres.usb.upgrade:drawable/abc_star_black_48dp = 0x7f070068
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070017
com.seres.usb.upgrade:dimen/mtrl_switch_thumb_size = 0x7f0602f1
com.seres.usb.upgrade:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f060288
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f100163
com.seres.usb.upgrade:attr/coordinatorLayoutStyle = 0x7f030149
com.seres.usb.upgrade:attr/materialSearchViewToolbarHeight = 0x7f0302f8
com.seres.usb.upgrade:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f06017d
com.seres.usb.upgrade:attr/fontProviderFetchTimeout = 0x7f0301ff
com.seres.usb.upgrade:color/design_fab_stroke_end_inner_color = 0x7f050050
com.seres.usb.upgrade:dimen/mtrl_btn_disabled_z = 0x7f060255
com.seres.usb.upgrade:attr/colorAccent = 0x7f0300f2
com.seres.usb.upgrade:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602b8
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500b9
com.seres.usb.upgrade:id/vertical_only = 0x7f0801f0
com.seres.usb.upgrade:attr/counterTextAppearance = 0x7f03015a
com.seres.usb.upgrade:attr/textPanX = 0x7f030463
com.seres.usb.upgrade:attr/itemStrokeWidth = 0x7f030258
com.seres.usb.upgrade:attr/subheaderTextAppearance = 0x7f0303f9
com.seres.usb.upgrade:attr/contentPaddingLeft = 0x7f030142
com.seres.usb.upgrade:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030061
com.seres.usb.upgrade:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.seres.usb.upgrade:attr/contentPaddingBottom = 0x7f030140
com.seres.usb.upgrade:color/material_dynamic_neutral_variant99 = 0x7f05021d
com.seres.usb.upgrade:attr/contentInsetStart = 0x7f03013d
com.seres.usb.upgrade:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f100106
com.seres.usb.upgrade:attr/chipGroupStyle = 0x7f0300c0
com.seres.usb.upgrade:attr/circularflow_radiusInDP = 0x7f0300d6
com.seres.usb.upgrade:style/TextAppearance.Compat.Notification.Time = 0x7f1001c7
com.seres.usb.upgrade:attr/contentInsetEnd = 0x7f030139
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f070010
com.seres.usb.upgrade:attr/colorOnTertiaryFixedVariant = 0x7f03010f
com.seres.usb.upgrade:style/Theme.Material3.DayNight.Dialog = 0x7f100232
com.seres.usb.upgrade:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0049
com.seres.usb.upgrade:attr/contentDescription = 0x7f030138
com.seres.usb.upgrade:attr/chipIconEnabled = 0x7f0300c2
com.seres.usb.upgrade:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1003ce
com.seres.usb.upgrade:id/action_mode_close_button = 0x7f080042
com.seres.usb.upgrade:drawable/avd_show_password = 0x7f070079
com.seres.usb.upgrade:dimen/mtrl_slider_tick_radius = 0x7f0602e4
com.seres.usb.upgrade:id/accessibility_custom_action_4 = 0x7f08002a
com.seres.usb.upgrade:attr/colorControlNormal = 0x7f0300f8
com.seres.usb.upgrade:attr/blendSrc = 0x7f030073
com.seres.usb.upgrade:attr/nestedScrollable = 0x7f030350
com.seres.usb.upgrade:attr/colorTertiaryFixed = 0x7f03012c
com.seres.usb.upgrade:dimen/design_navigation_separator_vertical_padding = 0x7f06007d
com.seres.usb.upgrade:style/Widget.Material3.BottomNavigationView = 0x7f100347
com.seres.usb.upgrade:attr/commitIcon = 0x7f03012e
com.seres.usb.upgrade:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f06018e
com.seres.usb.upgrade:attr/keyboardIcon = 0x7f030260
com.seres.usb.upgrade:attr/colorTertiary = 0x7f03012a
com.seres.usb.upgrade:dimen/mtrl_progress_circular_inset_small = 0x7f0602d0
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00ca
com.seres.usb.upgrade:color/m3_sys_color_dark_inverse_primary = 0x7f050150
com.seres.usb.upgrade:dimen/mtrl_calendar_navigation_height = 0x7f060285
com.seres.usb.upgrade:attr/contentPaddingTop = 0x7f030145
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f050188
com.seres.usb.upgrade:attr/chipIconVisible = 0x7f0300c5
com.seres.usb.upgrade:attr/listPreferredItemPaddingRight = 0x7f0302c6
com.seres.usb.upgrade:drawable/mtrl_switch_track_decoration = 0x7f0700d6
com.seres.usb.upgrade:attr/shapeAppearanceCornerLarge = 0x7f0303b2
com.seres.usb.upgrade:attr/colorSurfaceInverse = 0x7f030127
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1001b5
com.seres.usb.upgrade:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014c
com.seres.usb.upgrade:attr/colorSurfaceContainerHighest = 0x7f030123
com.seres.usb.upgrade:drawable/$m3_avd_show_password__1 = 0x7f07000b
com.seres.usb.upgrade:attr/colorSurface = 0x7f03011f
com.seres.usb.upgrade:color/m3_sys_color_light_surface_bright = 0x7f0501ce
com.seres.usb.upgrade:attr/textEndPadding = 0x7f030456
com.seres.usb.upgrade:integer/m3_sys_motion_duration_medium2 = 0x7f090018
com.seres.usb.upgrade:attr/constraints = 0x7f030136
com.seres.usb.upgrade:string/material_slider_range_start = 0x7f0f0054
com.seres.usb.upgrade:color/material_dynamic_neutral_variant0 = 0x7f050211
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f10025d
com.seres.usb.upgrade:attr/helperTextTextAppearance = 0x7f030214
com.seres.usb.upgrade:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060124
com.seres.usb.upgrade:attr/colorOnTertiary = 0x7f03010c
com.seres.usb.upgrade:attr/alertDialogTheme = 0x7f03002c
com.seres.usb.upgrade:color/design_dark_default_color_surface = 0x7f05003e
com.seres.usb.upgrade:styleable/ImageFilterView = 0x7f11003d
com.seres.usb.upgrade:attr/actionModeWebSearchDrawable = 0x7f030020
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001b
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Search = 0x7f1002b2
com.seres.usb.upgrade:color/highlighted_text_material_light = 0x7f05005f
com.seres.usb.upgrade:attr/borderlessButtonStyle = 0x7f030077
com.seres.usb.upgrade:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f100107
com.seres.usb.upgrade:attr/colorSecondaryContainer = 0x7f03011b
com.seres.usb.upgrade:style/Widget.Material3.Chip.Suggestion = 0x7f10036b
com.seres.usb.upgrade:attr/tabStyle = 0x7f030421
com.seres.usb.upgrade:color/m3_sys_color_dark_secondary_container = 0x7f050162
com.seres.usb.upgrade:attr/backgroundTintMode = 0x7f030051
com.seres.usb.upgrade:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.seres.usb.upgrade:drawable/tooltip_frame_dark = 0x7f0700e8
com.seres.usb.upgrade:attr/colorPrimarySurface = 0x7f030118
com.seres.usb.upgrade:id/search_mag_icon = 0x7f080187
com.seres.usb.upgrade:color/abc_tint_btn_checkable = 0x7f050013
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0142
com.seres.usb.upgrade:attr/autoSizePresetSizes = 0x7f030042
com.seres.usb.upgrade:attr/layout_constraintRight_toRightOf = 0x7f030291
com.seres.usb.upgrade:styleable/SnackbarLayout = 0x7f11007f
com.seres.usb.upgrade:attr/windowActionBarOverlay = 0x7f0304cc
com.seres.usb.upgrade:attr/textAppearanceDisplayMedium = 0x7f030432
com.seres.usb.upgrade:attr/colorOnErrorContainer = 0x7f0300ff
com.seres.usb.upgrade:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f0f003d
com.seres.usb.upgrade:attr/onHide = 0x7f030355
com.seres.usb.upgrade:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f100184
com.seres.usb.upgrade:attr/cornerSizeTopLeft = 0x7f030154
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501a4
com.seres.usb.upgrade:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1002a7
com.seres.usb.upgrade:attr/statusBarBackground = 0x7f0303f0
com.seres.usb.upgrade:attr/layout_constraintBottom_toBottomOf = 0x7f030279
com.seres.usb.upgrade:id/staticPostLayout = 0x7f0801ae
com.seres.usb.upgrade:attr/boxStrokeColor = 0x7f030085
com.seres.usb.upgrade:attr/tabIconTint = 0x7f03040b
com.seres.usb.upgrade:drawable/m3_selection_control_ripple = 0x7f0700a4
com.seres.usb.upgrade:dimen/mtrl_calendar_month_horizontal_padding = 0x7f060282
com.seres.usb.upgrade:attr/behavior_expandedOffset = 0x7f03006a
com.seres.usb.upgrade:drawable/notify_panel_notification_icon_bg = 0x7f0700e4
com.seres.usb.upgrade:attr/enforceTextAppearance = 0x7f0301aa
com.seres.usb.upgrade:attr/colorSecondary = 0x7f03011a
com.seres.usb.upgrade:id/message = 0x7f080110
com.seres.usb.upgrade:attr/fontProviderCerts = 0x7f0301fd
com.seres.usb.upgrade:attr/startIconTintMode = 0x7f0303e6
com.seres.usb.upgrade:color/m3_sys_color_dark_background = 0x7f05014c
com.seres.usb.upgrade:attr/autoCompleteMode = 0x7f03003d
com.seres.usb.upgrade:attr/activeIndicatorLabelPadding = 0x7f030026
com.seres.usb.upgrade:attr/quantizeMotionPhase = 0x7f030387
com.seres.usb.upgrade:color/material_dynamic_neutral50 = 0x7f05020a
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f100038
com.seres.usb.upgrade:attr/boxCornerRadiusBottomStart = 0x7f030082
com.seres.usb.upgrade:id/middle = 0x7f080111
com.seres.usb.upgrade:attr/colorOutline = 0x7f030110
com.seres.usb.upgrade:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c5
com.seres.usb.upgrade:attr/colorOnSecondaryFixed = 0x7f030107
com.seres.usb.upgrade:attr/toolbarSurfaceStyle = 0x7f030497
com.seres.usb.upgrade:attr/labelVisibilityMode = 0x7f030265
com.seres.usb.upgrade:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1003c5
com.seres.usb.upgrade:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
com.seres.usb.upgrade:attr/isLightTheme = 0x7f03023d
com.seres.usb.upgrade:style/Widget.MaterialComponents.BottomAppBar = 0x7f1003eb
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f100164
com.seres.usb.upgrade:drawable/section_background = 0x7f0700e5
com.seres.usb.upgrade:attr/currentState = 0x7f03015d
com.seres.usb.upgrade:id/continuousVelocity = 0x7f080087
com.seres.usb.upgrade:attr/animationMode = 0x7f030035
com.seres.usb.upgrade:macro/m3_comp_search_bar_input_text_color = 0x7f0c00ea
com.seres.usb.upgrade:id/right_icon = 0x7f080173
com.seres.usb.upgrade:attr/colorOnSecondary = 0x7f030105
com.seres.usb.upgrade:attr/layout_constraintTop_creator = 0x7f030295
com.seres.usb.upgrade:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060231
com.seres.usb.upgrade:style/Base.Theme.Material3.Dark = 0x7f100059
com.seres.usb.upgrade:dimen/abc_text_size_subhead_material = 0x7f06004d
com.seres.usb.upgrade:attr/motionDurationShort3 = 0x7f030328
com.seres.usb.upgrade:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1000d7
com.seres.usb.upgrade:attr/checkedButton = 0x7f0300b2
com.seres.usb.upgrade:macro/m3_comp_time_picker_container_color = 0x7f0c014f
com.seres.usb.upgrade:id/async = 0x7f080052
com.seres.usb.upgrade:attr/listDividerAlertDialog = 0x7f0302bc
com.seres.usb.upgrade:attr/itemShapeInsetTop = 0x7f030255
com.seres.usb.upgrade:attr/colorOnPrimaryContainer = 0x7f030101
com.seres.usb.upgrade:layout/abc_screen_simple = 0x7f0b0015
com.seres.usb.upgrade:color/switch_thumb_disabled_material_light = 0x7f0502e1
com.seres.usb.upgrade:dimen/tooltip_corner_radius = 0x7f060313
com.seres.usb.upgrade:attr/motionEasingAccelerated = 0x7f03032a
com.seres.usb.upgrade:string/mtrl_picker_confirm = 0x7f0f0072
com.seres.usb.upgrade:dimen/m3_btn_translation_z_base = 0x7f0600e2
com.seres.usb.upgrade:string/mtrl_picker_range_header_selected = 0x7f0f0081
com.seres.usb.upgrade:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1002dd
com.seres.usb.upgrade:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f060187
com.seres.usb.upgrade:attr/endIconContentDescription = 0x7f0301a2
com.seres.usb.upgrade:attr/colorOnBackground = 0x7f0300fb
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_container_height = 0x7f060117
com.seres.usb.upgrade:attr/nestedScrollFlags = 0x7f03034e
com.seres.usb.upgrade:styleable/SideSheetBehavior_Layout = 0x7f11007c
com.seres.usb.upgrade:color/m3_ref_palette_tertiary90 = 0x7f050140
com.seres.usb.upgrade:style/Theme.Material3.Light.Dialog.Alert = 0x7f10023e
com.seres.usb.upgrade:id/dialog_button = 0x7f08009c
com.seres.usb.upgrade:attr/animateCircleAngleTo = 0x7f030031
com.seres.usb.upgrade:drawable/notification_icon_background = 0x7f0700e0
com.seres.usb.upgrade:attr/actionBarItemBackground = 0x7f030003
com.seres.usb.upgrade:attr/constraintSetEnd = 0x7f030132
com.seres.usb.upgrade:attr/colorPrimaryVariant = 0x7f030119
com.seres.usb.upgrade:attr/expandedTitleTextAppearance = 0x7f0301bf
com.seres.usb.upgrade:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060176
com.seres.usb.upgrade:color/design_default_color_surface = 0x7f05004b
com.seres.usb.upgrade:id/titleDividerNoCustom = 0x7f0801d4
com.seres.usb.upgrade:attr/textAppearanceSearchResultTitle = 0x7f030448
com.seres.usb.upgrade:attr/contentScrim = 0x7f030146
com.seres.usb.upgrade:style/Widget.Material3.Chip.Input = 0x7f100367
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601c6
com.seres.usb.upgrade:attr/colorButtonNormal = 0x7f0300f4
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f100413
com.seres.usb.upgrade:style/Base.Widget.Material3.Chip = 0x7f100102
com.seres.usb.upgrade:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f060213
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f100127
com.seres.usb.upgrade:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f060196
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500b3
com.seres.usb.upgrade:styleable/SearchBar = 0x7f110078
com.seres.usb.upgrade:attr/cardMaxElevation = 0x7f03009e
com.seres.usb.upgrade:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b2
com.seres.usb.upgrade:attr/layout_constraintHorizontal_bias = 0x7f030289
com.seres.usb.upgrade:attr/flow_firstVerticalStyle = 0x7f0301ea
com.seres.usb.upgrade:attr/colorOnTertiaryContainer = 0x7f03010d
com.seres.usb.upgrade:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c014a
com.seres.usb.upgrade:attr/materialDisplayDividerStyle = 0x7f0302ee
com.seres.usb.upgrade:dimen/design_navigation_item_icon_padding = 0x7f060079
com.seres.usb.upgrade:attr/textInputStyle = 0x7f03045f
com.seres.usb.upgrade:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f10036f
com.seres.usb.upgrade:attr/borderRoundPercent = 0x7f030075
com.seres.usb.upgrade:attr/checkMarkCompat = 0x7f0300ae
com.seres.usb.upgrade:attr/extendStrategy = 0x7f0301c2
com.seres.usb.upgrade:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f050096
com.seres.usb.upgrade:attr/buttonStyle = 0x7f030096
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Body1 = 0x7f100195
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary20 = 0x7f0500c6
com.seres.usb.upgrade:dimen/mtrl_btn_stroke_size = 0x7f060264
com.seres.usb.upgrade:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f06019c
com.seres.usb.upgrade:dimen/m3_appbar_size_compact = 0x7f0600a9
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500b2
com.seres.usb.upgrade:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.seres.usb.upgrade:attr/errorIconTintMode = 0x7f0301b2
com.seres.usb.upgrade:style/Theme.Material3.Dark.Dialog.Alert = 0x7f10022b
com.seres.usb.upgrade:color/m3_sys_color_light_secondary_container = 0x7f0501cc
com.seres.usb.upgrade:attr/closeIconStartPadding = 0x7f0300e2
com.seres.usb.upgrade:attr/telltales_tailScale = 0x7f030427
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700d0
com.seres.usb.upgrade:style/Widget.Material3.NavigationView = 0x7f1003b6
com.seres.usb.upgrade:attr/listPreferredItemHeight = 0x7f0302c1
com.seres.usb.upgrade:attr/toolbarStyle = 0x7f030496
com.seres.usb.upgrade:layout/mtrl_calendar_day_of_week = 0x7f0b0049
com.seres.usb.upgrade:color/material_personalized_color_on_secondary_container = 0x7f050268
com.seres.usb.upgrade:attr/constraint_referenced_ids = 0x7f030134
com.seres.usb.upgrade:attr/flow_verticalGap = 0x7f0301f7
com.seres.usb.upgrade:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f10015a
com.seres.usb.upgrade:style/CardView = 0x7f100120
com.seres.usb.upgrade:drawable/abc_popup_background_mtrl_mult = 0x7f07005a
com.seres.usb.upgrade:attr/checkedChip = 0x7f0300b3
com.seres.usb.upgrade:attr/materialButtonToggleGroupStyle = 0x7f0302d8
com.seres.usb.upgrade:color/abc_primary_text_material_light = 0x7f05000c
com.seres.usb.upgrade:style/Widget.Material3.SearchBar = 0x7f1003bd
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c007b
com.seres.usb.upgrade:attr/constraintSetStart = 0x7f030133
com.seres.usb.upgrade:style/Base.Widget.Material3.FloatingActionButton = 0x7f100109
com.seres.usb.upgrade:attr/flow_lastHorizontalStyle = 0x7f0301f0
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700b7
com.seres.usb.upgrade:color/m3_sys_color_dynamic_light_secondary = 0x7f05019d
com.seres.usb.upgrade:layout/mtrl_search_bar = 0x7f0b005f
com.seres.usb.upgrade:attr/backgroundInsetTop = 0x7f03004c
com.seres.usb.upgrade:attr/badgeTextAppearance = 0x7f030059
com.seres.usb.upgrade:attr/badgeWithTextShapeAppearance = 0x7f030060
com.seres.usb.upgrade:attr/defaultMarginsEnabled = 0x7f030170
com.seres.usb.upgrade:attr/mock_label = 0x7f030314
com.seres.usb.upgrade:id/spring = 0x7f0801a3
com.seres.usb.upgrade:id/fitToContents = 0x7f0800c4
com.seres.usb.upgrade:attr/viewTransitionOnNegativeCross = 0x7f0304c0
com.seres.usb.upgrade:dimen/design_snackbar_padding_vertical = 0x7f060086
com.seres.usb.upgrade:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0157
com.seres.usb.upgrade:color/material_personalized_color_surface_container_low = 0x7f05027e
com.seres.usb.upgrade:attr/logoDescription = 0x7f0302ca
com.seres.usb.upgrade:attr/layout_constraintWidth_percent = 0x7f03029f
com.seres.usb.upgrade:attr/chipStrokeWidth = 0x7f0300ce
com.seres.usb.upgrade:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f100268
com.seres.usb.upgrade:attr/badgeWithTextRadius = 0x7f03005f
com.seres.usb.upgrade:attr/autoTransition = 0x7f030045
com.seres.usb.upgrade:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500a1
com.seres.usb.upgrade:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f060287
com.seres.usb.upgrade:attr/titleEnabled = 0x7f030487
com.seres.usb.upgrade:attr/panelMenuListWidth = 0x7f030368
com.seres.usb.upgrade:dimen/m3_sys_elevation_level0 = 0x7f0601e9
com.seres.usb.upgrade:color/mtrl_fab_ripple_color = 0x7f0502af
com.seres.usb.upgrade:dimen/m3_comp_menu_container_elevation = 0x7f060137
com.seres.usb.upgrade:id/mtrl_calendar_day_selector_frame = 0x7f08011b
com.seres.usb.upgrade:color/mtrl_filled_icon_tint = 0x7f0502b1
com.seres.usb.upgrade:attr/layout_goneMarginLeft = 0x7f0302a6
com.seres.usb.upgrade:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f100015
com.seres.usb.upgrade:attr/drawableBottomCompat = 0x7f030187
com.seres.usb.upgrade:drawable/abc_list_pressed_holo_light = 0x7f070052
com.seres.usb.upgrade:attr/singleLine = 0x7f0303cc
com.seres.usb.upgrade:styleable/TextInputLayout = 0x7f11008c
com.seres.usb.upgrade:style/Widget.Material3.Button.ElevatedButton = 0x7f10034d
com.seres.usb.upgrade:attr/rotationCenterId = 0x7f03039d
com.seres.usb.upgrade:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f100053
com.seres.usb.upgrade:attr/colorOnTertiaryFixed = 0x7f03010e
com.seres.usb.upgrade:color/m3_ref_palette_neutral80 = 0x7f050106
com.seres.usb.upgrade:attr/actionViewClass = 0x7f030025
com.seres.usb.upgrade:attr/shouldRemoveExpandedCorners = 0x7f0303bb
com.seres.usb.upgrade:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0031
com.seres.usb.upgrade:attr/alertDialogStyle = 0x7f03002b
com.seres.usb.upgrade:attr/clearsTag = 0x7f0300d8
com.seres.usb.upgrade:attr/contentInsetEndWithActions = 0x7f03013a
com.seres.usb.upgrade:color/m3_sys_color_secondary_fixed_dim = 0x7f0501e1
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500a3
com.seres.usb.upgrade:attr/borderWidth = 0x7f030076
com.seres.usb.upgrade:id/bottom = 0x7f08005d
com.seres.usb.upgrade:attr/fastScrollHorizontalThumbDrawable = 0x7f0301d3
com.seres.usb.upgrade:attr/colorOnSecondaryFixedVariant = 0x7f030108
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f10042a
com.seres.usb.upgrade:attr/chipSpacing = 0x7f0300c8
com.seres.usb.upgrade:styleable/Insets = 0x7f11003e
com.seres.usb.upgrade:attr/onTouchUp = 0x7f03035a
com.seres.usb.upgrade:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f06022f
com.seres.usb.upgrade:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f100430
com.seres.usb.upgrade:attr/checkedIconTint = 0x7f0300b9
com.seres.usb.upgrade:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602ec
com.seres.usb.upgrade:styleable/Chip = 0x7f11001d
com.seres.usb.upgrade:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f10025b
com.seres.usb.upgrade:id/visible_removing_fragment_view_tag = 0x7f0801f8
com.seres.usb.upgrade:attr/elevation = 0x7f03019b
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1001e1
com.seres.usb.upgrade:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f06024a
com.seres.usb.upgrade:color/material_personalized_color_tertiary_container = 0x7f050284
com.seres.usb.upgrade:dimen/m3_card_elevated_dragged_z = 0x7f0600e7
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500bb
com.seres.usb.upgrade:attr/constraint_referenced_tags = 0x7f030135
com.seres.usb.upgrade:dimen/mtrl_calendar_day_corner = 0x7f06026f
com.seres.usb.upgrade:attr/tickMarkTint = 0x7f03047c
com.seres.usb.upgrade:dimen/mtrl_calendar_landscape_header_width = 0x7f060280
com.seres.usb.upgrade:color/material_dynamic_tertiary60 = 0x7f05023f
com.seres.usb.upgrade:animator/design_appbar_state_list_animator = 0x7f020000
com.seres.usb.upgrade:color/material_dynamic_primary100 = 0x7f050220
com.seres.usb.upgrade:dimen/material_clock_hand_center_dot_radius = 0x7f06021f
com.seres.usb.upgrade:color/material_dynamic_primary99 = 0x7f05022a
com.seres.usb.upgrade:color/design_dark_default_color_primary = 0x7f050039
com.seres.usb.upgrade:attr/badgeStyle = 0x7f030057
com.seres.usb.upgrade:styleable/NavigationView = 0x7f11006c
com.seres.usb.upgrade:attr/carousel_touchUp_velocityThreshold = 0x7f0300ab
com.seres.usb.upgrade:integer/mtrl_calendar_year_selector_span = 0x7f090032
com.seres.usb.upgrade:attr/hideAnimationBehavior = 0x7f030216
com.seres.usb.upgrade:attr/layout_constraintBaseline_toBottomOf = 0x7f030276
com.seres.usb.upgrade:attr/carousel_previousState = 0x7f0300a8
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f100131
com.seres.usb.upgrade:attr/placeholder_emptyVisibility = 0x7f030379
com.seres.usb.upgrade:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f10009a
com.seres.usb.upgrade:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601a2
com.seres.usb.upgrade:attr/motionTarget = 0x7f030343
com.seres.usb.upgrade:attr/tabMaxWidth = 0x7f030415
com.seres.usb.upgrade:dimen/abc_action_button_min_width_material = 0x7f06000e
com.seres.usb.upgrade:attr/ensureMinTouchTargetSize = 0x7f0301ab
com.seres.usb.upgrade:attr/carousel_nextState = 0x7f0300a7
com.seres.usb.upgrade:color/material_dynamic_primary95 = 0x7f050229
com.seres.usb.upgrade:attr/cardViewStyle = 0x7f0300a1
com.seres.usb.upgrade:attr/layoutDuringTransition = 0x7f03026b
com.seres.usb.upgrade:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f10043c
com.seres.usb.upgrade:dimen/notification_right_icon_size = 0x7f06030c
com.seres.usb.upgrade:attr/motionPathRotate = 0x7f030340
com.seres.usb.upgrade:drawable/abc_ic_clear_material = 0x7f070040
com.seres.usb.upgrade:color/m3_chip_background_color = 0x7f050071
com.seres.usb.upgrade:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.seres.usb.upgrade:attr/layout_constraintHorizontal_weight = 0x7f03028b
com.seres.usb.upgrade:attr/cornerSizeTopRight = 0x7f030155
com.seres.usb.upgrade:attr/animateNavigationIcon = 0x7f030033
com.seres.usb.upgrade:attr/buttonGravity = 0x7f030090
com.seres.usb.upgrade:color/material_dynamic_primary80 = 0x7f050227
com.seres.usb.upgrade:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008e
com.seres.usb.upgrade:attr/shrinkMotionSpec = 0x7f0303c4
com.seres.usb.upgrade:attr/alertDialogButtonGroupStyle = 0x7f030029
com.seres.usb.upgrade:attr/brightness = 0x7f030089
com.seres.usb.upgrade:attr/alertDialogCenterButtons = 0x7f03002a
com.seres.usb.upgrade:attr/cardElevation = 0x7f03009c
com.seres.usb.upgrade:color/m3_sys_color_light_on_surface = 0x7f0501c3
com.seres.usb.upgrade:attr/maxLines = 0x7f030305
com.seres.usb.upgrade:attr/font = 0x7f0301fa
com.seres.usb.upgrade:attr/boxCornerRadiusBottomEnd = 0x7f030081
com.seres.usb.upgrade:id/mtrl_calendar_selection_frame = 0x7f080120
com.seres.usb.upgrade:attr/materialAlertDialogTheme = 0x7f0302d2
com.seres.usb.upgrade:color/m3_navigation_item_background_color = 0x7f050092
com.seres.usb.upgrade:attr/tooltipForegroundColor = 0x7f030498
com.seres.usb.upgrade:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f10041a
com.seres.usb.upgrade:dimen/notification_content_margin_start = 0x7f060307
com.seres.usb.upgrade:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f100359
com.seres.usb.upgrade:color/material_dynamic_neutral70 = 0x7f05020c
com.seres.usb.upgrade:attr/buttonPanelSideLayout = 0x7f030095
com.seres.usb.upgrade:id/clear_text = 0x7f08007a
com.seres.usb.upgrade:attr/drawerArrowStyle = 0x7f030190
com.seres.usb.upgrade:attr/buttonIconTint = 0x7f030093
com.seres.usb.upgrade:style/Platform.ThemeOverlay.AppCompat = 0x7f10013d
com.seres.usb.upgrade:id/x_right = 0x7f080201
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06011e
com.seres.usb.upgrade:dimen/design_bottom_sheet_peek_height_min = 0x7f06006d
com.seres.usb.upgrade:string/path_password_eye = 0x7f0f009c
com.seres.usb.upgrade:anim/m3_motion_fade_exit = 0x7f010024
com.seres.usb.upgrade:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f1003f9
com.seres.usb.upgrade:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602bf
com.seres.usb.upgrade:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014f
com.seres.usb.upgrade:style/MaterialAlertDialog.Material3 = 0x7f100123
com.seres.usb.upgrade:attr/endIconTint = 0x7f0301a7
com.seres.usb.upgrade:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060107
com.seres.usb.upgrade:attr/checkedIconGravity = 0x7f0300b6
com.seres.usb.upgrade:attr/cornerFamilyBottomRight = 0x7f03014d
com.seres.usb.upgrade:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f10037b
com.seres.usb.upgrade:color/m3_ref_palette_error70 = 0x7f0500f2
com.seres.usb.upgrade:attr/materialButtonStyle = 0x7f0302d7
com.seres.usb.upgrade:color/m3_sys_color_dark_on_surface_variant = 0x7f05015a
com.seres.usb.upgrade:attr/errorTextColor = 0x7f0301b5
com.seres.usb.upgrade:style/CardView.Dark = 0x7f100121
com.seres.usb.upgrade:attr/altSrc = 0x7f030030
com.seres.usb.upgrade:attr/animateRelativeTo = 0x7f030034
com.seres.usb.upgrade:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f060199
com.seres.usb.upgrade:attr/buttonBarButtonStyle = 0x7f03008a
com.seres.usb.upgrade:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1003e3
com.seres.usb.upgrade:attr/contentPaddingEnd = 0x7f030141
com.seres.usb.upgrade:attr/round = 0x7f03039e
com.seres.usb.upgrade:attr/startIconTint = 0x7f0303e5
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0165
com.seres.usb.upgrade:color/design_dark_default_color_on_background = 0x7f050034
com.seres.usb.upgrade:attr/imageButtonStyle = 0x7f03022f
com.seres.usb.upgrade:id/bestChoice = 0x7f08005b
com.seres.usb.upgrade:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501ad
com.seres.usb.upgrade:attr/colorOnContainerUnchecked = 0x7f0300fd
com.seres.usb.upgrade:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f10016b
com.seres.usb.upgrade:dimen/notification_right_side_padding_top = 0x7f06030d
com.seres.usb.upgrade:id/closest = 0x7f08007e
com.seres.usb.upgrade:attr/badgeHeight = 0x7f030053
com.seres.usb.upgrade:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f100312
com.seres.usb.upgrade:string/fab_transformation_sheet_behavior = 0x7f0f0033
com.seres.usb.upgrade:color/m3_ref_palette_neutral99 = 0x7f05010e
com.seres.usb.upgrade:attr/errorContentDescription = 0x7f0301ae
com.seres.usb.upgrade:attr/expandedTitleMarginTop = 0x7f0301be
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0168
com.seres.usb.upgrade:attr/boxStrokeWidth = 0x7f030087
com.seres.usb.upgrade:string/material_timepicker_minute = 0x7f0f0059
com.seres.usb.upgrade:color/abc_decor_view_status_guard = 0x7f050005
com.seres.usb.upgrade:attr/colorOnSurface = 0x7f030109
com.seres.usb.upgrade:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c0010
com.seres.usb.upgrade:attr/collapseIcon = 0x7f0300e7
com.seres.usb.upgrade:color/m3_ref_palette_neutral100 = 0x7f0500f9
com.seres.usb.upgrade:attr/iconPadding = 0x7f030227
com.seres.usb.upgrade:layout/mtrl_alert_select_dialog_item = 0x7f0b0044
com.seres.usb.upgrade:dimen/m3_extended_fab_top_padding = 0x7f0601ae
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1001bb
com.seres.usb.upgrade:layout/design_layout_snackbar = 0x7f0b0020
com.seres.usb.upgrade:attr/bottomNavigationStyle = 0x7f03007a
com.seres.usb.upgrade:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f10007e
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500c0
com.seres.usb.upgrade:attr/listItemLayout = 0x7f0302bd
com.seres.usb.upgrade:styleable/MotionTelltales = 0x7f110068
com.seres.usb.upgrade:attr/dropDownListViewStyle = 0x7f030194
com.seres.usb.upgrade:color/m3_sys_color_dark_outline_variant = 0x7f05015e
com.seres.usb.upgrade:attr/liftOnScrollColor = 0x7f0302b3
com.seres.usb.upgrade:attr/collapsedTitleTextColor = 0x7f0300eb
com.seres.usb.upgrade:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f10022d
com.seres.usb.upgrade:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.seres.usb.upgrade:attr/colorErrorContainer = 0x7f0300fa
com.seres.usb.upgrade:dimen/m3_comp_input_chip_container_height = 0x7f060132
com.seres.usb.upgrade:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.seres.usb.upgrade:color/material_dynamic_neutral_variant70 = 0x7f050219
com.seres.usb.upgrade:attr/collapsedTitleGravity = 0x7f0300e9
com.seres.usb.upgrade:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f100328
com.seres.usb.upgrade:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012b
com.seres.usb.upgrade:attr/colorSurfaceBright = 0x7f030120
com.seres.usb.upgrade:attr/textureEffect = 0x7f030467
com.seres.usb.upgrade:attr/defaultScrollFlagsEnabled = 0x7f030172
com.seres.usb.upgrade:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f10021e
com.seres.usb.upgrade:dimen/mtrl_btn_padding_left = 0x7f06025f
com.seres.usb.upgrade:layout/design_layout_tab_icon = 0x7f0b0022
com.seres.usb.upgrade:dimen/compat_notification_large_icon_max_height = 0x7f06005b
com.seres.usb.upgrade:style/Platform.MaterialComponents.Light = 0x7f10013b
com.seres.usb.upgrade:attr/fastScrollVerticalTrackDrawable = 0x7f0301d6
com.seres.usb.upgrade:styleable/OnSwipe = 0x7f11006e
com.seres.usb.upgrade:color/m3_ref_palette_primary70 = 0x7f050124
com.seres.usb.upgrade:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f100302
com.seres.usb.upgrade:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0138
com.seres.usb.upgrade:attr/bottomAppBarStyle = 0x7f030078
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0164
com.seres.usb.upgrade:attr/cardForegroundColor = 0x7f03009d
com.seres.usb.upgrade:attr/closeIcon = 0x7f0300de
com.seres.usb.upgrade:attr/behavior_hideable = 0x7f03006d
com.seres.usb.upgrade:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00d1
com.seres.usb.upgrade:dimen/m3_btn_padding_bottom = 0x7f0600d9
com.seres.usb.upgrade:style/Widget.Material3.Toolbar = 0x7f1003da
com.seres.usb.upgrade:style/TextAppearance.AppCompat.Title = 0x7f1001ae
com.seres.usb.upgrade:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f100148
com.seres.usb.upgrade:color/material_personalized_color_primary_container = 0x7f050271
com.seres.usb.upgrade:attr/materialCardViewElevatedStyle = 0x7f0302e8
com.seres.usb.upgrade:integer/config_tooltipAnimTime = 0x7f090005
com.seres.usb.upgrade:attr/behavior_autoHide = 0x7f030067
com.seres.usb.upgrade:attr/waveVariesBy = 0x7f0304ca
com.seres.usb.upgrade:color/material_dynamic_primary60 = 0x7f050225
com.seres.usb.upgrade:color/design_fab_shadow_mid_color = 0x7f05004e
com.seres.usb.upgrade:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.seres.usb.upgrade:color/material_dynamic_secondary60 = 0x7f050232
com.seres.usb.upgrade:attr/tabSelectedTextColor = 0x7f030420
com.seres.usb.upgrade:color/m3_dynamic_default_color_primary_text = 0x7f050081
com.seres.usb.upgrade:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f03032e
com.seres.usb.upgrade:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003c
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f100029
com.seres.usb.upgrade:string/mtrl_picker_cancel = 0x7f0f0071
com.seres.usb.upgrade:dimen/m3_card_elevated_disabled_z = 0x7f0600e6
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary90 = 0x7f0500cd
com.seres.usb.upgrade:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301d8
com.seres.usb.upgrade:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1002da
com.seres.usb.upgrade:integer/mtrl_card_anim_duration_ms = 0x7f090034
com.seres.usb.upgrade:dimen/m3_comp_suggestion_chip_container_height = 0x7f060184
com.seres.usb.upgrade:attr/trackHeight = 0x7f0304a8
com.seres.usb.upgrade:attr/thumbRadius = 0x7f030472
com.seres.usb.upgrade:dimen/abc_action_bar_default_height_material = 0x7f060002
com.seres.usb.upgrade:attr/colorOnContainer = 0x7f0300fc
com.seres.usb.upgrade:color/abc_secondary_text_material_dark = 0x7f050011
com.seres.usb.upgrade:attr/haloColor = 0x7f03020e
com.seres.usb.upgrade:attr/boxStrokeWidthFocused = 0x7f030088
com.seres.usb.upgrade:styleable/MaterialAlertDialog = 0x7f11004c
com.seres.usb.upgrade:color/mtrl_navigation_item_icon_tint = 0x7f0502b9
com.seres.usb.upgrade:attr/imagePanX = 0x7f030230
com.seres.usb.upgrade:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602b1
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_container_height = 0x7f06013b
com.seres.usb.upgrade:id/path = 0x7f08015f
com.seres.usb.upgrade:attr/layout_goneMarginBottom = 0x7f0302a4
com.seres.usb.upgrade:attr/badgeWidePadding = 0x7f03005c
com.seres.usb.upgrade:drawable/ic_call_answer_video_low = 0x7f07008c
com.seres.usb.upgrade:style/Widget.Compat.NotificationActionText = 0x7f10032f
com.seres.usb.upgrade:color/m3_slider_halo_color = 0x7f050147
com.seres.usb.upgrade:color/bright_foreground_inverse_material_dark = 0x7f050024
com.seres.usb.upgrade:drawable/m3_appbar_background = 0x7f07009d
com.seres.usb.upgrade:attr/badgeShapeAppearanceOverlay = 0x7f030056
com.seres.usb.upgrade:attr/backgroundStacked = 0x7f03004f
com.seres.usb.upgrade:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f10009e
com.seres.usb.upgrade:attr/closeIconEndPadding = 0x7f0300e0
com.seres.usb.upgrade:dimen/mtrl_min_touch_target_size = 0x7f0602bb
com.seres.usb.upgrade:attr/carousel_emptyViewsBehavior = 0x7f0300a3
com.seres.usb.upgrade:attr/recyclerViewStyle = 0x7f030395
com.seres.usb.upgrade:id/title_template = 0x7f0801d5
com.seres.usb.upgrade:attr/cardCornerRadius = 0x7f03009b
com.seres.usb.upgrade:color/design_default_color_secondary = 0x7f050049
com.seres.usb.upgrade:dimen/m3_navigation_rail_icon_size = 0x7f0601c4
com.seres.usb.upgrade:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f100089
com.seres.usb.upgrade:attr/behavior_draggable = 0x7f030069
com.seres.usb.upgrade:attr/indeterminateProgressStyle = 0x7f030235
com.seres.usb.upgrade:id/action_bar_root = 0x7f080036
com.seres.usb.upgrade:drawable/mtrl_switch_thumb_pressed = 0x7f0700cf
com.seres.usb.upgrade:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060162
com.seres.usb.upgrade:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1002ad
com.seres.usb.upgrade:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301c4
com.seres.usb.upgrade:attr/buttonTint = 0x7f030098
com.seres.usb.upgrade:color/primary_dark_material_dark = 0x7f0502d2
com.seres.usb.upgrade:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060133
com.seres.usb.upgrade:attr/drawableEndCompat = 0x7f030188
com.seres.usb.upgrade:color/material_dynamic_neutral40 = 0x7f050209
com.seres.usb.upgrade:animator/fragment_open_enter = 0x7f020007
com.seres.usb.upgrade:id/mtrl_picker_title_text = 0x7f08012f
com.seres.usb.upgrade:attr/verticalOffsetWithText = 0x7f0304bc
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000f
com.seres.usb.upgrade:attr/animateMenuItems = 0x7f030032
com.seres.usb.upgrade:styleable/MaterialDivider = 0x7f110056
com.seres.usb.upgrade:dimen/abc_text_size_headline_material = 0x7f060047
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500d9
com.seres.usb.upgrade:id/open_search_view_clear_button = 0x7f080149
com.seres.usb.upgrade:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f070020
com.seres.usb.upgrade:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1000cf
com.seres.usb.upgrade:attr/alpha = 0x7f03002e
com.seres.usb.upgrade:attr/customNavigationLayout = 0x7f030167
com.seres.usb.upgrade:attr/layout_constrainedHeight = 0x7f030272
com.seres.usb.upgrade:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.seres.usb.upgrade:attr/tabIndicatorGravity = 0x7f030412
com.seres.usb.upgrade:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f100435
com.seres.usb.upgrade:dimen/notification_large_icon_width = 0x7f060309
com.seres.usb.upgrade:color/m3_ref_palette_neutral90 = 0x7f050108
com.seres.usb.upgrade:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301e0
com.seres.usb.upgrade:dimen/abc_list_item_height_small_material = 0x7f060032
com.seres.usb.upgrade:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301d9
com.seres.usb.upgrade:attr/region_heightLessThan = 0x7f030396
com.seres.usb.upgrade:style/Widget.Material3.SearchView.Toolbar = 0x7f1003c1
com.seres.usb.upgrade:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0065
com.seres.usb.upgrade:attr/boxBackgroundColor = 0x7f03007e
com.seres.usb.upgrade:attr/customIntegerValue = 0x7f030166
com.seres.usb.upgrade:dimen/m3_comp_navigation_rail_container_width = 0x7f06014a
com.seres.usb.upgrade:color/material_dynamic_neutral10 = 0x7f050205
com.seres.usb.upgrade:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f100085
com.seres.usb.upgrade:attr/backgroundInsetBottom = 0x7f030049
com.seres.usb.upgrade:styleable/MaterialShape = 0x7f110058
com.seres.usb.upgrade:attr/colorOnSurfaceInverse = 0x7f03010a
com.seres.usb.upgrade:attr/boxCornerRadiusTopEnd = 0x7f030083
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f0601fc
com.seres.usb.upgrade:attr/badgeGravity = 0x7f030052
com.seres.usb.upgrade:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f10010b
com.seres.usb.upgrade:attr/colorSurfaceDim = 0x7f030126
com.seres.usb.upgrade:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.seres.usb.upgrade:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070044
com.seres.usb.upgrade:color/material_dynamic_primary30 = 0x7f050222
com.seres.usb.upgrade:string/bottomsheet_action_expand_halfway = 0x7f0f0021
com.seres.usb.upgrade:color/m3_button_background_color_selector = 0x7f050064
com.seres.usb.upgrade:attr/background = 0x7f030047
com.seres.usb.upgrade:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.seres.usb.upgrade:color/mtrl_filled_stroke_color = 0x7f0502b2
com.seres.usb.upgrade:attr/addElevationShadow = 0x7f030028
com.seres.usb.upgrade:attr/actionBarTabStyle = 0x7f030009
com.seres.usb.upgrade:dimen/m3_navigation_rail_item_padding_top = 0x7f0601cb
com.seres.usb.upgrade:attr/buttonBarNegativeButtonStyle = 0x7f03008b
com.seres.usb.upgrade:dimen/m3_comp_search_bar_avatar_size = 0x7f06016c
com.seres.usb.upgrade:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1001a8
com.seres.usb.upgrade:attr/flow_horizontalGap = 0x7f0301ed
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f050187
com.seres.usb.upgrade:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060118
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500af
com.seres.usb.upgrade:color/material_personalized_color_error = 0x7f050260
com.seres.usb.upgrade:style/Base.Widget.AppCompat.RatingBar = 0x7f1000f0
com.seres.usb.upgrade:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.seres.usb.upgrade:style/Theme.Design.Light = 0x7f100224
com.seres.usb.upgrade:style/Base.V21.Theme.MaterialComponents = 0x7f1000a6
com.seres.usb.upgrade:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.seres.usb.upgrade:attr/thumbIconTint = 0x7f030470
com.seres.usb.upgrade:drawable/ic_mtrl_chip_checked_circle = 0x7f070099
com.seres.usb.upgrade:dimen/compat_button_inset_horizontal_material = 0x7f060056
com.seres.usb.upgrade:attr/actionBarSplitStyle = 0x7f030006
com.seres.usb.upgrade:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007d
com.seres.usb.upgrade:attr/motionEasingLinear = 0x7f030330
com.seres.usb.upgrade:attr/layout_constraintEnd_toEndOf = 0x7f03027f
com.seres.usb.upgrade:attr/region_heightMoreThan = 0x7f030397
com.seres.usb.upgrade:attr/layout_constraintGuide_end = 0x7f030282
com.seres.usb.upgrade:dimen/m3_comp_outlined_card_outline_width = 0x7f060155
com.seres.usb.upgrade:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002e
com.seres.usb.upgrade:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f100133
com.seres.usb.upgrade:integer/m3_badge_max_number = 0x7f090009
com.seres.usb.upgrade:color/m3_sys_color_light_on_primary = 0x7f0501bf
com.seres.usb.upgrade:attr/actionBarSize = 0x7f030005
com.seres.usb.upgrade:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060146
com.seres.usb.upgrade:attr/keyPositionType = 0x7f03025f
com.seres.usb.upgrade:attr/bottomSheetDialogTheme = 0x7f03007b
com.seres.usb.upgrade:drawable/abc_btn_radio_material_anim = 0x7f070033
com.seres.usb.upgrade:attr/colorPrimaryFixed = 0x7f030115
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060211
com.seres.usb.upgrade:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f050179
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500b6
com.seres.usb.upgrade:string/mtrl_checkbox_button_icon_path_checked = 0x7f0f005e
com.seres.usb.upgrade:dimen/tooltip_vertical_padding = 0x7f060318
com.seres.usb.upgrade:id/tag_accessibility_clickable_spans = 0x7f0801b6
com.seres.usb.upgrade:dimen/design_snackbar_min_width = 0x7f060084
com.seres.usb.upgrade:attr/content = 0x7f030137
com.seres.usb.upgrade:attr/flow_horizontalBias = 0x7f0301ec
com.seres.usb.upgrade:attr/behavior_skipCollapsed = 0x7f030072
com.seres.usb.upgrade:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013f
com.seres.usb.upgrade:color/m3_ref_palette_secondary10 = 0x7f05012a
com.seres.usb.upgrade:style/Base.TextAppearance.AppCompat.Subhead = 0x7f10002d
com.seres.usb.upgrade:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0103
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_primary80 = 0x7f0500cc
com.seres.usb.upgrade:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.seres.usb.upgrade:color/background_floating_material_dark = 0x7f05001d
com.seres.usb.upgrade:id/ignore = 0x7f0800de
com.seres.usb.upgrade:attr/actionModePasteDrawable = 0x7f030019
com.seres.usb.upgrade:attr/overlay = 0x7f03035c
com.seres.usb.upgrade:animator/m3_appbar_state_list_animator = 0x7f020009
com.seres.usb.upgrade:attr/largeFontVerticalOffsetAdjustment = 0x7f030266
com.seres.usb.upgrade:style/Widget.AppCompat.ActivityChooserView = 0x7f1002ee
com.seres.usb.upgrade:attr/tickRadiusInactive = 0x7f03047f
com.seres.usb.upgrade:attr/chipMinTouchTargetSize = 0x7f0300c7
com.seres.usb.upgrade:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f06027e
com.seres.usb.upgrade:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f100084
com.seres.usb.upgrade:attr/actionOverflowButtonStyle = 0x7f030021
com.seres.usb.upgrade:attr/layout_collapseMode = 0x7f030270
com.seres.usb.upgrade:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1002ba
com.seres.usb.upgrade:attr/actionButtonStyle = 0x7f03000d
com.seres.usb.upgrade:id/disablePostScroll = 0x7f0800a1
com.seres.usb.upgrade:dimen/mtrl_tooltip_cornerSize = 0x7f0602ff
com.seres.usb.upgrade:dimen/mtrl_switch_thumb_icon_size = 0x7f0602f0
com.seres.usb.upgrade:styleable/MaterialTimePicker = 0x7f11005c
com.seres.usb.upgrade:style/Widget.AppCompat.ListPopupWindow = 0x7f100316
com.seres.usb.upgrade:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011b
com.seres.usb.upgrade:color/material_personalized_color_tertiary = 0x7f050283
com.seres.usb.upgrade:attr/titleTextEllipsize = 0x7f030491
com.seres.usb.upgrade:style/Base.Widget.Material3.TabLayout = 0x7f10010f
com.seres.usb.upgrade:attr/constraintSet = 0x7f030131
com.seres.usb.upgrade:dimen/mtrl_extended_fab_icon_size = 0x7f0602a5
com.seres.usb.upgrade:attr/layout_editor_absoluteX = 0x7f0302a1
com.seres.usb.upgrade:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0166
com.seres.usb.upgrade:attr/actionOverflowMenuStyle = 0x7f030022
com.seres.usb.upgrade:style/TextAppearance.MaterialComponents.Button = 0x7f1001fb
com.seres.usb.upgrade:color/design_fab_shadow_start_color = 0x7f05004f
com.seres.usb.upgrade:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301e2
com.seres.usb.upgrade:color/m3_ref_palette_error0 = 0x7f0500ea
com.seres.usb.upgrade:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.seres.usb.upgrade:color/m3_ref_palette_primary95 = 0x7f050127
com.seres.usb.upgrade:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012e
com.seres.usb.upgrade:color/mtrl_textinput_focused_box_stroke_color = 0x7f0502ce
com.seres.usb.upgrade:attr/floatingActionButtonSmallStyle = 0x7f0301e1
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f0601fe
com.seres.usb.upgrade:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.seres.usb.upgrade:color/material_personalized_color_error_container = 0x7f050261
com.seres.usb.upgrade:animator/fragment_open_exit = 0x7f020008
com.seres.usb.upgrade:color/m3_simple_item_ripple_color = 0x7f050145
com.seres.usb.upgrade:animator/m3_btn_state_list_anim = 0x7f02000b
com.seres.usb.upgrade:style/Widget.Material3.MaterialCalendar.Day = 0x7f100392
com.seres.usb.upgrade:color/m3_ref_palette_neutral6 = 0x7f050103
com.seres.usb.upgrade:attr/actionLayout = 0x7f03000f
com.seres.usb.upgrade:attr/fontProviderQuery = 0x7f030201
com.seres.usb.upgrade:id/beginning = 0x7f08005a
com.seres.usb.upgrade:attr/actionModeTheme = 0x7f03001f
com.seres.usb.upgrade:attr/motionEasingStandardInterpolator = 0x7f030335
com.seres.usb.upgrade:attr/iconEndPadding = 0x7f030225
com.seres.usb.upgrade:color/m3_sys_color_light_surface_dim = 0x7f0501d4
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral0 = 0x7f05009e
com.seres.usb.upgrade:attr/customFloatValue = 0x7f030165
com.seres.usb.upgrade:string/mtrl_checkbox_state_description_indeterminate = 0x7f0f0067
com.seres.usb.upgrade:attr/flow_lastVerticalBias = 0x7f0301f1
com.seres.usb.upgrade:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602ae
com.seres.usb.upgrade:attr/collapsedTitleTextAppearance = 0x7f0300ea
com.seres.usb.upgrade:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
com.seres.usb.upgrade:animator/fragment_close_enter = 0x7f020003
com.seres.usb.upgrade:attr/layout_scrollInterpolator = 0x7f0302b0
com.seres.usb.upgrade:drawable/m3_avd_show_password = 0x7f07009f
com.seres.usb.upgrade:drawable/abc_item_background_holo_dark = 0x7f07004b
com.seres.usb.upgrade:color/m3_ref_palette_error50 = 0x7f0500f0
com.seres.usb.upgrade:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f100443
com.seres.usb.upgrade:dimen/m3_comp_slider_inactive_track_height = 0x7f060182
com.seres.usb.upgrade:id/navigation_bar_item_icon_view = 0x7f080134
com.seres.usb.upgrade:anim/abc_slide_in_bottom = 0x7f010006
com.seres.usb.upgrade:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.seres.usb.upgrade:attr/hideMotionSpec = 0x7f030217
com.seres.usb.upgrade:attr/popupWindowStyle = 0x7f03037e
com.seres.usb.upgrade:anim/design_snackbar_out = 0x7f01001b
com.seres.usb.upgrade:color/material_dynamic_neutral90 = 0x7f05020e
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500bf
com.seres.usb.upgrade:dimen/m3_sys_elevation_level3 = 0x7f0601ec
com.seres.usb.upgrade:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1001db
com.seres.usb.upgrade:attr/iconSize = 0x7f030228
com.seres.usb.upgrade:drawable/design_fab_background = 0x7f070083
com.seres.usb.upgrade:attr/customStringValue = 0x7f03016a
com.seres.usb.upgrade:attr/menu = 0x7f03030a
com.seres.usb.upgrade:style/ThemeOverlay.AppCompat.DayNight = 0x7f100277
com.seres.usb.upgrade:id/beginOnFirstDraw = 0x7f080059
com.seres.usb.upgrade:color/m3_tabs_icon_color_secondary = 0x7f0501e5
com.seres.usb.upgrade:attr/barrierAllowsGoneWidgets = 0x7f030064
com.seres.usb.upgrade:dimen/m3_btn_padding_top = 0x7f0600dc
com.seres.usb.upgrade:color/background_material_dark = 0x7f05001f
com.seres.usb.upgrade:attr/actionModeCloseDrawable = 0x7f030015
com.seres.usb.upgrade:string/mtrl_picker_day_of_week_column_header = 0x7f0f0076
com.seres.usb.upgrade:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700ba
com.seres.usb.upgrade:string/mtrl_switch_thumb_path_morphing = 0x7f0f0093
com.seres.usb.upgrade:attr/itemIconPadding = 0x7f030246
com.seres.usb.upgrade:attr/layout_constraintCircle = 0x7f03027b
com.seres.usb.upgrade:id/withText = 0x7f0801fa
com.seres.usb.upgrade:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.seres.usb.upgrade:attr/cornerFamilyTopRight = 0x7f03014f
com.seres.usb.upgrade:anim/m3_motion_fade_enter = 0x7f010023
com.seres.usb.upgrade:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.seres.usb.upgrade:attr/itemHorizontalTranslationEnabled = 0x7f030245
com.seres.usb.upgrade:attr/contentInsetRight = 0x7f03013c
com.seres.usb.upgrade:id/progress_horizontal = 0x7f08016a
com.seres.usb.upgrade:dimen/mtrl_calendar_navigation_top_padding = 0x7f060286
com.seres.usb.upgrade:style/Base.DialogWindowTitle.AppCompat = 0x7f100011
com.seres.usb.upgrade:attr/textAppearanceHeadlineMedium = 0x7f03043b
com.seres.usb.upgrade:attr/actionBarDivider = 0x7f030002
com.seres.usb.upgrade:color/m3_sys_color_primary_fixed = 0x7f0501de
com.seres.usb.upgrade:layout/mtrl_calendar_horizontal = 0x7f0b004b
com.seres.usb.upgrade:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f06020c
com.seres.usb.upgrade:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500a4

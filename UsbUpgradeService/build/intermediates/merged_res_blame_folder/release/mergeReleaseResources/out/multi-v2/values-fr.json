{"logs": [{"outputFile": "com.seres.usb.upgrade.UsbUpgradeService-mergeReleaseResources-30:/values-fr/values-fr.xml", "map": [{"source": "/home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2982,3075,3154,3209,3260,3326,3406,3487,3590,3670,3743,3821,3894,3966,4078,4171,4243,4335,4427,4501,4585,4677,4734,4818,4884,4967,5054,5116,5180,5243,5321,5423,5527,5624,5728,5787,5842", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2977,3070,3149,3204,3255,3321,3401,3482,3585,3665,3738,3816,3889,3961,4073,4166,4238,4330,4422,4496,4580,4672,4729,4813,4879,4962,5049,5111,5175,5238,5316,5418,5522,5619,5723,5782,5837,5926"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,4563,4629,4726,4809,4875,4977,5042,5117,5173,5252,5312,5366,5488,5547,5609,5663,5745,5880,5972,6056,6200,6279,6360,6501,6594,6673,6728,6779,6845,6925,7006,7109,7189,7262,7340,7413,7485,7597,7690,7762,7854,7946,8020,8104,8196,8253,8337,8403,8486,8573,8635,8699,8762,8840,8942,9046,9143,9247,9306,9361", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,4624,4721,4804,4870,4972,5037,5112,5168,5247,5307,5361,5483,5542,5604,5658,5740,5875,5967,6051,6195,6274,6355,6496,6589,6668,6723,6774,6840,6920,7001,7104,7184,7257,7335,7408,7480,7592,7685,7757,7849,7941,8015,8099,8191,8248,8332,8398,8481,8568,8630,8694,8757,8835,8937,9041,9138,9242,9301,9356,9445"}}, {"source": "/home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,9450", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,9532"}}, {"source": "/home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,9537", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,9633"}}]}]}
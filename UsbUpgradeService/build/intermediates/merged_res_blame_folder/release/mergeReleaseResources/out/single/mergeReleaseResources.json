[{"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/drawable_status_background.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/drawable/status_background.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xhdpi_logo.png.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xhdpi/logo.png"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-anydpi_ic_launcher.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/drawable_button_background.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/drawable/button_background.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/xml_file_paths.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/xml/file_paths.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/xml_backup_rules.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/xml/backup_rules.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/drawable_section_background.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/drawable/section_background.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/layout_item_storage_device.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/layout/item_storage_device.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-mdpi_ic_launcher.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/layout_activity_main.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/layout/activity_main.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-hdpi_ic_launcher.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.seres.usb.upgrade.UsbUpgradeService-release-32:/drawable_ic_launcher_background.xml.flat", "source": "com.seres.usb.upgrade.UsbUpgradeService-main-33:/drawable/ic_launcher_background.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/xml_data_extraction_rules.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/xml/data_extraction_rules.xml"}, {"merged": "com.seres.usb.upgrade.UsbUpgradeService-release-32:/drawable_ic_launcher_foreground.xml.flat", "source": "com.seres.usb.upgrade.UsbUpgradeService-main-33:/drawable/ic_launcher_foreground.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-release-32:/drawable_ic_usb.xml.flat", "source": "/home/<USER>/.gradle/daemon/8.9/com.seres.usb.upgrade.UsbUpgradeService-main-33:/drawable/ic_usb.xml"}]
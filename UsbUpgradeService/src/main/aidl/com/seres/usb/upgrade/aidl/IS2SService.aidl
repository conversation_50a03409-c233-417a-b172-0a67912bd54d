// IS2SService.aidl
package com.seres.usb.upgrade.aidl;

import com.seres.usb.upgrade.aidl.IAsyncResultCallback;
import com.seres.usb.upgrade.aidl.IS2SReportListener;

/**
 * S2S服务接口
 */
interface IS2SService {
    /**
     * 同步调用服务
     */
    Bundle invoke(int appId, int serviceHashId, in Bundle params);
    
    /**
     * 异步调用服务
     */
    void invokeAsync(int appId, int serviceHashId, in Bundle params, IAsyncResultCallback callback);
    
    /**
     * 注册信号监听器
     */
    void registerS2SSignalListener(int appId, IS2SReportListener listener, in int[] initSignalHashIdList);
    
    /**
     * 取消注册信号监听器
     */
    void unregisterS2SSignalListener(int appId);
}

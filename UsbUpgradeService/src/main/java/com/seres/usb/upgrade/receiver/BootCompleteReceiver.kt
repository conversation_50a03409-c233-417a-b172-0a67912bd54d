package com.seres.usb.upgrade.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.seres.usb.upgrade.service.UsbDetectionService
import com.seres.usb.upgrade.utils.LogUtils

/**
 * 开机启动接收器
 * 系统启动后自动启动USB检测服务
 */
class BootCompleteReceiver : BroadcastReceiver() {
    
    private val TAG = "BootCompleteReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON" -> {
                LogUtils.i(TAG, "System boot completed, starting USB detection service")
                
                try {
                    // 启动USB检测服务
                    val serviceIntent = Intent(context, UsbDetectionService::class.java)
                    context.startForegroundService(serviceIntent)
                    
                    LogUtils.i(TAG, "USB detection service started on boot")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "Error starting USB detection service on boot: ${e.message}")
                }
            }
        }
    }
}

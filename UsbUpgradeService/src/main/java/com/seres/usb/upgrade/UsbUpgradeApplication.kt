package com.seres.usb.upgrade

import android.app.Application
import android.content.Intent
import com.seres.usb.upgrade.service.UsbDetectionService
import com.seres.usb.upgrade.utils.LogUtils

/**
 * USB升级服务应用程序类
 * 负责应用程序的初始化和全局配置
 */
class UsbUpgradeApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志工具
        LogUtils.init(this)
        LogUtils.d(TAG, "UsbUpgradeApplication onCreate")
        
        // 启动USB检测服务
        startUsbDetectionService()
    }
    
    /**
     * 启动USB检测服务
     */
    private fun startUsbDetectionService() {
        try {
            val intent = Intent(this, UsbDetectionService::class.java)
            startForegroundService(intent)
            LogUtils.d(TAG, "USB检测服务启动成功")
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动USB检测服务失败", e)
        }
    }
    
    companion object {
        private const val TAG = "UsbUpgradeApplication"
    }
}

package com.seres.usb.upgrade.model

import android.os.Parcel
import android.os.Parcelable
import java.io.Serializable

/**
 * 升级包信息数据类
 */
data class UpgradePackageInfo(
    var packageName: String = "",
    var packageSize: Long = 0L,
    var packagePath: String = "",
    var packageVersion: String = "",
    var targetEcu: String = "",
    var checksum: String = "",
    var packageType: String = "" // firmware, config, app等
) : Parcelable, Serializable {
    
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readLong(),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: ""
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(packageName)
        parcel.writeLong(packageSize)
        parcel.writeString(packagePath)
        parcel.writeString(packageVersion)
        parcel.writeString(targetEcu)
        parcel.writeString(checksum)
        parcel.writeString(packageType)
    }

    override fun describeContents(): Int = 0

    companion object CREATOR : Parcelable.Creator<UpgradePackageInfo> {
        override fun createFromParcel(parcel: Parcel): UpgradePackageInfo {
            return UpgradePackageInfo(parcel)
        }

        override fun newArray(size: Int): Array<UpgradePackageInfo?> {
            return arrayOfNulls(size)
        }
    }
}

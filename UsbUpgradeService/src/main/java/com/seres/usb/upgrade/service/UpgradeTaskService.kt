package com.seres.usb.upgrade.service

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import com.seres.usb.upgrade.manager.UpgradeTaskManager
import com.seres.usb.upgrade.model.UpgradeTaskInfo
import com.seres.usb.upgrade.utils.LogUtils

/**
 * 升级任务服务
 * 提供外部接口用于管理升级任务
 */
class UpgradeTaskService : Service() {
    
    private val TAG = "UpgradeTaskService"
    private var upgradeTaskManager: UpgradeTaskManager? = null
    
    /**
     * 服务Binder
     */
    inner class UpgradeTaskBinder : Binder() {
        fun getService(): UpgradeTaskService = this@UpgradeTaskService
    }
    
    private val binder = UpgradeTaskBinder()
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "UpgradeTaskService onCreate")
        upgradeTaskManager = UpgradeTaskManager.getInstance(this)
    }
    
    override fun onBind(intent: Intent?): IBinder {
        LogUtils.i(TAG, "UpgradeTaskService onBind")
        return binder
    }
    
    override fun onUnbind(intent: Intent?): Boolean {
        LogUtils.i(TAG, "UpgradeTaskService onUnbind")
        return super.onUnbind(intent)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "UpgradeTaskService onDestroy")
    }
    
    /**
     * 获取活跃任务列表
     */
    fun getActiveTasks(): List<UpgradeTaskInfo> {
        return upgradeTaskManager?.getActiveTasks() ?: emptyList()
    }
    
    /**
     * 获取指定任务
     */
    fun getTask(taskId: String): UpgradeTaskInfo? {
        return upgradeTaskManager?.getTask(taskId)
    }
    
    /**
     * 完成任务
     */
    fun completeTask(taskId: String) {
        upgradeTaskManager?.completeTask(taskId)
    }
    
    /**
     * 任务失败
     */
    fun failTask(taskId: String, errorMessage: String) {
        upgradeTaskManager?.failTask(taskId, errorMessage)
    }
    
    /**
     * 取消任务
     */
    fun cancelTasksByUsbPath(usbPath: String) {
        upgradeTaskManager?.cancelTasksByUsbPath(usbPath)
    }
}

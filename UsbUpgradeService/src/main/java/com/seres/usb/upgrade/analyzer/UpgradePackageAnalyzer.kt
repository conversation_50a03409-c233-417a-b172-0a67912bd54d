package com.seres.usb.upgrade.analyzer

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.seres.usb.upgrade.model.UpgradePackageInfo
import com.seres.usb.upgrade.model.UpgradeTaskInfo
import com.seres.usb.upgrade.model.UpgradeTaskStatus
import com.seres.usb.upgrade.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest
import java.util.*

/**
 * 升级包分析器
 * 分析U盘中的升级包和配置文件
 */
class UpgradePackageAnalyzer {
    
    private val TAG = "UpgradePackageAnalyzer"
    private val gson = Gson()
    
    // 支持的升级包文件扩展名
    private val supportedExtensions = setOf("bin", "hex", "img", "tar", "zip", "apk", "so", "conf", "json", "xml")
    
    // 配置文件名
    private val configFileName = "upgrade_config.json"
    
    /**
     * 分析U盘中的升级包
     */
    fun analyzeUpgradePackages(usbDir: File): UpgradeTaskInfo? {
        LogUtils.i(TAG, "Analyzing upgrade packages in: ${usbDir.absolutePath}")
        
        try {
            // 查找配置文件
            val configFile = File(usbDir, configFileName)
            val upgradeConfig = if (configFile.exists()) {
                parseUpgradeConfig(configFile)
            } else {
                LogUtils.w(TAG, "No config file found, using default analysis")
                null
            }
            
            // 扫描升级包文件
            val packageList = scanUpgradePackages(usbDir, upgradeConfig)
            
            if (packageList.isEmpty()) {
                LogUtils.i(TAG, "No upgrade packages found")
                return null
            }
            
            // 创建升级任务
            val taskId = generateTaskId()
            val upgradeTask = UpgradeTaskInfo(
                taskId = taskId,
                taskName = upgradeConfig?.taskName ?: "USB_Upgrade_${System.currentTimeMillis()}",
                taskVersion = upgradeConfig?.version ?: "1.0.0",
                createTime = System.currentTimeMillis(),
                usbPath = usbDir.absolutePath,
                targetPath = getTargetPath(),
                packageList = packageList.toMutableList(),
                totalSize = packageList.sumOf { it.packageSize },
                taskStatus = UpgradeTaskStatus.PENDING,
                description = upgradeConfig?.description ?: "Auto-detected upgrade task from USB"
            )
            
            LogUtils.i(TAG, "Created upgrade task: $taskId with ${packageList.size} packages")
            return upgradeTask
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error analyzing upgrade packages: ${e.message}")
            return null
        }
    }
    
    /**
     * 解析升级配置文件
     */
    private fun parseUpgradeConfig(configFile: File): UpgradeConfig? {
        return try {
            val configJson = configFile.readText()
            gson.fromJson(configJson, UpgradeConfig::class.java)
        } catch (e: JsonSyntaxException) {
            LogUtils.e(TAG, "Invalid config file format: ${e.message}")
            null
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error reading config file: ${e.message}")
            null
        }
    }
    
    /**
     * 扫描升级包文件
     */
    private fun scanUpgradePackages(dir: File, config: UpgradeConfig?): List<UpgradePackageInfo> {
        val packageList = mutableListOf<UpgradePackageInfo>()
        
        dir.listFiles()?.forEach { file ->
            if (file.isFile && isUpgradePackage(file)) {
                val packageInfo = analyzePackageFile(file, config)
                if (packageInfo != null) {
                    packageList.add(packageInfo)
                    LogUtils.d(TAG, "Found package: ${packageInfo.packageName}")
                }
            } else if (file.isDirectory) {
                // 递归扫描子目录
                packageList.addAll(scanUpgradePackages(file, config))
            }
        }
        
        return packageList
    }
    
    /**
     * 判断是否为升级包文件
     */
    private fun isUpgradePackage(file: File): Boolean {
        val extension = file.extension.lowercase()
        return supportedExtensions.contains(extension) && file.name != configFileName
    }
    
    /**
     * 分析单个包文件
     */
    private fun analyzePackageFile(file: File, config: UpgradeConfig?): UpgradePackageInfo? {
        return try {
            val fileName = file.name
            val fileSize = file.length()
            val checksum = calculateChecksum(file)
            
            // 从配置文件中查找包信息
            val configPackage = config?.packages?.find { it.fileName == fileName }
            
            UpgradePackageInfo(
                packageName = configPackage?.name ?: fileName,
                packageSize = fileSize,
                packagePath = file.absolutePath,
                packageVersion = configPackage?.version ?: extractVersionFromFileName(fileName),
                targetEcu = configPackage?.targetEcu ?: guessTargetEcu(fileName),
                checksum = checksum,
                packageType = configPackage?.type ?: guessPackageType(file.extension)
            )
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error analyzing package file ${file.name}: ${e.message}")
            null
        }
    }
    
    /**
     * 计算文件校验和
     */
    fun calculateChecksum(file: File): String {
        return try {
            val md5 = MessageDigest.getInstance("MD5")
            FileInputStream(file).use { fis ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (fis.read(buffer).also { bytesRead = it } != -1) {
                    md5.update(buffer, 0, bytesRead)
                }
            }
            md5.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            LogUtils.w(TAG, "Failed to calculate checksum for ${file.name}: ${e.message}")
            ""
        }
    }
    
    /**
     * 从文件名提取版本号
     */
    private fun extractVersionFromFileName(fileName: String): String {
        val versionRegex = Regex("""v?(\d+\.\d+(?:\.\d+)?)""")
        val match = versionRegex.find(fileName)
        return match?.groupValues?.get(1) ?: "1.0.0"
    }
    
    /**
     * 根据文件名猜测目标ECU
     */
    private fun guessTargetEcu(fileName: String): String {
        val lowerName = fileName.lowercase()
        return when {
            lowerName.contains("hpcm") || lowerName.contains("mcu") -> "HPCM"
            lowerName.contains("zcuf") -> "ZCUF"
            lowerName.contains("zcufr") -> "ZCUFR"
            lowerName.contains("zcur") -> "ZCUR"
            lowerName.contains("bcm") -> "BCM"
            lowerName.contains("tbox") -> "TBOX"
            else -> "UNKNOWN"
        }
    }
    
    /**
     * 根据文件扩展名猜测包类型
     */
    private fun guessPackageType(extension: String): String {
        return when (extension.lowercase()) {
            "bin", "hex", "img" -> "firmware"
            "apk" -> "application"
            "so" -> "library"
            "conf", "json", "xml" -> "config"
            "tar", "zip" -> "archive"
            else -> "unknown"
        }
    }
    
    /**
     * 生成任务ID
     */
    private fun generateTaskId(): String {
        return "UPGRADE_${System.currentTimeMillis()}_${UUID.randomUUID().toString().substring(0, 8)}"
    }
    
    /**
     * 获取目标路径
     */
    private fun getTargetPath(): String {
        return "/data/upgrade" // 可配置的目标路径
    }
}

/**
 * 升级配置数据类
 */
data class UpgradeConfig(
    val taskName: String = "",
    val version: String = "1.0.0",
    val description: String = "",
    val packages: List<PackageConfig> = emptyList()
)

/**
 * 包配置数据类
 */
data class PackageConfig(
    val fileName: String = "",
    val name: String = "",
    val version: String = "",
    val targetEcu: String = "",
    val type: String = "",
    val required: Boolean = true
)

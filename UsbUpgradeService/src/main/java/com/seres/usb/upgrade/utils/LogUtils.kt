package com.seres.usb.upgrade.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志工具类
 */
object LogUtils {
    
    private const val TAG_PREFIX = "UsbUpgrade"
    private var isDebugMode = true
    private var logToFile = true
    private var logFile: File? = null
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    /**
     * 初始化日志系统
     */
    fun init(context: Context) {
        try {
            // 创建日志文件
            val logDir = File(context.getExternalFilesDir(null), "logs")
            if (!logDir.exists()) {
                logDir.mkdirs()
            }
            
            val logFileName = "usb_upgrade_${SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Date())}.log"
            logFile = File(logDir, logFileName)
            
            i("LogUtils", "Log system initialized, log file: ${logFile?.absolutePath}")
        } catch (e: Exception) {
            Log.e(TAG_PREFIX, "Failed to initialize log system: ${e.message}")
        }
    }
    
    /**
     * Debug日志
     */
    fun d(tag: String, message: String) {
        val fullTag = "$TAG_PREFIX-$tag"
        if (isDebugMode) {
            Log.d(fullTag, message)
        }
        writeToFile("D", tag, message)
    }
    
    /**
     * Info日志
     */
    fun i(tag: String, message: String) {
        val fullTag = "$TAG_PREFIX-$tag"
        Log.i(fullTag, message)
        writeToFile("I", tag, message)
    }
    
    /**
     * Warning日志
     */
    fun w(tag: String, message: String) {
        val fullTag = "$TAG_PREFIX-$tag"
        Log.w(fullTag, message)
        writeToFile("W", tag, message)
    }
    
    /**
     * Error日志
     */
    fun e(tag: String, message: String) {
        val fullTag = "$TAG_PREFIX-$tag"
        Log.e(fullTag, message)
        writeToFile("E", tag, message)
    }
    
    /**
     * Error日志（带异常）
     */
    fun e(tag: String, message: String, throwable: Throwable) {
        val fullTag = "$TAG_PREFIX-$tag"
        Log.e(fullTag, message, throwable)
        writeToFile("E", tag, "$message\n${Log.getStackTraceString(throwable)}")
    }
    
    /**
     * 写入日志文件
     */
    private fun writeToFile(level: String, tag: String, message: String) {
        if (!logToFile || logFile == null) return
        
        try {
            val timestamp = dateFormat.format(Date())
            val logEntry = "$timestamp $level/$TAG_PREFIX-$tag: $message\n"
            
            FileWriter(logFile, true).use { writer ->
                writer.append(logEntry)
            }
        } catch (e: Exception) {
            // 避免日志写入失败导致的循环错误
        }
    }
    
    /**
     * 设置调试模式
     */
    fun setDebugMode(debug: Boolean) {
        isDebugMode = debug
    }
    
    /**
     * 设置文件日志
     */
    fun setLogToFile(enabled: Boolean) {
        logToFile = enabled
    }
    
    /**
     * 获取日志文件路径
     */
    fun getLogFilePath(): String? {
        return logFile?.absolutePath
    }
    
    /**
     * 清理旧日志文件
     */
    fun cleanOldLogs(context: Context, daysToKeep: Int = 7) {
        try {
            val logDir = File(context.getExternalFilesDir(null), "logs")
            if (!logDir.exists()) return
            
            val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
            
            logDir.listFiles()?.forEach { file ->
                if (file.isFile && file.lastModified() < cutoffTime) {
                    if (file.delete()) {
                        i("LogUtils", "Deleted old log file: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            e("LogUtils", "Error cleaning old logs: ${e.message}")
        }
    }
}

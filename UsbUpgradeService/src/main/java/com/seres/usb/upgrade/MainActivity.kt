package com.seres.usb.upgrade

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.seres.usb.upgrade.adapter.StorageDeviceAdapter
import com.seres.usb.upgrade.databinding.ActivityMainBinding
import com.seres.usb.upgrade.storage.StorageStatusManager
import com.seres.usb.upgrade.utils.LogUtils

/**
 * USB升级服务主界面
 * 显示存储设备状态和升级任务信息
 */
class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var storageStatusManager: StorageStatusManager
    private lateinit var storageDeviceAdapter: StorageDeviceAdapter
    
    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            initStorageManager()
        } else {
            Toast.makeText(this, "需要存储权限才能正常工作", Toast.LENGTH_LONG).show()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        LogUtils.d(TAG, "MainActivity onCreate")
        
        setupUI()
        checkPermissions()
    }
    
    /**
     * 设置UI界面
     */
    private fun setupUI() {
        // 设置RecyclerView
        storageDeviceAdapter = StorageDeviceAdapter(this)
        binding.recyclerViewStorageDevices.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = storageDeviceAdapter
        }
        
        // 设置刷新按钮
        binding.btnRefreshStorage.setOnClickListener {
            refreshStorageDevices()
        }
    }
    
    /**
     * 检查权限
     */
    private fun checkPermissions() {
        val permissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.MANAGE_EXTERNAL_STORAGE
        )
        
        val needRequestPermissions = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }
        
        if (needRequestPermissions.isNotEmpty()) {
            permissionLauncher.launch(needRequestPermissions.toTypedArray())
        } else {
            initStorageManager()
        }
    }
    
    /**
     * 初始化存储管理器
     */
    private fun initStorageManager() {
        storageStatusManager = StorageStatusManager(this)
        refreshStorageDevices()
    }
    
    /**
     * 刷新存储设备列表
     */
    private fun refreshStorageDevices() {
        if (::storageStatusManager.isInitialized) {
            val devices = storageStatusManager.getAllStorageDevices()
            storageDeviceAdapter.updateDevices(devices)
            
            binding.tvStorageStatus.text = "已检测到 ${devices.size} 个存储设备"
            LogUtils.d(TAG, "刷新存储设备列表，共 ${devices.size} 个设备")
        }
    }
    
    override fun onResume() {
        super.onResume()
        refreshStorageDevices()
    }
    
    companion object {
        private const val TAG = "MainActivity"
    }
}

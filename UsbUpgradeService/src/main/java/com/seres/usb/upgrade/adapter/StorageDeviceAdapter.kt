package com.seres.usb.upgrade.adapter

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.RecyclerView
import com.seres.usb.upgrade.R
import com.seres.usb.upgrade.storage.StorageStatusManager
import com.seres.usb.upgrade.utils.LogUtils
import java.io.File

/**
 * 存储设备列表适配器
 */
class StorageDeviceAdapter(
    private val context: Context,
    private var devices: List<StorageStatusManager.StorageDeviceInfo> = emptyList()
) : RecyclerView.Adapter<StorageDeviceAdapter.StorageDeviceViewHolder>() {
    
    private val TAG = "StorageDeviceAdapter"
    
    class StorageDeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvDeviceName: TextView = itemView.findViewById(R.id.tvDeviceName)
        val tvDevicePath: TextView = itemView.findViewById(R.id.tvDevicePath)
        val tvDeviceStatus: TextView = itemView.findViewById(R.id.tvDeviceStatus)
        val tvStorageInfo: TextView = itemView.findViewById(R.id.tvStorageInfo)
        val progressBarUsage: ProgressBar = itemView.findViewById(R.id.progressBarUsage)
        val tvUsagePercentage: TextView = itemView.findViewById(R.id.tvUsagePercentage)
        val btnOpenDevice: Button = itemView.findViewById(R.id.btnOpenDevice)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StorageDeviceViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_storage_device, parent, false)
        return StorageDeviceViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: StorageDeviceViewHolder, position: Int) {
        val device = devices[position]
        
        // 设备名称和类型
        holder.tvDeviceName.text = device.description
        
        // 设备路径
        holder.tvDevicePath.text = device.path
        
        // 设备状态
        val statusText = when (device.state) {
            "mounted" -> "已挂载"
            "unmounted" -> "未挂载"
            "checking" -> "检查中"
            "removed" -> "已移除"
            else -> device.state
        }
        val statusColor = when (device.state) {
            "mounted" -> android.R.color.holo_green_dark
            "unmounted", "removed" -> android.R.color.holo_red_dark
            else -> android.R.color.holo_orange_dark
        }
        holder.tvDeviceStatus.text = statusText
        holder.tvDeviceStatus.setTextColor(context.getColor(statusColor))
        
        // 存储信息
        val storageManager = StorageStatusManager(context)
        val totalSpaceStr = storageManager.formatStorageSize(device.totalSpace)
        val freeSpaceStr = storageManager.formatStorageSize(device.freeSpace)
        val usedSpaceStr = storageManager.formatStorageSize(device.usedSpace)
        
        holder.tvStorageInfo.text = "总容量: $totalSpaceStr | 已用: $usedSpaceStr | 可用: $freeSpaceStr"
        
        // 使用率进度条
        holder.progressBarUsage.progress = device.usagePercentage
        holder.tvUsagePercentage.text = "${device.usagePercentage}%"
        
        // 进度条颜色根据使用率变化
        val progressColor = when {
            device.usagePercentage < 70 -> android.R.color.holo_green_light
            device.usagePercentage < 90 -> android.R.color.holo_orange_light
            else -> android.R.color.holo_red_light
        }
        holder.progressBarUsage.progressTintList = context.getColorStateList(progressColor)
        
        // 打开设备按钮
        val canOpen = device.state == "mounted" && StorageStatusManager(context).isPathAccessible(device.path)
        holder.btnOpenDevice.isEnabled = canOpen
        holder.btnOpenDevice.text = if (device.isRemovable) "打开U盘" else "打开存储"
        
        if (canOpen) {
            holder.btnOpenDevice.setOnClickListener {
                openStorageDevice(device)
            }
        } else {
            holder.btnOpenDevice.setOnClickListener(null)
        }
        
        // 为可移动设备添加特殊标识
        if (device.isRemovable) {
            holder.tvDeviceName.text = "${device.description} 📱"
        }
    }
    
    override fun getItemCount(): Int = devices.size
    
    /**
     * 更新设备列表
     */
    fun updateDevices(newDevices: List<StorageStatusManager.StorageDeviceInfo>) {
        devices = newDevices
        notifyDataSetChanged()
        LogUtils.d(TAG, "Updated storage devices list: ${devices.size} devices")
    }
    
    /**
     * 打开存储设备
     */
    private fun openStorageDevice(device: StorageStatusManager.StorageDeviceInfo) {
        try {
            LogUtils.i(TAG, "Opening storage device: ${device.path}")
            
            val file = File(device.path)
            if (!file.exists() || !file.canRead()) {
                LogUtils.w(TAG, "Cannot access device path: ${device.path}")
                return
            }
            
            // 尝试使用文件管理器打开
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(getUriForFile(file), "resource/folder")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            // 检查是否有应用可以处理这个Intent
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                LogUtils.i(TAG, "Opened storage device with file manager")
            } else {
                // 如果没有文件管理器，尝试使用系统的文档选择器
                openWithDocumentPicker(device)
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error opening storage device: ${e.message}")
            // 尝试备用方法
            openWithAlternativeMethod(device)
        }
    }
    
    /**
     * 使用文档选择器打开
     */
    private fun openWithDocumentPicker(device: StorageStatusManager.StorageDeviceInfo) {
        try {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                LogUtils.i(TAG, "Opened with document picker")
            } else {
                LogUtils.w(TAG, "No document picker available")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error opening with document picker: ${e.message}")
        }
    }
    
    /**
     * 使用备用方法打开
     */
    private fun openWithAlternativeMethod(device: StorageStatusManager.StorageDeviceInfo) {
        try {
            // 尝试使用通用的ACTION_VIEW
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("file://${device.path}")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            context.startActivity(intent)
            LogUtils.i(TAG, "Opened with alternative method")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "All methods failed to open storage device: ${e.message}")
        }
    }
    
    /**
     * 获取文件的URI
     */
    private fun getUriForFile(file: File): Uri {
        return try {
            // 尝试使用FileProvider
            FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
        } catch (e: Exception) {
            // 如果FileProvider失败，使用file URI
            Uri.fromFile(file)
        }
    }
}

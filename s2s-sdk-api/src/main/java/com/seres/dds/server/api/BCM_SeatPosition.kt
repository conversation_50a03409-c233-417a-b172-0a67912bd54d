package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class BCM_SeatPosition(var array: Array<SeatInstancePosition>) : Parcelable {

    // 惰性初始化属性，可以在需要的时候使用
    constructor(parcel: Parcel) : this(parcel.createTypedArray(SeatInstancePosition.CREATOR)!!) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedArray(array, flags)
    }

    override fun describeContents(): Int {
        return 0
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as BCM_SeatPosition
        if (!array.contentEquals(other.array)) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        return array.contentHashCode()
    }

    companion object CREATOR : Parcelable.Creator<BCM_SeatPosition> {
        override fun createFromParcel(parcel: Parcel): BCM_SeatPosition {
            return BCM_SeatPosition(parcel)
        }

        override fun newArray(size: Int): Array<BCM_SeatPosition?> {
            return arrayOfNulls(size)
        }
    }
}
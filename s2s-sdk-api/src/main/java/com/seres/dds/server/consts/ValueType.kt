import kotlin.reflect.KClass

/**
 *@BelongsProject: CarPropHub
 *@BelongsPackage: com.seres.dds.server.consts
 *@Author: ke.dong(416718)
 *@Email: <EMAIL>
 *@CreateTime: 2025-01-08 09:37
 *@Description: s2s上报参数的类型枚举
 */
enum class ValueType(val clz: KClass<*>) {
    UNIT(UNIT::class),
    CHAR(Char::class),
    BYTE(Byte::class),
    SHORT(Short::class),
    INT(Int::class),
    LONG(Long::class),
    FLOAT(Float::class),
    DOUBLE(Double::class),
    BOOLEAN(Boolean::class),
    STRING(String::class);
    
    companion object {
        fun fromValue(typeName: String): ValueType? {
            return ValueType.values().find { it.name == typeName }
        }

        fun get(typeName: String): KClass<*>? {
            return fromValue(typeName)?.clz
        }
    }
}
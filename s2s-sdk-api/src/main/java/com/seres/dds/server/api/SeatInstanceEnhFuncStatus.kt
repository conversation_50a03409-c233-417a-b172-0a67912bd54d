package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

typealias BCM_VentilationStatus = OnOffSts
typealias BCM_HeatStatus = OnOffSts
typealias BCM_SeatMassagFuncStatus = BCM_SeatMassagStrengthStatus
data class SeatInstanceEnhFuncStatus(
    var bcm_seatId: BCM_SeatId,
    var bcm_VentilationStatus: BCM_VentilationStatus,
    var bcm_HeatStatus: BCM_HeatStatus,
    var bcm_MassagFuncStatus: BCM_SeatMassagFuncStatus,
    var bcm_SeatFoldFuncStatus: BCM_SeatFoldFuncStatus,
    var bcm_SeatOnOffStatus: OnOffSts,
    var bcm_SeatChildLockOnOffStatus: OnOffSts,
    var bcm_SeatLinkageStatus: EnableDisAble,
    var bcm_SeatCrashStatus: BCM_SeatCrashStatus,
    var bcm_SeatOccupiedStatus: BCM_SeatOccupiedStatus,
    var bcm_SeatControlStatus: BCM_SeatControlStatus,
    var bcm_SeatAntiPinchStatus: EnableDisAble,
    var bcm_SeatHeatProtectionStatus: EnableDisAble,
    var bcm_SeatForbiddenCause: BCM_SeatForbiddenCause,
    var bcm_SeatWarningStatus: BCM_SeatWarningStatus,
    var bcm_SeatInitSts: BCM_InitSts
) : Parcelable {

    constructor(parcel: Parcel) : this(
        BCM_SeatId.fromValue(parcel.readInt()),
        BCM_VentilationStatus.fromValue(parcel.readInt()),
        BCM_HeatStatus.fromValue(parcel.readInt()),
        BCM_SeatMassagFuncStatus.fromValue(parcel.readInt()),
        BCM_SeatFoldFuncStatus.fromValue(parcel.readInt()),
        OnOffSts.fromValue(parcel.readInt()),
        OnOffSts.fromValue(parcel.readInt()),
        EnableDisAble.fromValue(parcel.readInt()),
        BCM_SeatCrashStatus.fromValue(parcel.readInt()),
        BCM_SeatOccupiedStatus.fromValue(parcel.readInt()),
        BCM_SeatControlStatus.fromValue(parcel.readInt()),
        EnableDisAble.fromValue(parcel.readInt()),
        EnableDisAble.fromValue(parcel.readInt()),
        BCM_SeatForbiddenCause.fromValue(parcel.readInt()),
        BCM_SeatWarningStatus.fromValue(parcel.readInt()),
        BCM_InitSts.fromValue(parcel.readInt())
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {

        parcel.writeInt(bcm_seatId.value)
        parcel.writeInt(bcm_VentilationStatus.value)
        parcel.writeInt(bcm_HeatStatus.value)
        parcel.writeInt(bcm_MassagFuncStatus.value)
        parcel.writeInt(bcm_SeatFoldFuncStatus.value)
        parcel.writeInt(bcm_SeatOnOffStatus.value)
        parcel.writeInt(bcm_SeatChildLockOnOffStatus.value)
        parcel.writeInt(bcm_SeatLinkageStatus.value)
        parcel.writeInt(bcm_SeatCrashStatus.value)
        parcel.writeInt(bcm_SeatOccupiedStatus.value)
        parcel.writeInt(bcm_SeatControlStatus.value)
        parcel.writeInt(bcm_SeatAntiPinchStatus.value)
        parcel.writeInt(bcm_SeatHeatProtectionStatus.value)
        parcel.writeInt(bcm_SeatForbiddenCause.value)
        parcel.writeInt(bcm_SeatWarningStatus.value)
        parcel.writeInt(bcm_SeatInitSts.value)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SeatInstanceEnhFuncStatus> {
        override fun createFromParcel(parcel: Parcel): SeatInstanceEnhFuncStatus {
            return SeatInstanceEnhFuncStatus(parcel)
        }

        override fun newArray(size: Int): Array<SeatInstanceEnhFuncStatus?> {
            return arrayOfNulls(size)
        }
    }

}

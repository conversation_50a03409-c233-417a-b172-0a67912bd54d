package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

class BCM_SeatPositonByStep(var array: Array<SeatInstancePositionByStep> ) : Parcelable {

    lateinit var stuct: Array<SeatInstancePositionByStep>

    constructor(parcel: Parcel) : this(parcel.createTypedArray(SeatInstancePositionByStep.CREATOR)!!) {
        stuct = array
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeTypedArray(array, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<BCM_SeatPositonByStep> {
        override fun createFromParcel(parcel: Parcel): BCM_SeatPositonByStep {
            return BCM_SeatPositonByStep(parcel)
        }

        override fun newArray(size: Int): Array<BCM_SeatPositonByStep?> {
            return arrayOfNulls(size)
        }
    }
}
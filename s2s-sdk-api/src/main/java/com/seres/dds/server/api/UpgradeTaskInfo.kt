package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable
import java.io.Serializable

/**
 * 升级任务信息数据类
 */
data class UpgradeTaskInfo(
    var taskId: String = "",
    var taskName: String = "",
    var taskVersion: String = "",
    var createTime: Long = System.currentTimeMillis(),
    var usbPath: String = "",
    var targetPath: String = "",
    var packageList: MutableList<UpgradePackageInfo> = mutableListOf(),
    var totalSize: Long = 0L,
    var taskStatus: UpgradeTaskStatus = UpgradeTaskStatus.PENDING,
    var description: String = ""
) : Parcelable, Serializable {
    
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readLong(),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.createTypedArrayList(UpgradePackageInfo.CREATOR) ?: mutableListOf(),
        parcel.readLong(),
        UpgradeTaskStatus.valueOf(parcel.readString() ?: "PENDING"),
        parcel.readString() ?: ""
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(taskId)
        parcel.writeString(taskName)
        parcel.writeString(taskVersion)
        parcel.writeLong(createTime)
        parcel.writeString(usbPath)
        parcel.writeString(targetPath)
        parcel.writeTypedList(packageList)
        parcel.writeLong(totalSize)
        parcel.writeString(taskStatus.name)
        parcel.writeString(description)
    }

    override fun describeContents(): Int = 0

    companion object CREATOR : Parcelable.Creator<UpgradeTaskInfo> {
        override fun createFromParcel(parcel: Parcel): UpgradeTaskInfo {
            return UpgradeTaskInfo(parcel)
        }

        override fun newArray(size: Int): Array<UpgradeTaskInfo?> {
            return arrayOfNulls(size)
        }
    }
}

/**
 * 升级任务状态枚举
 */
enum class UpgradeTaskStatus {
    PENDING,        // 待处理
    COPYING,        // 拷贝中
    READY,          // 准备就绪
    DISTRIBUTING,   // 分发中
    COMPLETED,      // 已完成
    FAILED,         // 失败
    CANCELLED       // 已取消
}

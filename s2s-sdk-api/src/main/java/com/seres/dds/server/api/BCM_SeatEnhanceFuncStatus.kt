package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class BCM_SeatEnhanceFuncStatus(var array: Array<SeatInstanceEnhFuncStatus>) : Parcelable {

    // 惰性初始化属性，可以在需要的时候使用
    lateinit var stuct: Array<SeatInstanceEnhFuncStatus>

    constructor(parcel: Parcel) : this(parcel.createTypedArray(SeatInstanceEnhFuncStatus.CREATOR)!!) {
        stuct = array
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedArray(array, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<BCM_SeatEnhanceFuncStatus> {
        override fun createFromParcel(parcel: Parcel): BCM_SeatEnhanceFuncStatus {
            return BCM_SeatEnhanceFuncStatus(parcel)
        }

        override fun newArray(size: Int): Array<BCM_SeatEnhanceFuncStatus?> {
            return arrayOfNulls(size)
        }
    }
}
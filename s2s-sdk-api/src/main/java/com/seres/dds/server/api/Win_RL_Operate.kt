package com.seres.dds.server.api


enum class Win_RL_Operate(val value: Int) {
    No_Action_Stop(0x0),
    MANUAL_UP(0x1),
    MANUAL_DOWN(0x2),
    AUTO_UP(0x3),
    AUTO_DOWN(0x4),
    adjustPosition_0(0x5),
    adjustPosition_1(0x6),
    adjustPosition_2(0x7),
    adjustPosition_3(0x8),
    adjustPosition_4(0x9),
    adjustPosition_5(0xA),
    adjustPosition_6(0xB),
    adjustPosition_7(0xC),
    adjustPosition_8(0xD),
    adjustPosition_9(0xE),
    adjustPosition_10(0xF),
    adjustPosition_11(0x10),
    adjustPosition_12(0x11),
    adjustPosition_13(0x12),
    adjustPosition_14(0x13),
    adjustPosition_15(0x14),
    adjustPosition_16(0x15),
    adjustPosition_17(0x16),
    adjustPosition_18(0x17),
    adjustPosition_19(0x18),
    adjustPosition_20(0x19),
    adjustPosition_21(0x1A),
    adjustPosition_22(0x1B),
    adjustPosition_23(0x1C),
    adjustPosition_24(0x1D),
    adjustPosition_25(0x1E),
    adjustPosition_26(0x1F),
    adjustPosition_27(0x20),
    adjustPosition_28(0x21),
    adjustPosition_29(0x22),
    adjustPosition_30(0x23),
    adjustPosition_31(0x24),
    adjustPosition_32(0x25),
    adjustPosition_33(0x26),
    adjustPosition_34(0x27),
    adjustPosition_35(0x28),
    adjustPosition_36(0x29),
    adjustPosition_37(0x2A),
    adjustPosition_38(0x2B),
    adjustPosition_39(0x2C),
    adjustPosition_40(0x2D),
    adjustPosition_41(0x2E),
    adjustPosition_42(0x2F),
    adjustPosition_43(0x30),
    adjustPosition_44(0x31),
    adjustPosition_45(0x32),
    adjustPosition_46(0x33),
    adjustPosition_47(0x34),
    adjustPosition_48(0x35),
    adjustPosition_49(0x36),
    adjustPosition_50(0x37),
    adjustPosition_51(0x38),
    adjustPosition_52(0x39),
    adjustPosition_53(0x3A),
    adjustPosition_54(0x3B),
    adjustPosition_55(0x3C),
    adjustPosition_56(0x3D),
    adjustPosition_57(0x3E),
    adjustPosition_58(0x3F),
    adjustPosition_59(0x40),
    adjustPosition_60(0x41),
    adjustPosition_61(0x42),
    adjustPosition_62(0x43),
    adjustPosition_63(0x44),
    adjustPosition_64(0x45),
    adjustPosition_65(0x46),
    adjustPosition_66(0x47),
    adjustPosition_67(0x48),
    adjustPosition_68(0x49),
    adjustPosition_69(0x4A),
    adjustPosition_70(0x4B),
    adjustPosition_71(0x4C),
    adjustPosition_72(0x4D),
    adjustPosition_73(0x4E),
    adjustPosition_74(0x4F),
    adjustPosition_75(0x50),
    adjustPosition_76(0x51),
    adjustPosition_77(0x52),
    adjustPosition_78(0x53),
    adjustPosition_79(0x54),
    adjustPosition_80(0x55),
    adjustPosition_81(0x56),
    adjustPosition_82(0x57),
    adjustPosition_83(0x58),
    adjustPosition_84(0x59),
    adjustPosition_85(0x5A),
    adjustPosition_86(0x5B),
    adjustPosition_87(0x5C),
    adjustPosition_88(0x5D),
    adjustPosition_89(0x5E),
    adjustPosition_90(0x5F),
    adjustPosition_91(0x60),
    adjustPosition_92(0x61),
    adjustPosition_93(0x62),
    adjustPosition_94(0x63),
    adjustPosition_95(0x64),
    adjustPosition_96(0x65),
    adjustPosition_97(0x66),
    adjustPosition_98(0x67),
    adjustPosition_99(0x68),
    adjustPosition_100(0x69);
    //  Reserved                 ( 0x6A~0xFF )         ;

    companion object {
        fun fromValue(value: Int): Win_RL_Operate {
            return Win_RL_Operate.values().first { it.value == value }
        }
    }
}
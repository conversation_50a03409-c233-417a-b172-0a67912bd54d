package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class SeatInstancePositionByStep(
    val bcm_seatId: BCM_SeatId,
    var seatXFrontBackAdjust: SeatFrontBackAdjustByStep,
    var seatXBackRestAngleAdjust: SeatFrontBackAdjustByStep,
    var seatZDirAdjust: SeatUpDownAdjustByStep,
    var seatFrontZDirAdjust: SeatUpDownAdjustByStep,
    var seatLegRestAngleAdjust: SeatUpDownAdjustByStep,
    var seatLegRestXDirAdjust: SeatFrontBackAdjustByStep,
    var seatFrontXDirAdjust: SeatFrontBackAdjustByStep,
    var seatFootRestAngleAdjust: SeatUpDownAdjustByStep,
    var seatMainYDirAdjust: SeatFrontBackAdjustByStep
) : Parcelable {
    constructor(parcel: Parcel) : this(
        BCM_SeatId.fromValue(parcel.readInt()),
        SeatFrontBackAdjustByStep.fromValue(parcel.readInt()),
        SeatFrontBackAdjustByStep.fromValue(parcel.readInt()),
        SeatUpDownAdjustByStep.fromValue(parcel.readInt()),
        SeatUpDownAdjustByStep.fromValue(parcel.readInt()),
        SeatUpDownAdjustByStep.fromValue(parcel.readInt()),
        SeatFrontBackAdjustByStep.fromValue(parcel.readInt()),
        SeatFrontBackAdjustByStep.fromValue(parcel.readInt()),
        SeatUpDownAdjustByStep.fromValue(parcel.readInt()),
        SeatFrontBackAdjustByStep.fromValue(parcel.readInt())
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(bcm_seatId.value)
        parcel.writeInt(seatXFrontBackAdjust.value)
        parcel.writeInt(seatXBackRestAngleAdjust.value)
        parcel.writeInt(seatZDirAdjust.value)
        parcel.writeInt(seatFrontZDirAdjust.value)
        parcel.writeInt(seatLegRestAngleAdjust.value)
        parcel.writeInt(seatLegRestXDirAdjust.value)
        parcel.writeInt(seatFrontXDirAdjust.value)
        parcel.writeInt(seatFootRestAngleAdjust.value)
        parcel.writeInt(seatMainYDirAdjust.value)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SeatInstancePositionByStep> {
        override fun createFromParcel(parcel: Parcel): SeatInstancePositionByStep {
            return SeatInstancePositionByStep(parcel)
        }

        override fun newArray(size: Int): Array<SeatInstancePositionByStep?> {
            return arrayOfNulls(size)
        }
    }
}
package com.seres.dds.server.consts;

/**
 * @BelongsProject: CarPropHub
 * @BelongsPackage: com.seres.dds.server.consts
 * @Author: ke.dong(416718)
 * @Email: <EMAIL>
 * @CreateTime: 2024-12-31 19:57
 * @Description: 定义s2s服务提供的方法名和参数key
 */
public interface InvokeConsts {
    String KEY_METHOD_ID = "method_name";
    String KEY_RESULT = "invoke_result";
    String KEY_PACKAGE_NAME = "package_name";
    String KEY_REPORT_RESULT_TYPE = "report_type";
    String TYPE_REPORT_RESULT_JSON = "report_type_json";
    String TYPE_REPORT_RESULT_PROP = "report_type_prop";

    String KEY_REPORT_JSON_CONTENT = "report_type_json_content";

    String KEY_NOTIFY_AREA = "notify_area";
    String AREA_F_RESULT = "ZCUF";
    String AREA_R_RESULT = "ZCUR";
    String AREA_FR_RESULT = "ZCUFR";
    String AREA_HPCM_RESULT = "HPCM";

    String KEY_CHANGE_PROP_LIST = "change_prop_list";
    String KEY_CHANGE_PROP = "change_prop";

    String KEY_CHANGE_PROP_VALUE = "change_value";
    String KEY_CHANGE_VALUE_TYPE = "change_value_type";

    String CHANGE_VALUE_TYPE_BYTE = "BYTE";
    String CHANGE_VALUE_TYPE_CHAR = "CHAR";
    String CHANGE_VALUE_TYPE_SHORT = "SHORT";
    String CHANGE_VALUE_TYPE_INT = "INT";
    String CHANGE_VALUE_TYPE_LONG = "LONG";
    String CHANGE_VALUE_TYPE_FLOAT = "FLOAT";
    String CHANGE_VALUE_TYPE_DOUBLE = "DOUBLE";
    String change_value_type_bool = "BOOLEAN";
    String CHANGE_VALUE_TYPE_STRING = "STRING";

    /**
     * 座椅位置调节控制
     */
    interface MethodSeatAdjustPosCtrl {
        String NAME = "BCM_SeatAdjustPositionCtrl";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_SEAT_POSITION = "bcm_SeatPositon";
    }

    /**
     * 座椅位置状态获取
     */
    interface MethodSeatAdjustPositionGetSts {
        String NAME = "BCM_SeatAdjustPositionGetSts";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_SEAT_POSITION = "bcm_SeatPositon";
    }

    /**
     * 座椅位置步进式调节控制
     */
    interface MethodSeatAdjustPositionByStepCtrl {
        String NAME = "BCM_SeatAdjustPositionByStepCtrl";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_SEAT_BY_STEP = "bcm_SeatPositionSteps";
    }

    /**
     * 座椅自学习控制
     */
    interface MethodSeatLearnCtrl {
        String NAME = "BCM_SeatLearnCtrl";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_LEARN_CMD = "learns_cmd";
    }

    /**
     * 座椅恢复出厂设置
     */
    interface MethodSeatFactoryCtrl {
        String NAME = "BCM_SeatFactoryCtrl";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_ENABLE_DISABLE = "enableDisAble";
    }

    /**
     * 座椅增强功能控制(按摩  通风  加热)
     */
    interface MethodSeatEnhanceFuncCtrl {
        String NAME = "BCM_SeatEnhanceFuncCtrl";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_ENHANCE_FUNC = "bcm_SeatEnhanceFunc";
    }

    /**
     * 获取座椅增强功能控制(按摩  通风  加热)状态
     */
    interface MethodSeatEnhanceFuncGetSts {
        String NAME = "BCM_SeatEnhanceFuncGetSts";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_ENHANCE_FUNC_STS = "bcm_SeatEnhanceFuncStatus";
    }

    /**
     * 控制车窗
     */
    interface MethodWindowCtrl {
        String NAME = "BCM_Window_Ctrl";
        String PARAM_ESRV_CALL_ID = "win_eSrv_CallerID";
        String PARAM_FL_OP = "win_FL_Operate";
        String PARAM_FR_OP = "win_FR_Operate";
        String PARAM_RL_OP = "win_RL_Operate";
        String PARAM_RR_OP = "win_RR_Operate";
    }

    /**
     * 获取车窗
     */
    interface MethodGetWindowDatas {
        String NAME = "getWindowDatas";
    }

    /**
     * 设置后备箱灯功能
     */
    interface MethodTrunkLightSet {
        String NAME = "BCM_TrunkLightSet";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_ON_OFF_CMD = "onOffCmd";
    }

    /**
     * 获取后备箱灯状态
     */
    interface MethodTrunkLightGetSts {
        String NAME = "BCM_TrunkLightGetSts";
    }

    /**
     * 设置前备箱灯功能
     */
    interface MethodHoodLightSet {
        String NAME = "BCM_HoodLightSet";
        String PARAM_CALL_ID = "bcm_CallerID";
        String PARAM_ON_OFF_CMD = "onOffCmd";
    }

    /**
     * 获取前备箱灯状态
     */
    interface MethodHoodLightGetSts {
        String NAME = "BCM_HoodLightGetSts";
    }
}

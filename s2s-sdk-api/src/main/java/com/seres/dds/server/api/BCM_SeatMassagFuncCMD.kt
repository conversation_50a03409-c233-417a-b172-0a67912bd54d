package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class BCM_SeatMassagFuncCMD(
    var seatMassagStrengthCMD: BCM_SeatMassagStrengthCMD,
    var seatMassagModeCMD: BCM_SeatMassagModeCMD
) : Parcelable {
    constructor(parcel: Parcel) : this(
        BCM_SeatMassagStrengthCMD.fromValue(parcel.readInt()),
        BCM_SeatMassagModeCMD.fromValue(parcel.readInt())
    )

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(seatMassagStrengthCMD.value)
        parcel.writeInt(seatMassagModeCMD.value)
    }

    companion object CREATOR : Parcelable.Creator<BCM_SeatMassagFuncCMD> {
        override fun createFromParcel(parcel: Parcel): BCM_SeatMassagFuncCMD {
            return BCM_SeatMassagFuncCMD(parcel)
        }

        override fun newArray(size: Int): Array<BCM_SeatMassagFuncCMD?> {
            return arrayOfNulls(size)
        }
    }
}
package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

class BCM_SeatLearnCMD(var array: Array<SeatInstanceLearn>) : Parcelable {

    lateinit var stuct: Array<SeatInstanceLearn>

    constructor(parcel: Parcel) : this(
        parcel.createTypedArray(SeatInstanceLearn.CREATOR)!!
    ){
        stuct = array
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedArray(array, flags)
    }

    companion object CREATOR : Parcelable.Creator<BCM_SeatLearnCMD> {
        override fun createFromParcel(parcel: Parcel): BCM_SeatLearnCMD {
            return BCM_SeatLearnCMD(parcel)
        }

        override fun newArray(size: Int): Array<BCM_SeatLearnCMD?> {
            return arrayOfNulls(size)
        }
    }
}
package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class SeatInstanceEnhFunc(
    var seatId: BCM_SeatId,
    var ventilationCmd: BCM_VentilationCmd,
    var heatCmd: BCM_HeatCmd,
    var massageFuncCmd: BCM_SeatMassagFuncCMD,
    var seatFoldFuncCmd: BCM_SeatFoldFuncCMD,
    var seatOnOffCmd: OnOffCmd,
    var anotherSeatOnOffCmd: OnOffCmd,
    var seatLinkageCmd: BCM_SeatLinkageCMD,
    var seatCrashCmd: BCM_SeatCrashCMD
) : Parcelable {
    constructor(parcel: Parcel) : this(
//        BCM_SeatId.fromValue(parcel.readInt()),
//        BCM_VentilationCmd(parcel), //这里写法是否有问题
//        BCM_HeatCmd(parcel),
//        BCM_SeatMassagFuncCMD(parcel),
//        BCM_SeatFoldFuncCMD.fromValue(parcel.readInt()),
//        OnOffCmd.fromValue(parcel.readInt()),
//        OnOffCmd.fromValue(parcel.readInt()),
//        BCM_SeatLinkageCMD.fromValue(parcel.readInt()),
//        BCM_SeatCrashCMD.fromValue(parcel.readInt()),
        BCM_SeatId.fromValue(parcel.readInt()),
        BCM_VentilationCmd(parcel), //这里写法是否有问题
        BCM_HeatCmd(parcel),
        BCM_SeatMassagFuncCMD(parcel),
        BCM_SeatFoldFuncCMD.fromValue(parcel.readInt()),
        OnOffCmd.fromValue(parcel.readInt()),
        OnOffCmd.fromValue(parcel.readInt()),
        BCM_SeatLinkageCMD.fromValue(parcel.readInt()),
        BCM_SeatCrashCMD.fromValue(parcel.readInt()),
    )

    override fun describeContents(): Int {
        return 0
    }

    //这里需要重点看一下
    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(seatId.value)
        ventilationCmd.writeToParcel(parcel, flags)
        heatCmd.writeToParcel(parcel, flags)
        massageFuncCmd.writeToParcel(parcel, flags)
        parcel.writeInt(seatFoldFuncCmd.value)
        parcel.writeInt(seatOnOffCmd.value)
        parcel.writeInt(anotherSeatOnOffCmd.value)
        parcel.writeInt(seatLinkageCmd.value)
        parcel.writeInt(seatCrashCmd.value)
    }

    companion object CREATOR : Parcelable.Creator<SeatInstanceEnhFunc> {
        override fun createFromParcel(parcel: Parcel): SeatInstanceEnhFunc {
            return SeatInstanceEnhFunc(parcel)
        }

        override fun newArray(size: Int): Array<SeatInstanceEnhFunc?> {
            return arrayOfNulls(size)
        }
    }
}
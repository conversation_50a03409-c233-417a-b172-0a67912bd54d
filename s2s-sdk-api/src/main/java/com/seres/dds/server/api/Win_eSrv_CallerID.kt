package com.seres.dds.server.api


enum class Win_eSrv_CallerID(val value: Int) {
    CDC(0x8001),
    ADS(0x8201),
    TBOX(0x8401),
    SwtManCtrWin_APP(0x88D1),
    BCM_BLEWindowCtrl_Appl(0x88D5),
    BCM_RLSWindowCtrl_Appl(0x88D6),
    BCM_LockWindowCtrl_Appl(0x88D7),
    BCM_ThermalRunaway(0x89F7),
    BCM_DiagMG_Appl(0x89F8);

    companion object {
        fun fromValue(value: Int): Win_eSrv_CallerID {
            return Win_eSrv_CallerID.values().first { it.value == value }
        }
    }
}
package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class SeatInstanceLearn(
    var seatId: BCM_SeatId,
    var enableLearn: EnableDisAble,
    var disableLearn: EnableDisAble
) : Parcelable {

    constructor(parcel: Parcel) : this(
        BCM_SeatId.fromValue(parcel.readInt()),
        EnableDisAble.fromValue(parcel.readInt()),
        EnableDisAble.fromValue(parcel.readInt())
    )

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(seatId.value)
        parcel.writeInt(enableLearn.value)
        parcel.writeInt(disableLearn.value)
    }

    companion object CREATOR : Parcelable.Creator<SeatInstanceLearn> {
        override fun createFromParcel(parcel: Parcel): SeatInstanceLearn {
            return SeatInstanceLearn(parcel)
        }

        override fun newArray(size: Int): Array<SeatInstanceLearn?> {
            return arrayOfNulls(size)
        }
    }
}
package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

typealias HeatLevel = Int //是否需要Int

data class BCM_HeatCmd(
    var onOffCmd: OnOffCmd,
    var heatLevel: HeatLevel
) : Parcelable {
    constructor(parcel: Parcel) : this(
        OnOffCmd.fromValue(parcel.readInt()),
        parcel.readInt()
    )

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(onOffCmd.value)
        parcel.writeInt(heatLevel)
    }

    companion object CREATOR : Parcelable.Creator<BCM_HeatCmd> {
        override fun createFromParcel(parcel: Parcel): BCM_HeatCmd {
            return BCM_HeatCmd(parcel)
        }

        override fun newArray(size: Int): Array<BCM_HeatCmd?> {
            return arrayOfNulls(size)
        }
    }
}
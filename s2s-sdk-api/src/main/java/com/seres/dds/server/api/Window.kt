package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

data class Window( val windowId: Window_Id,
                   val windowPosition: Win_FL_Operate,
                   val windowLockStatus: OnOffSts,) : Parcelable {
    constructor(parcel: Parcel) : this(
        Window_Id.fromValue(parcel.readInt()),
        Win_FL_Operate.fromValue(parcel.readInt()),
        OnOffSts.fromValue(parcel.readInt())
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(windowId.value)
        parcel.writeInt(windowPosition.value)
        parcel.writeInt(windowLockStatus.value)

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Window> {
        override fun createFromParcel(parcel: Parcel): Window {
            return Window(parcel)
        }

        override fun newArray(size: Int): Array<Window?> {
            return arrayOfNulls(size)
        }
    }
}
package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

typealias SeatPositon_Percent = Int

data class SeatInstancePosition(
    var bcm_seatId: BCM_SeatId,
    var mainXDir: SeatPositon_Percent,
    var mainZDir: SeatPositon_Percent,
    var frontZDir: SeatPositon_Percent,
    var backRestAngle: SeatPositon_Percent,
    var legRestAngle: SeatPositon_Percent,
    var legRestXDir: SeatPositon_Percent,
    var footRestAngle: SeatPositon_Percent,
    var frontXDir: SeatPositon_Percent
) : Parcelable {

    constructor(parcel: Parcel) : this(
        BCM_SeatId.fromValue(parcel.readInt()),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(bcm_seatId.value)
        parcel.writeInt(mainXDir)
        parcel.writeInt(mainZDir)
        parcel.writeInt(frontZDir)
        parcel.writeInt(backRestAngle)
        parcel.writeInt(legRestAngle)
        parcel.writeInt(legRestXDir)
        parcel.writeInt(footRestAngle)
        parcel.writeInt(frontXDir)
    }


    fun readFromParcel(parcel: Parcel, flags: Int) {
//        bcm_seatId.value = parcel.readInt()
        bcm_seatId = BCM_SeatId.fromValue(parcel.readInt())
        mainXDir = parcel.readInt()
        mainZDir = parcel.readInt()
        frontZDir = parcel.readInt()
        backRestAngle = parcel.readInt()
        legRestAngle = parcel.readInt()
        legRestXDir = parcel.readInt()
        footRestAngle = parcel.readInt()
        frontXDir = parcel.readInt()

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SeatInstancePosition> {
        override fun createFromParcel(parcel: Parcel): SeatInstancePosition {
            return SeatInstancePosition(parcel)
        }

        override fun newArray(size: Int): Array<SeatInstancePosition?> {
            return arrayOfNulls(size)
        }
    }
}
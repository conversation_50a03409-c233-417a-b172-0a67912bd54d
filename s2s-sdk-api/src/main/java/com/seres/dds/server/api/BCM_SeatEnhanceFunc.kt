package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable

class BCM_SeatEnhanceFunc(var array: Array<SeatInstanceEnhFunc>) : Parcelable {

    lateinit var stuct: Array<SeatInstanceEnhFunc>

    constructor(parcel: Parcel) : this(
        parcel.createTypedArray(SeatInstanceEnhFunc.CREATOR)!!
    ){
        stuct = array
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedArray(array, flags)
    }

    companion object CREATOR : Parcelable.Creator<BCM_SeatEnhanceFunc> {
        override fun createFromParcel(parcel: Parcel): BCM_SeatEnhanceFunc {
            return BCM_SeatEnhanceFunc(parcel)
        }

        override fun newArray(size: Int): Array<BCM_SeatEnhanceFunc?> {
            return arrayOfNulls(size)
        }
    }
}
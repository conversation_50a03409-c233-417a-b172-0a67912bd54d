package com.seres.dds.server.api

enum class BCM_SeatMassagModeCMD(val value: Int) {
    NO_REQUEST(0x00),
    OFF(0x01),
    MODE1(0x02),
    MODE2(0x03),
    <PERSON>ODE3(0x04),
    <PERSON><PERSON><PERSON>4(0x05),
    MODE5(0x06);
    companion object {
        fun fromValue(value: Int): BCM_SeatMassagModeCMD {
            return BCM_SeatMassagModeCMD.values().first { it.value == value }
        }
    }
}
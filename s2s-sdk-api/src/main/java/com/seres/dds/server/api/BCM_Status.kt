package com.seres.dds.server.api

import seres.zcufr.BCM_Window_ActuateStatus
import java.io.Serializable

class BCM_Status : Serializable {
    // hpcm
    private var heatingStatusFL: Boolean = false
    private var heatingLevelFL: Int = 0
    private var ventilatingStatusFL: Boolean = false
    private var ventilatingLevelFL: Int = 0
    private var heatingStatusFR: Boolean = false
    private var ventilatingStatusFR: Boolean = false
    private var heatingLevelFR: Int = 0
    private var ventilatingLevelFR: Int = 0
    private var mainXDirFL: Int = 0
    private var mainXDirFR: Int = 0
    // 主驾座椅调节执行状态
    private var xActuateStatusFL: Int = 0

    // 副驾座椅调节执行状态
    private var xActuateStatusFR: Int = 0

    // zcufr
    private var positionFL: Int = 0
    private var positionFR: Int = 0

    private var actuateStatusFL: BCM_Window_ActuateStatus = BCM_Window_ActuateStatus.IDLE
    private var actuateStatusFR: BCM_Window_ActuateStatus = BCM_Window_ActuateStatus.IDLE

    // zcuf
    private var commonlight_hood: Int = 0

    // zcur
    private var commonlight_trunk_lightstatus: Int = 0


    // hpcm
    fun get_heatingStatusFL(): Boolean {
        return heatingStatusFL
    }

    fun set_heatingStatusFL(value: Boolean) {
        heatingStatusFL = value
    }

    fun get_heatingLevelFL(): Int {
        return heatingLevelFL
    }

    fun set_heatingLevelFL(value: Int) {
        heatingLevelFL = value
    }

    fun get_ventilatingStatusFL(): Boolean {
        return ventilatingStatusFL
    }

    fun set_ventilatingStatusFL(value: Boolean) {
        ventilatingStatusFL = value
    }

    fun get_heatingStatusFR(): Boolean {
        return heatingStatusFR
    }

    fun set_heatingStatusFR(value: Boolean) {
        heatingStatusFR = value
    }

    fun get_ventilatingStatusFR(): Boolean {
        return ventilatingStatusFR
    }

    fun set_ventilatingStatusFR(value: Boolean) {
        ventilatingStatusFR = value
    }

    fun get_heatingLevelFR(): Int {
        return heatingLevelFR
    }

    fun set_heatingLevelFR(value: Int) {
        heatingLevelFR = value
    }

    fun get_mainXDirFL(): Int {
        return mainXDirFL
    }

    fun set_mainXDirFL(value: Int) {
        mainXDirFL = value
    }

    fun get_mainXDirFR(): Int {
        return mainXDirFR
    }

    fun set_mainXDirFR(value: Int) {
        mainXDirFR = value
    }

    fun get_xActuateStatusFL(): Int {
        return xActuateStatusFL
    }

    fun get_xActuateStatusFR(): Int {
        return xActuateStatusFR
    }

    fun set_xActuateStatusFL(value: Int) {
        xActuateStatusFL = value
    }

    fun set_xActuateStatusFR(value: Int) {
        xActuateStatusFR = value
    }
    // zcufr

    fun get_positionFL(): Int {
        return positionFL
    }

    fun set_positionFL(value: Int) {
        positionFL = value
    }

    fun get_positionFR(): Int {
        return positionFR
    }

    fun set_positionFR(value: Int) {
        positionFR = value
    }

    fun set_ventilatingLevelFL(value: Int) {
        ventilatingLevelFL = value
    }

    fun set_ventilatingLevelFR(value: Int) {
        ventilatingLevelFR = value
    }

    fun get_ventilatingLevelFL(): Int {
        return ventilatingLevelFL
    }

    fun get_ventilatingLevelFR(): Int {
        return ventilatingLevelFR
    }

//    fun get_heatingStatusFL(): Boolean{
//        return heatingStatusFL
//    }
//
//    fun set_heatingStatusFL(value: Boolean){
//        heatingStatusFL = value
//    }
//
//    fun get_heatingLevelFL(): Int{
//        return heatingLevelFL
//    }
//
//    fun set_heatingLevelFL(value: Int){
//        heatingLevelFL = value
//    }
//
//    fun get_ventilatingStatusFL(): Boolean{
//        return ventilatingStatusFL
//    }
//
//    fun set_ventilatingStatusFL(value: Boolean){
//        ventilatingStatusFL = value
//    }
//
//    fun get_heatingStatusFR(): Boolean{
//        return heatingStatusFR
//    }
//
//    fun set_heatingStatusFR(value: Boolean){
//        heatingStatusFR = value
//    }
//
//    fun get_ventilatingStatusFR(): Boolean{
//        return ventilatingStatusFR
//    }
//
//    fun set_ventilatingStatusFR(value: Boolean){
//        ventilatingStatusFR = value
//    }
//
//    fun get_heatingLevelFR(): Int{
//        return heatingLevelFR
//    }
//
//    fun set_heatingLevelFR(value: Int){
//        heatingLevelFR = value
//    }

    fun get_actuateStatusFL(): BCM_Window_ActuateStatus {
        return actuateStatusFL
    }

    fun set_actuateStatusFL(value: BCM_Window_ActuateStatus) {
        actuateStatusFL = value
    }

    fun get_actuateStatusFR(): BCM_Window_ActuateStatus {
        return actuateStatusFR
    }

    fun set_actuateStatusFR(value: BCM_Window_ActuateStatus) {
        actuateStatusFR = value
    }

    // zcuf

    fun get_commonlight_hood(): Int {
        return commonlight_hood
    }

    fun set_commonlight_hood(value: Int) {
        commonlight_hood = value
    }

    // zcur
    fun get_commonlight_trunk_lightstatus(): Int {
        return commonlight_trunk_lightstatus
    }

    fun set_commonlight_trunk_lightstatus(value: Int) {
        commonlight_trunk_lightstatus = value
    }
}
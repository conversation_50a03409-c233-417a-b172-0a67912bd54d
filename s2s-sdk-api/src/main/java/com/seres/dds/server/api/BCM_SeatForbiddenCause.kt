package com.seres.dds.server.api

enum class BCM_SeatForbiddenCause(val value: Int) {
    NOREQUEST(0x0),
    SYSTEM_FAILURE(0x1),
    SEAT_FR_OCCUPIED(0x2),
    SEAT_THIRDR_OCCUPIED(0x3),
    SEAT_THIRDL_OCCUPIED(0x4),
    CHILD_LOCK(0x5),
    BUTTON_FORBIDDEN(0x6),
    CHB_DOOR_OPEN(0x7),
    SEAT_RR_OCCUPIED(0x8),
    SEAT_RL_OCCUPIED(0x9),
    StorageBoxOpen(0xA),
    SEAT_THIRDL_NOT_LEARNED(0xB),
    SEAT_THIRDR_NOT_LEARNED(0xC),
    SEAT_RR_UNFOLD(0xD),
    BOSS_BUTTON_ACTIVE_FORBIDDEN(0xE),
    SEAT_FL_OCCUPIED(0xF),
    SEAT_RM_OCCUPIED(0x10),
    MLINK_STAND_ACTIVE(0x11),
    ARMREST_TRAY_TABLE_OPEN(0x12),
    UNKNOW<PERSON>(0xFF);
    companion object {
        fun fromValue(value: Int): BCM_SeatForbiddenCause {
            return BCM_SeatForbiddenCause.values().first { it.value == value }
        }
    }
}





















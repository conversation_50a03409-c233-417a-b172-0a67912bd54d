package seres.zcufr

import java.io.Serializable


enum class BCM_Window_ActuateStatus(var value : Int)  : Serializable {
    IDLE(0),
    CLOSING_BACKWARD_FOLDING_DOWNWARD_DECREASING(1),
    OPENING_FORWARD_UNFOLDING_UPWARD_INCREASING(2),
    STALL(3),
    IDLE_MOTOR_FAULT(4),
    IDLE_ANTIPINCH_RESTRAIN(5),
    IDLE_HEAT_PROTECTION(6),
    UNKNOWN(255);


    companion object {
        private val valueMap = BCM_Window_ActuateStatus.entries.associateBy { it.value }
        fun fromValue(value: Int): BCM_Window_ActuateStatus{
            return  valueMap[value]?:IDLE
        }
    }
}

package com.seres.dds.server.api

enum class BCM_SeatFoldFuncStatus(val value: Int) {
    UNFOLD(0x0),
    FOLD(0x1),
    RETRACT(0x2),
    UNFOLDING(0x3),
    FOLDING(0x4),
    RETRACTING(0x5),
    PAUSE(0x6),
    UNKNOWN(0xFF);
    companion object {
        fun fromValue(value: Int): BCM_SeatFoldFuncStatus {
            return BCM_SeatFoldFuncStatus.values().first { it.value == value }
        }
    }
}